/**
 * 知识库管理服务
 * 实现文档上传、分块、向量化、索引构建等核心功能
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * 文档元数据接口
 */
export interface DocumentMetadata {
  title: string;
  category: string;
  tags: string[];
  author?: string;
  createdAt: Date;
  language: string;
  description?: string;
  source?: string;
  version?: string;
}

/**
 * 文档块接口
 */
export interface DocumentChunk {
  id: string;
  content: string;
  index: number;
  startOffset: number;
  endOffset: number;
  metadata?: Record<string, any>;
  embedding?: number[];
}

/**
 * 文档接口
 */
export interface Document {
  id: string;
  filename: string;
  content: string;
  metadata: DocumentMetadata;
  chunks: DocumentChunk[];
  createdAt: Date;
  updatedAt: Date;
  size: number;
  type: string;
}

/**
 * 分块选项接口
 */
export interface ChunkOptions {
  chunkSize: number;
  overlap: number;
  preserveSentences: boolean;
  preserveParagraphs: boolean;
}

/**
 * 搜索选项接口
 */
export interface SearchOptions {
  topK?: number;
  threshold?: number;
  category?: string;
  tags?: string[];
  language?: string;
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  chunk: DocumentChunk;
  document: Document;
  score: number;
  relevance: number;
}

/**
 * 知识库配置接口
 */
export interface KnowledgeBaseConfig {
  chunkSize: number;
  chunkOverlap: number;
  embeddingModel: string;
  vectorStoreConfig: any;
  embeddingConfig: any;
  maxDocumentSize: number;
  supportedFileTypes: string[];
}

/**
 * 向量存储接口
 */
export interface VectorStore {
  addDocuments(chunks: DocumentChunk[], embeddings: number[][], metadata: any): Promise<void>;
  search(queryEmbedding: number[], options: any): Promise<SearchResult[]>;
  deleteDocument(documentId: string): Promise<void>;
  updateDocument(documentId: string, chunks: DocumentChunk[], embeddings: number[][]): Promise<void>;
  getStats(): Promise<any>;
}

/**
 * 嵌入模型接口
 */
export interface EmbeddingModel {
  embed(text: string): Promise<number[]>;
  embedBatch(texts: string[]): Promise<number[][]>;
  getDimension(): number;
  getModelInfo(): any;
}

/**
 * 简单向量存储实现
 */
export class SimpleVectorStore implements VectorStore {
  private documents: Map<string, { chunks: DocumentChunk[], embeddings: number[][], metadata: any }> = new Map();

  async addDocuments(chunks: DocumentChunk[], embeddings: number[][], metadata: any): Promise<void> {
    this.documents.set(metadata.documentId, { chunks, embeddings, metadata });
  }

  async search(queryEmbedding: number[], options: any): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    
    for (const [documentId, data] of this.documents) {
      for (let i = 0; i < data.chunks.length; i++) {
        const chunk = data.chunks[i];
        const embedding = data.embeddings[i];
        
        // 计算余弦相似度
        const similarity = this.cosineSimilarity(queryEmbedding, embedding);
        
        if (similarity >= (options.threshold || 0.7)) {
          results.push({
            chunk,
            document: data.metadata as Document,
            score: similarity,
            relevance: similarity
          });
        }
      }
    }
    
    // 按相似度排序并返回topK结果
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, options.topK || 5);
  }

  async deleteDocument(documentId: string): Promise<void> {
    this.documents.delete(documentId);
  }

  async updateDocument(documentId: string, chunks: DocumentChunk[], embeddings: number[][]): Promise<void> {
    const existing = this.documents.get(documentId);
    if (existing) {
      existing.chunks = chunks;
      existing.embeddings = embeddings;
    }
  }

  async getStats(): Promise<any> {
    let totalChunks = 0;
    for (const data of this.documents.values()) {
      totalChunks += data.chunks.length;
    }
    
    return {
      documentCount: this.documents.size,
      chunkCount: totalChunks
    };
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }
}

/**
 * 简单嵌入模型实现
 */
export class SimpleEmbeddingModel implements EmbeddingModel {
  private dimension: number = 384;

  async embed(text: string): Promise<number[]> {
    // 简单的文本向量化实现（实际应该使用真实的嵌入模型）
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(this.dimension).fill(0);
    
    // 基于词频的简单向量化
    for (const word of words) {
      const hash = this.simpleHash(word);
      for (let i = 0; i < this.dimension; i++) {
        embedding[i] += Math.sin(hash + i) * 0.1;
      }
    }
    
    // 归一化
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / norm);
  }

  async embedBatch(texts: string[]): Promise<number[][]> {
    const embeddings: number[][] = [];
    for (const text of texts) {
      embeddings.push(await this.embed(text));
    }
    return embeddings;
  }

  getDimension(): number {
    return this.dimension;
  }

  getModelInfo(): any {
    return {
      name: 'SimpleEmbeddingModel',
      dimension: this.dimension,
      version: '1.0.0'
    };
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }
}

/**
 * 知识库管理服务
 */
export class KnowledgeBaseService {
  private documents: Map<string, Document> = new Map();
  private vectorStore: VectorStore;
  private embeddingModel: EmbeddingModel;
  private config: KnowledgeBaseConfig;

  constructor(config: KnowledgeBaseConfig) {
    this.config = config;
    this.vectorStore = new SimpleVectorStore();
    this.embeddingModel = new SimpleEmbeddingModel();
  }

  /**
   * 上传知识文档
   */
  async uploadDocument(
    file: File,
    metadata: DocumentMetadata
  ): Promise<string> {
    const documentId = uuidv4();

    // 检查文件大小
    if (file.size > this.config.maxDocumentSize) {
      throw new Error(`文件大小超过限制: ${this.config.maxDocumentSize} bytes`);
    }

    // 检查文件类型
    if (!this.config.supportedFileTypes.includes(file.type)) {
      throw new Error(`不支持的文件类型: ${file.type}`);
    }

    // 解析文档内容
    const content = await this.parseDocument(file);

    // 文档分块
    const chunks = await this.chunkDocument(content, {
      chunkSize: this.config.chunkSize,
      overlap: this.config.chunkOverlap,
      preserveSentences: true,
      preserveParagraphs: true
    });

    // 生成向量嵌入
    const embeddings = await this.generateEmbeddings(chunks);

    // 创建文档对象
    const document: Document = {
      id: documentId,
      filename: file.name,
      content,
      metadata,
      chunks,
      createdAt: new Date(),
      updatedAt: new Date(),
      size: file.size,
      type: file.type
    };

    // 存储到向量数据库
    await this.vectorStore.addDocuments(chunks, embeddings, document);

    // 保存文档信息
    this.documents.set(documentId, document);

    return documentId;
  }

  /**
   * 解析文档
   */
  private async parseDocument(file: File): Promise<string> {
    const fileType = file.type;

    switch (fileType) {
      case 'text/plain':
        return await file.text();
      case 'application/pdf':
        return await this.parsePDF(file);
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return await this.parseDocx(file);
      case 'text/html':
        return await this.parseHTML(file);
      default:
        // 尝试作为文本文件处理
        return await file.text();
    }
  }

  /**
   * 解析PDF文件
   */
  private async parsePDF(file: File): Promise<string> {
    // 这里应该使用PDF解析库，如pdf-parse
    // 暂时返回占位符
    return `PDF文件内容: ${file.name}`;
  }

  /**
   * 解析Word文档
   */
  private async parseDocx(file: File): Promise<string> {
    // 这里应该使用Word解析库，如mammoth
    // 暂时返回占位符
    return `Word文档内容: ${file.name}`;
  }

  /**
   * 解析HTML文件
   */
  private async parseHTML(file: File): Promise<string> {
    const htmlContent = await file.text();
    // 简单的HTML标签移除
    return htmlContent.replace(/<[^>]*>/g, '').trim();
  }

  /**
   * 文档分块
   */
  private async chunkDocument(
    content: string,
    options: ChunkOptions
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];

    if (options.preserveParagraphs) {
      // 按段落分块
      const paragraphs = content.split(/\n\s*\n/);
      let currentChunk = '';
      let chunkIndex = 0;
      let startOffset = 0;

      for (const paragraph of paragraphs) {
        if (currentChunk.length + paragraph.length > options.chunkSize) {
          if (currentChunk.length > 0) {
            chunks.push({
              id: `chunk_${chunkIndex}`,
              content: currentChunk.trim(),
              index: chunkIndex,
              startOffset,
              endOffset: startOffset + currentChunk.length,
              metadata: { type: 'paragraph' }
            });
            chunkIndex++;
          }

          // 处理重叠
          if (options.overlap > 0 && chunks.length > 0) {
            const overlapText = this.getOverlapText(currentChunk, options.overlap);
            currentChunk = overlapText + '\n\n' + paragraph;
            startOffset = Math.max(0, startOffset + currentChunk.length - options.overlap);
          } else {
            currentChunk = paragraph;
            startOffset = startOffset + currentChunk.length;
          }
        } else {
          currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
        }
      }

      // 添加最后一个块
      if (currentChunk.length > 0) {
        chunks.push({
          id: `chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          index: chunkIndex,
          startOffset,
          endOffset: startOffset + currentChunk.length
        });
      }
    } else if (options.preserveSentences) {
      // 按句子分块
      const sentences = this.splitIntoSentences(content);
      let currentChunk = '';
      let chunkIndex = 0;
      let startOffset = 0;

      for (const sentence of sentences) {
        if (currentChunk.length + sentence.length > options.chunkSize) {
          if (currentChunk.length > 0) {
            chunks.push({
              id: `chunk_${chunkIndex}`,
              content: currentChunk.trim(),
              index: chunkIndex,
              startOffset,
              endOffset: startOffset + currentChunk.length,
              metadata: { type: 'sentence' }
            });
            chunkIndex++;
          }

          // 处理重叠
          if (options.overlap > 0 && chunks.length > 0) {
            const overlapText = this.getOverlapText(currentChunk, options.overlap);
            currentChunk = overlapText + ' ' + sentence;
            startOffset = Math.max(0, startOffset + currentChunk.length - options.overlap);
          } else {
            currentChunk = sentence;
            startOffset = startOffset + currentChunk.length;
          }
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence;
        }
      }

      // 添加最后一个块
      if (currentChunk.length > 0) {
        chunks.push({
          id: `chunk_${chunkIndex}`,
          content: currentChunk.trim(),
          index: chunkIndex,
          startOffset,
          endOffset: startOffset + currentChunk.length
        });
      }
    } else {
      // 固定大小分块
      for (let i = 0; i < content.length; i += options.chunkSize - options.overlap) {
        const end = Math.min(i + options.chunkSize, content.length);
        const chunkContent = content.substring(i, end);

        chunks.push({
          id: `chunk_${chunks.length}`,
          content: chunkContent,
          index: chunks.length,
          startOffset: i,
          endOffset: end,
          metadata: { type: 'fixed' }
        });

        if (end >= content.length) break;
      }
    }

    return chunks;
  }

  /**
   * 分割成句子
   */
  private splitIntoSentences(text: string): string[] {
    // 简单的句子分割（支持中英文）
    const sentences = text.split(/[.!?。！？]+/);
    return sentences
      .map(s => s.trim())
      .filter(s => s.length > 0)
      .map(s => s + '。'); // 添加句号
  }

  /**
   * 获取重叠文本
   */
  private getOverlapText(text: string, overlapSize: number): string {
    if (text.length <= overlapSize) return text;
    return text.substring(text.length - overlapSize);
  }

  /**
   * 生成向量嵌入
   */
  private async generateEmbeddings(chunks: DocumentChunk[]): Promise<number[][]> {
    const texts = chunks.map(chunk => chunk.content);
    return await this.embeddingModel.embedBatch(texts);
  }

  /**
   * 语义搜索
   */
  public async semanticSearch(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    // 生成查询向量
    const queryEmbedding = await this.embeddingModel.embed(query);

    // 向量搜索
    const searchResults = await this.vectorStore.search(queryEmbedding, {
      topK: options.topK || 5,
      threshold: options.threshold || 0.7
    });

    // 过滤和重新排序
    return this.filterAndRerankResults(searchResults, query, options);
  }

  /**
   * 关键词搜索
   */
  public async keywordSearch(keyword: string): Promise<SearchResult[]> {
    const results: SearchResult[] = [];

    for (const document of this.documents.values()) {
      for (const chunk of document.chunks) {
        if (chunk.content.toLowerCase().includes(keyword.toLowerCase())) {
          results.push({
            chunk,
            document,
            score: this.calculateKeywordScore(chunk.content, keyword),
            relevance: 0.8
          });
        }
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * 计算关键词得分
   */
  private calculateKeywordScore(text: string, keyword: string): number {
    const lowerText = text.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();

    // 计算关键词出现次数
    const matches = (lowerText.match(new RegExp(lowerKeyword, 'g')) || []).length;

    // 基于出现次数和文本长度计算得分
    return matches / (text.length / 100);
  }

  /**
   * 过滤和重新排序结果
   */
  private filterAndRerankResults(
    results: SearchResult[],
    query: string,
    options: SearchOptions
  ): SearchResult[] {
    let filteredResults = results;

    // 按类别过滤
    if (options.category) {
      filteredResults = filteredResults.filter(
        result => result.document.metadata.category === options.category
      );
    }

    // 按标签过滤
    if (options.tags && options.tags.length > 0) {
      filteredResults = filteredResults.filter(result =>
        options.tags!.some(tag =>
          result.document.metadata.tags.includes(tag)
        )
      );
    }

    // 按语言过滤
    if (options.language) {
      filteredResults = filteredResults.filter(
        result => result.document.metadata.language === options.language
      );
    }

    return filteredResults;
  }

  /**
   * 获取文档
   */
  public getDocument(documentId: string): Document | undefined {
    return this.documents.get(documentId);
  }

  /**
   * 获取所有文档
   */
  public getAllDocuments(): Document[] {
    return Array.from(this.documents.values());
  }

  /**
   * 删除文档
   */
  public async deleteDocument(documentId: string): Promise<boolean> {
    const document = this.documents.get(documentId);
    if (!document) return false;

    // 从向量存储中删除
    await this.vectorStore.deleteDocument(documentId);

    // 从内存中删除
    this.documents.delete(documentId);

    return true;
  }

  /**
   * 更新文档
   */
  public async updateDocument(
    documentId: string,
    content: string,
    metadata?: Partial<DocumentMetadata>
  ): Promise<boolean> {
    const document = this.documents.get(documentId);
    if (!document) return false;

    // 更新内容和元数据
    document.content = content;
    document.updatedAt = new Date();

    if (metadata) {
      Object.assign(document.metadata, metadata);
    }

    // 重新分块
    const chunks = await this.chunkDocument(content, {
      chunkSize: this.config.chunkSize,
      overlap: this.config.chunkOverlap,
      preserveSentences: true,
      preserveParagraphs: true
    });

    // 重新生成嵌入
    const embeddings = await this.generateEmbeddings(chunks);

    // 更新文档块
    document.chunks = chunks;

    // 更新向量存储
    await this.vectorStore.updateDocument(documentId, chunks, embeddings);

    return true;
  }

  /**
   * 获取统计信息
   */
  public async getStats(): Promise<any> {
    const vectorStats = await this.vectorStore.getStats();

    return {
      documentCount: this.documents.size,
      totalChunks: vectorStats.chunkCount,
      totalSize: Array.from(this.documents.values()).reduce((sum, doc) => sum + doc.size, 0),
      categories: this.getCategoryStats(),
      languages: this.getLanguageStats(),
      embeddingModel: this.embeddingModel.getModelInfo()
    };
  }

  /**
   * 获取类别统计
   */
  private getCategoryStats(): Record<string, number> {
    const stats: Record<string, number> = {};

    for (const document of this.documents.values()) {
      const category = document.metadata.category;
      stats[category] = (stats[category] || 0) + 1;
    }

    return stats;
  }

  /**
   * 获取语言统计
   */
  private getLanguageStats(): Record<string, number> {
    const stats: Record<string, number> = {};

    for (const document of this.documents.values()) {
      const language = document.metadata.language;
      stats[language] = (stats[language] || 0) + 1;
    }

    return stats;
  }

  /**
   * 清空知识库
   */
  public async clearAll(): Promise<void> {
    // 清空所有文档
    for (const documentId of this.documents.keys()) {
      await this.vectorStore.deleteDocument(documentId);
    }

    this.documents.clear();
  }
}
