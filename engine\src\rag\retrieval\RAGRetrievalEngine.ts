/**
 * RAG检索引擎
 * 实现语义检索、上下文管理、答案生成等功能
 */

import { KnowledgeBaseService, SearchResult, SearchOptions } from '../knowledge/KnowledgeBaseService';

/**
 * 查询类型枚举
 */
export enum QueryType {
  FACTUAL = 'factual',           // 事实性查询
  PROCEDURAL = 'procedural',     // 程序性查询
  CONCEPTUAL = 'conceptual',     // 概念性查询
  COMPARATIVE = 'comparative',   // 比较性查询
  CAUSAL = 'causal',            // 因果性查询
  TEMPORAL = 'temporal',        // 时间性查询
  SPATIAL = 'spatial'           // 空间性查询
}

/**
 * 检索策略枚举
 */
export enum RetrievalStrategy {
  SEMANTIC_ONLY = 'semantic_only',
  KEYWORD_ONLY = 'keyword_only',
  HYBRID = 'hybrid',
  CONTEXTUAL = 'contextual'
}

/**
 * 上下文信息接口
 */
export interface ContextInfo {
  conversationHistory: ConversationTurn[];
  currentLocation?: string;
  userProfile?: UserProfile;
  sessionId: string;
  timestamp: Date;
}

/**
 * 对话轮次接口
 */
export interface ConversationTurn {
  id: string;
  userQuery: string;
  systemResponse: string;
  timestamp: Date;
  context?: any;
}

/**
 * 用户画像接口
 */
export interface UserProfile {
  id: string;
  preferences: string[];
  interests: string[];
  language: string;
  expertiseLevel: 'beginner' | 'intermediate' | 'advanced';
  visitHistory: string[];
}

/**
 * 检索配置接口
 */
export interface RetrievalConfig {
  strategy: RetrievalStrategy;
  maxResults: number;
  semanticWeight: number;
  keywordWeight: number;
  contextWeight: number;
  diversityThreshold: number;
  relevanceThreshold: number;
  useReranking: boolean;
  enableContextualFiltering: boolean;
}

/**
 * 查询分析结果接口
 */
export interface QueryAnalysis {
  type: QueryType;
  intent: string;
  entities: Entity[];
  keywords: string[];
  complexity: number;
  language: string;
  requiresContext: boolean;
}

/**
 * 实体接口
 */
export interface Entity {
  text: string;
  type: string;
  confidence: number;
  startOffset: number;
  endOffset: number;
}

/**
 * 检索结果接口
 */
export interface RetrievalResult {
  searchResults: SearchResult[];
  context: ContextInfo;
  queryAnalysis: QueryAnalysis;
  retrievalMetadata: {
    strategy: RetrievalStrategy;
    totalResults: number;
    processingTime: number;
    confidence: number;
  };
}

/**
 * 答案生成结果接口
 */
export interface AnswerGenerationResult {
  answer: string;
  confidence: number;
  sources: SearchResult[];
  reasoning: string;
  followUpQuestions: string[];
  metadata: {
    generationTime: number;
    model: string;
    temperature: number;
  };
}

/**
 * 查询分析器
 */
export class QueryAnalyzer {
  /**
   * 分析查询
   */
  public analyzeQuery(query: string, context?: ContextInfo): QueryAnalysis {
    const keywords = this.extractKeywords(query);
    const entities = this.extractEntities(query);
    const type = this.classifyQueryType(query);
    const intent = this.extractIntent(query, context);
    
    return {
      type,
      intent,
      entities,
      keywords,
      complexity: this.calculateComplexity(query),
      language: this.detectLanguage(query),
      requiresContext: this.requiresContext(query, type)
    };
  }

  /**
   * 提取关键词
   */
  private extractKeywords(query: string): string[] {
    // 简单的关键词提取（实际应该使用NLP库）
    const stopWords = new Set(['的', '是', '在', '有', '和', '与', '或', '但', '然而', '因为', '所以', 
                              'the', 'is', 'in', 'and', 'or', 'but', 'because', 'so', 'what', 'how', 'where', 'when']);
    
    return query
      .toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 1 && !stopWords.has(word))
      .filter((word, index, arr) => arr.indexOf(word) === index); // 去重
  }

  /**
   * 提取实体
   */
  private extractEntities(query: string): Entity[] {
    const entities: Entity[] = [];
    
    // 简单的实体识别（实际应该使用NER模型）
    const patterns = [
      { type: 'PERSON', regex: /([A-Z][a-z]+\s+[A-Z][a-z]+)/g },
      { type: 'LOCATION', regex: /(北京|上海|广州|深圳|杭州|南京|成都|重庆)/g },
      { type: 'DATE', regex: /(\d{4}年|\d{1,2}月|\d{1,2}日)/g },
      { type: 'NUMBER', regex: /(\d+)/g }
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.regex.exec(query)) !== null) {
        entities.push({
          text: match[1],
          type: pattern.type,
          confidence: 0.8,
          startOffset: match.index,
          endOffset: match.index + match[1].length
        });
      }
    }
    
    return entities;
  }

  /**
   * 分类查询类型
   */
  private classifyQueryType(query: string): QueryType {
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('什么') || lowerQuery.includes('what')) {
      return QueryType.FACTUAL;
    } else if (lowerQuery.includes('如何') || lowerQuery.includes('怎么') || lowerQuery.includes('how')) {
      return QueryType.PROCEDURAL;
    } else if (lowerQuery.includes('为什么') || lowerQuery.includes('why')) {
      return QueryType.CAUSAL;
    } else if (lowerQuery.includes('比较') || lowerQuery.includes('对比') || lowerQuery.includes('compare')) {
      return QueryType.COMPARATIVE;
    } else if (lowerQuery.includes('何时') || lowerQuery.includes('when')) {
      return QueryType.TEMPORAL;
    } else if (lowerQuery.includes('哪里') || lowerQuery.includes('where')) {
      return QueryType.SPATIAL;
    } else {
      return QueryType.CONCEPTUAL;
    }
  }

  /**
   * 提取意图
   */
  private extractIntent(query: string, context?: ContextInfo): string {
    // 简单的意图识别
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('介绍') || lowerQuery.includes('explain')) {
      return 'explain';
    } else if (lowerQuery.includes('导航') || lowerQuery.includes('navigate')) {
      return 'navigate';
    } else if (lowerQuery.includes('推荐') || lowerQuery.includes('recommend')) {
      return 'recommend';
    } else if (lowerQuery.includes('搜索') || lowerQuery.includes('search')) {
      return 'search';
    } else {
      return 'general_inquiry';
    }
  }

  /**
   * 计算查询复杂度
   */
  private calculateComplexity(query: string): number {
    const words = query.split(/\s+/).length;
    const hasConjunctions = /和|与|或|但|然而|因为|所以|and|or|but|because|so/.test(query);
    const hasQuestions = /什么|如何|为什么|哪里|何时|what|how|why|where|when/.test(query);
    
    let complexity = words / 10; // 基础复杂度
    if (hasConjunctions) complexity += 0.3;
    if (hasQuestions) complexity += 0.2;
    
    return Math.min(complexity, 1.0);
  }

  /**
   * 检测语言
   */
  private detectLanguage(query: string): string {
    const chineseChars = query.match(/[\u4e00-\u9fff]/g);
    const englishChars = query.match(/[a-zA-Z]/g);
    
    if (chineseChars && chineseChars.length > (englishChars?.length || 0)) {
      return 'zh';
    } else {
      return 'en';
    }
  }

  /**
   * 判断是否需要上下文
   */
  private requiresContext(query: string, type: QueryType): boolean {
    const contextIndicators = ['这个', '那个', '它', '他', '她', 'this', 'that', 'it'];
    const hasContextIndicators = contextIndicators.some(indicator => 
      query.toLowerCase().includes(indicator)
    );
    
    return hasContextIndicators || type === QueryType.COMPARATIVE;
  }
}

/**
 * 上下文管理器
 */
export class ContextManager {
  private conversations: Map<string, ConversationTurn[]> = new Map();
  private maxHistoryLength: number = 10;

  /**
   * 添加对话轮次
   */
  public addConversationTurn(sessionId: string, turn: ConversationTurn): void {
    if (!this.conversations.has(sessionId)) {
      this.conversations.set(sessionId, []);
    }
    
    const history = this.conversations.get(sessionId)!;
    history.push(turn);
    
    // 保持历史长度限制
    if (history.length > this.maxHistoryLength) {
      history.shift();
    }
  }

  /**
   * 获取对话历史
   */
  public getConversationHistory(sessionId: string): ConversationTurn[] {
    return this.conversations.get(sessionId) || [];
  }

  /**
   * 构建上下文信息
   */
  public buildContext(
    sessionId: string,
    currentLocation?: string,
    userProfile?: UserProfile
  ): ContextInfo {
    return {
      conversationHistory: this.getConversationHistory(sessionId),
      currentLocation,
      userProfile,
      sessionId,
      timestamp: new Date()
    };
  }

  /**
   * 清除会话
   */
  public clearSession(sessionId: string): void {
    this.conversations.delete(sessionId);
  }

  /**
   * 获取相关上下文
   */
  public getRelevantContext(
    sessionId: string,
    query: string,
    maxTurns: number = 3
  ): ConversationTurn[] {
    const history = this.getConversationHistory(sessionId);
    
    // 简单的相关性计算（实际应该使用语义相似度）
    const relevantTurns = history
      .slice(-maxTurns)
      .filter(turn => this.isRelevant(turn, query));
    
    return relevantTurns;
  }

  /**
   * 判断对话轮次是否相关
   */
  private isRelevant(turn: ConversationTurn, query: string): boolean {
    const queryWords = query.toLowerCase().split(/\s+/);
    const turnWords = (turn.userQuery + ' ' + turn.systemResponse).toLowerCase().split(/\s+/);

    const commonWords = queryWords.filter(word => turnWords.includes(word));
    return commonWords.length / queryWords.length > 0.3;
  }
}

/**
 * RAG检索引擎
 */
export class RAGRetrievalEngine {
  private knowledgeBase: KnowledgeBaseService;
  private queryAnalyzer: QueryAnalyzer;
  private contextManager: ContextManager;
  private config: RetrievalConfig;

  constructor(
    knowledgeBase: KnowledgeBaseService,
    config: RetrievalConfig
  ) {
    this.knowledgeBase = knowledgeBase;
    this.queryAnalyzer = new QueryAnalyzer();
    this.contextManager = new ContextManager();
    this.config = config;
  }

  /**
   * 执行检索
   */
  public async retrieve(
    query: string,
    context?: ContextInfo,
    options?: Partial<SearchOptions>
  ): Promise<RetrievalResult> {
    const startTime = Date.now();

    // 分析查询
    const queryAnalysis = this.queryAnalyzer.analyzeQuery(query, context);

    // 构建搜索选项
    const searchOptions: SearchOptions = {
      topK: this.config.maxResults,
      threshold: this.config.relevanceThreshold,
      ...options
    };

    // 根据策略执行检索
    let searchResults: SearchResult[] = [];

    switch (this.config.strategy) {
      case RetrievalStrategy.SEMANTIC_ONLY:
        searchResults = await this.semanticRetrieval(query, searchOptions);
        break;
      case RetrievalStrategy.KEYWORD_ONLY:
        searchResults = await this.keywordRetrieval(query, searchOptions);
        break;
      case RetrievalStrategy.HYBRID:
        searchResults = await this.hybridRetrieval(query, queryAnalysis, searchOptions);
        break;
      case RetrievalStrategy.CONTEXTUAL:
        searchResults = await this.contextualRetrieval(query, queryAnalysis, context, searchOptions);
        break;
    }

    // 重新排序
    if (this.config.useReranking) {
      searchResults = await this.rerankResults(searchResults, query, queryAnalysis);
    }

    // 多样性过滤
    searchResults = this.diversityFiltering(searchResults);

    const processingTime = Date.now() - startTime;

    return {
      searchResults,
      context: context || this.contextManager.buildContext('default'),
      queryAnalysis,
      retrievalMetadata: {
        strategy: this.config.strategy,
        totalResults: searchResults.length,
        processingTime,
        confidence: this.calculateOverallConfidence(searchResults)
      }
    };
  }

  /**
   * 语义检索
   */
  private async semanticRetrieval(
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    return await this.knowledgeBase.semanticSearch(query, options);
  }

  /**
   * 关键词检索
   */
  private async keywordRetrieval(
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const keywords = this.queryAnalyzer.analyzeQuery(query).keywords;
    const allResults: SearchResult[] = [];

    for (const keyword of keywords) {
      const results = await this.knowledgeBase.keywordSearch(keyword);
      allResults.push(...results);
    }

    // 去重并排序
    const uniqueResults = this.deduplicateResults(allResults);
    return uniqueResults.slice(0, options.topK || 5);
  }

  /**
   * 混合检索
   */
  private async hybridRetrieval(
    query: string,
    queryAnalysis: QueryAnalysis,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    // 并行执行语义和关键词检索
    const [semanticResults, keywordResults] = await Promise.all([
      this.semanticRetrieval(query, options),
      this.keywordRetrieval(query, options)
    ]);

    // 合并和重新评分
    return this.mergeAndRescoreResults(
      semanticResults,
      keywordResults,
      this.config.semanticWeight,
      this.config.keywordWeight
    );
  }

  /**
   * 上下文检索
   */
  private async contextualRetrieval(
    query: string,
    queryAnalysis: QueryAnalysis,
    context?: ContextInfo,
    options?: SearchOptions
  ): Promise<SearchResult[]> {
    // 扩展查询以包含上下文信息
    const expandedQuery = this.expandQueryWithContext(query, context);

    // 执行混合检索
    const results = await this.hybridRetrieval(expandedQuery, queryAnalysis, options || {});

    // 基于上下文过滤结果
    if (this.config.enableContextualFiltering && context) {
      return this.filterByContext(results, context);
    }

    return results;
  }

  /**
   * 使用上下文扩展查询
   */
  private expandQueryWithContext(query: string, context?: ContextInfo): string {
    if (!context) return query;

    let expandedQuery = query;

    // 添加位置信息
    if (context.currentLocation) {
      expandedQuery += ` 位置:${context.currentLocation}`;
    }

    // 添加对话历史中的相关信息
    const relevantHistory = this.contextManager.getRelevantContext(
      context.sessionId,
      query,
      2
    );

    for (const turn of relevantHistory) {
      const relevantKeywords = this.extractRelevantKeywords(turn, query);
      if (relevantKeywords.length > 0) {
        expandedQuery += ` ${relevantKeywords.join(' ')}`;
      }
    }

    return expandedQuery;
  }

  /**
   * 提取相关关键词
   */
  private extractRelevantKeywords(turn: ConversationTurn, query: string): string[] {
    const queryKeywords = this.queryAnalyzer.analyzeQuery(query).keywords;
    const turnKeywords = this.queryAnalyzer.analyzeQuery(turn.userQuery).keywords;

    return turnKeywords.filter(keyword =>
      queryKeywords.some(qk => qk.includes(keyword) || keyword.includes(qk))
    );
  }

  /**
   * 基于上下文过滤结果
   */
  private filterByContext(results: SearchResult[], context: ContextInfo): SearchResult[] {
    return results.filter(result => {
      // 基于用户画像过滤
      if (context.userProfile) {
        const profile = context.userProfile;

        // 检查专业水平匹配
        if (profile.expertiseLevel === 'beginner') {
          // 过滤掉过于复杂的内容
          if (result.chunk.content.length > 1000) return false;
        }

        // 检查兴趣匹配
        const hasMatchingInterest = profile.interests.some(interest =>
          result.document.metadata.tags.includes(interest)
        );

        if (profile.interests.length > 0 && !hasMatchingInterest) {
          result.relevance *= 0.8; // 降低相关性
        }
      }

      return result.relevance >= this.config.relevanceThreshold;
    });
  }

  /**
   * 重新排序结果
   */
  private async rerankResults(
    results: SearchResult[],
    query: string,
    queryAnalysis: QueryAnalysis
  ): Promise<SearchResult[]> {
    // 基于查询类型调整评分
    const typeWeights: Record<QueryType, number> = {
      [QueryType.FACTUAL]: 1.0,
      [QueryType.PROCEDURAL]: 0.9,
      [QueryType.CONCEPTUAL]: 0.8,
      [QueryType.COMPARATIVE]: 0.7,
      [QueryType.CAUSAL]: 0.8,
      [QueryType.TEMPORAL]: 0.9,
      [QueryType.SPATIAL]: 0.9
    };

    const typeWeight = typeWeights[queryAnalysis.type] || 1.0;

    return results
      .map(result => ({
        ...result,
        score: result.score * typeWeight,
        relevance: result.relevance * typeWeight
      }))
      .sort((a, b) => b.score - a.score);
  }

  /**
   * 多样性过滤
   */
  private diversityFiltering(results: SearchResult[]): SearchResult[] {
    if (results.length <= 1) return results;

    const diverseResults: SearchResult[] = [results[0]]; // 总是包含最高分的结果

    for (let i = 1; i < results.length; i++) {
      const candidate = results[i];
      let isDiverse = true;

      // 检查与已选结果的相似性
      for (const selected of diverseResults) {
        const similarity = this.calculateContentSimilarity(
          candidate.chunk.content,
          selected.chunk.content
        );

        if (similarity > this.config.diversityThreshold) {
          isDiverse = false;
          break;
        }
      }

      if (isDiverse) {
        diverseResults.push(candidate);
      }

      // 限制结果数量
      if (diverseResults.length >= this.config.maxResults) {
        break;
      }
    }

    return diverseResults;
  }

  /**
   * 计算内容相似性
   */
  private calculateContentSimilarity(content1: string, content2: string): number {
    const words1 = new Set(content1.toLowerCase().split(/\s+/));
    const words2 = new Set(content2.toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size; // Jaccard相似性
  }

  /**
   * 去重结果
   */
  private deduplicateResults(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = `${result.document.id}_${result.chunk.id}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 合并和重新评分结果
   */
  private mergeAndRescoreResults(
    semanticResults: SearchResult[],
    keywordResults: SearchResult[],
    semanticWeight: number,
    keywordWeight: number
  ): SearchResult[] {
    const allResults = [...semanticResults, ...keywordResults];
    const uniqueResults = this.deduplicateResults(allResults);

    // 重新计算综合得分
    return uniqueResults
      .map(result => {
        const inSemantic = semanticResults.some(sr =>
          sr.document.id === result.document.id && sr.chunk.id === result.chunk.id
        );
        const inKeyword = keywordResults.some(kr =>
          kr.document.id === result.document.id && kr.chunk.id === result.chunk.id
        );

        let combinedScore = 0;
        if (inSemantic) combinedScore += result.score * semanticWeight;
        if (inKeyword) combinedScore += result.score * keywordWeight;

        return {
          ...result,
          score: combinedScore,
          relevance: combinedScore
        };
      })
      .sort((a, b) => b.score - a.score);
  }

  /**
   * 计算整体置信度
   */
  private calculateOverallConfidence(results: SearchResult[]): number {
    if (results.length === 0) return 0;

    const avgScore = results.reduce((sum, result) => sum + result.score, 0) / results.length;
    const scoreVariance = results.reduce((sum, result) =>
      sum + Math.pow(result.score - avgScore, 2), 0
    ) / results.length;

    // 基于平均分和方差计算置信度
    const confidence = avgScore * (1 - Math.sqrt(scoreVariance));
    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * 添加对话轮次
   */
  public addConversationTurn(sessionId: string, turn: ConversationTurn): void {
    this.contextManager.addConversationTurn(sessionId, turn);
  }

  /**
   * 获取上下文管理器
   */
  public getContextManager(): ContextManager {
    return this.contextManager;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<RetrievalConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * 获取配置
   */
  public getConfig(): RetrievalConfig {
    return { ...this.config };
  }
}
