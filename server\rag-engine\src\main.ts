import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用CORS
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('RAG查询引擎')
    .setDescription('RAG数字人交互系统 - 多知识库RAG查询引擎API')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 健康检查端点
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'rag-engine',
    });
  });

  const port = process.env.PORT || 3002;
  await app.listen(port);
  
  console.log(`RAG查询引擎启动成功，端口: ${port}`);
  console.log(`Swagger文档地址: http://localhost:${port}/api/docs`);
}

bootstrap();
