import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  HttpException,
  HttpStatus,
  Query,
} from '@nestjs/common';
import {
  FileInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { UploadService, DocumentMetadata, UploadResult } from './upload.service';

@ApiTags('文档上传')
@Controller('api/knowledge-bases')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post(':id/documents')
  @ApiOperation({ summary: '上传单个文档到知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '要上传的文档文件',
        },
        title: {
          type: 'string',
          description: '文档标题',
        },
        category: {
          type: 'string',
          description: '文档分类',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: '文档标签',
        },
        author: {
          type: 'string',
          description: '文档作者',
        },
        language: {
          type: 'string',
          description: '文档语言',
          default: 'zh-CN',
        },
        description: {
          type: 'string',
          description: '文档描述',
        },
      },
      required: ['file', 'title', 'category'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '文档上传成功',
    schema: {
      type: 'object',
      properties: {
        documentId: { type: 'string' },
        uploadId: { type: 'string' },
        status: { type: 'string' },
        filePath: { type: 'string' },
        message: { type: 'string' },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @Param('id') knowledgeBaseId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
  ): Promise<UploadResult> {
    if (!file) {
      throw new HttpException('未提供文件', HttpStatus.BAD_REQUEST);
    }

    const metadata: DocumentMetadata = {
      title: body.title,
      category: body.category,
      tags: Array.isArray(body.tags) ? body.tags : body.tags?.split(',') || [],
      author: body.author,
      language: body.language || 'zh-CN',
      description: body.description,
    };

    // 这里应该从JWT token中获取用户ID
    const userId = body.userId || 'system';

    try {
      return await this.uploadService.uploadKnowledgeDocument(
        knowledgeBaseId,
        file,
        metadata,
        userId,
      );
    } catch (error) {
      throw new HttpException(
        error.message || '文档上传失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post(':id/documents/batch')
  @ApiOperation({ summary: '批量上传文档到知识库' })
  @ApiParam({ name: 'id', description: '知识库ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: '要上传的文档文件列表',
        },
        metadata: {
          type: 'string',
          description: 'JSON格式的文档元数据列表',
        },
      },
      required: ['files'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '批量上传结果',
    schema: {
      type: 'object',
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              documentId: { type: 'string' },
              uploadId: { type: 'string' },
              status: { type: 'string' },
              filePath: { type: 'string' },
              message: { type: 'string' },
            },
          },
        },
        totalCount: { type: 'number' },
        successCount: { type: 'number' },
        failureCount: { type: 'number' },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 10))
  async batchUploadDocuments(
    @Param('id') knowledgeBaseId: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: any,
  ): Promise<any> {
    if (!files || files.length === 0) {
      throw new HttpException('未提供文件', HttpStatus.BAD_REQUEST);
    }

    let metadata: DocumentMetadata[] = [];
    
    if (body.metadata) {
      try {
        metadata = JSON.parse(body.metadata);
      } catch (error) {
        throw new HttpException('元数据格式错误', HttpStatus.BAD_REQUEST);
      }
    }

    // 如果元数据数量不足，使用默认值
    while (metadata.length < files.length) {
      metadata.push({
        title: files[metadata.length].originalname,
        category: 'default',
        tags: [],
        language: 'zh-CN',
      });
    }

    const userId = body.userId || 'system';

    try {
      const results = await this.uploadService.batchUploadDocuments(
        knowledgeBaseId,
        files,
        metadata,
        userId,
      );

      return {
        results,
        totalCount: files.length,
        successCount: results.length,
        failureCount: files.length - results.length,
      };
    } catch (error) {
      throw new HttpException(
        error.message || '批量上传失败',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('uploads/:uploadId/progress')
  @ApiOperation({ summary: '获取上传进度' })
  @ApiParam({ name: 'uploadId', description: '上传ID' })
  @ApiResponse({
    status: 200,
    description: '上传进度信息',
    schema: {
      type: 'object',
      properties: {
        uploadId: { type: 'string' },
        progress: { type: 'number' },
        status: { type: 'string' },
        timestamp: { type: 'string' },
      },
    },
  })
  async getUploadProgress(@Param('uploadId') uploadId: string): Promise<any> {
    try {
      const progress = await this.uploadService.getUploadProgress(uploadId);
      
      if (!progress) {
        throw new HttpException('上传记录不存在', HttpStatus.NOT_FOUND);
      }

      return progress;
    } catch (error) {
      throw new HttpException(
        error.message || '获取上传进度失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
