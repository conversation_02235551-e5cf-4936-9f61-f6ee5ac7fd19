global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter（系统指标）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    metrics_path: /nginx_status
    scrape_interval: 30s

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # MinIO监控
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: /minio/v2/metrics/cluster

  # API网关监控
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: /metrics
    scrape_interval: 30s

  # 知识库服务监控
  - job_name: 'knowledge-service'
    static_configs:
      - targets: ['knowledge-service:3000']
    metrics_path: /metrics
    scrape_interval: 30s

  # 绑定服务监控
  - job_name: 'binding-service'
    static_configs:
      - targets: ['binding-service:3001']
    metrics_path: /metrics
    scrape_interval: 30s

  # RAG引擎监控
  - job_name: 'rag-engine'
    static_configs:
      - targets: ['rag-engine:3002']
    metrics_path: /metrics
    scrape_interval: 30s

  # Chroma向量数据库监控
  - job_name: 'chroma'
    static_configs:
      - targets: ['chroma:8000']
    metrics_path: /api/v1/metrics
    scrape_interval: 60s

  # 应用健康检查
  - job_name: 'health-checks'
    static_configs:
      - targets: 
        - 'api-gateway:3000'
        - 'knowledge-service:3000'
        - 'binding-service:3001'
        - 'rag-engine:3002'
    metrics_path: /health
    scrape_interval: 30s
