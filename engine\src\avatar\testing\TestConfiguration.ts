/**
 * 测试配置管理
 * 为M4阶段的系统测试提供统一的配置管理
 */

/**
 * 测试环境配置
 */
export interface TestEnvironmentConfig {
  name: string;
  baseUrl: string;
  apiKey: string;
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
  };
  storage: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
  };
  timeout: {
    short: number;    // 短超时 (5s)
    medium: number;   // 中等超时 (30s)
    long: number;     // 长超时 (120s)
  };
}

/**
 * 测试数据配置
 */
export interface TestDataConfig {
  samplePhotos: string[];
  sampleBipFiles: string[];
  sampleDigitalHumans: string[];
  testUsers: {
    id: string;
    username: string;
    email: string;
    role: 'admin' | 'user' | 'creator';
  }[];
  performanceThresholds: {
    maxCreationTime: number;      // 最大创建时间 (ms)
    maxRenderTime: number;        // 最大渲染时间 (ms)
    maxMemoryUsage: number;       // 最大内存使用 (MB)
    minFPS: number;               // 最小FPS
    maxLoadTime: number;          // 最大加载时间 (ms)
  };
}

/**
 * 测试套件配置
 */
export interface TestSuiteConfig {
  functional: {
    enabled: boolean;
    parallel: boolean;
    retryCount: number;
    timeout: number;
  };
  performance: {
    enabled: boolean;
    iterations: number;
    warmupIterations: number;
    timeout: number;
  };
  integration: {
    enabled: boolean;
    setupTimeout: number;
    teardownTimeout: number;
  };
  compatibility: {
    enabled: boolean;
    browsers: string[];
    devices: string[];
    platforms: string[];
  };
  acceptance: {
    enabled: boolean;
    userScenarios: string[];
    timeout: number;
  };
}

/**
 * 测试报告配置
 */
export interface TestReportConfig {
  outputDir: string;
  formats: ('json' | 'html' | 'xml' | 'csv')[];
  includeScreenshots: boolean;
  includeLogs: boolean;
  includeMetrics: boolean;
  emailNotification: {
    enabled: boolean;
    recipients: string[];
    onFailureOnly: boolean;
  };
}

/**
 * 测试配置管理器
 */
export class TestConfiguration {
  private static instance: TestConfiguration;
  private environments: Map<string, TestEnvironmentConfig> = new Map();
  private testData: TestDataConfig;
  private suiteConfig: TestSuiteConfig;
  private reportConfig: TestReportConfig;
  private currentEnvironment: string = 'development';

  private constructor() {
    this.initializeDefaultConfigurations();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TestConfiguration {
    if (!TestConfiguration.instance) {
      TestConfiguration.instance = new TestConfiguration();
    }
    return TestConfiguration.instance;
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigurations(): void {
    // 开发环境配置
    this.environments.set('development', {
      name: 'Development',
      baseUrl: 'http://localhost:3000',
      apiKey: 'dev-api-key',
      database: {
        host: 'localhost',
        port: 3306,
        database: 'digital_human_dev',
        username: 'dev_user',
        password: 'dev_password'
      },
      storage: {
        endpoint: 'localhost:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin'
      },
      timeout: {
        short: 5000,
        medium: 30000,
        long: 120000
      }
    });

    // 测试环境配置
    this.environments.set('testing', {
      name: 'Testing',
      baseUrl: 'http://test.digitalhuman.com',
      apiKey: 'test-api-key',
      database: {
        host: 'test-db.internal',
        port: 3306,
        database: 'digital_human_test',
        username: 'test_user',
        password: 'test_password'
      },
      storage: {
        endpoint: 'test-storage.internal:9000',
        accessKey: 'testadmin',
        secretKey: 'testpassword'
      },
      timeout: {
        short: 10000,
        medium: 60000,
        long: 300000
      }
    });

    // 生产环境配置
    this.environments.set('production', {
      name: 'Production',
      baseUrl: 'https://api.digitalhuman.com',
      apiKey: process.env.PROD_API_KEY || '',
      database: {
        host: process.env.DB_HOST || '',
        port: parseInt(process.env.DB_PORT || '3306'),
        database: process.env.DB_NAME || '',
        username: process.env.DB_USER || '',
        password: process.env.DB_PASSWORD || ''
      },
      storage: {
        endpoint: process.env.STORAGE_ENDPOINT || '',
        accessKey: process.env.STORAGE_ACCESS_KEY || '',
        secretKey: process.env.STORAGE_SECRET_KEY || ''
      },
      timeout: {
        short: 15000,
        medium: 90000,
        long: 600000
      }
    });

    // 测试数据配置
    this.testData = {
      samplePhotos: [
        'test-data/photos/portrait1.jpg',
        'test-data/photos/portrait2.jpg',
        'test-data/photos/portrait3.jpg'
      ],
      sampleBipFiles: [
        'test-data/bip/walk.bip',
        'test-data/bip/run.bip',
        'test-data/bip/idle.bip'
      ],
      sampleDigitalHumans: [
        'test-data/humans/basic-human.dhp',
        'test-data/humans/cartoon-human.dhp',
        'test-data/humans/realistic-human.dhp'
      ],
      testUsers: [
        {
          id: 'test-admin-001',
          username: 'testadmin',
          email: '<EMAIL>',
          role: 'admin'
        },
        {
          id: 'test-user-001',
          username: 'testuser1',
          email: '<EMAIL>',
          role: 'user'
        },
        {
          id: 'test-creator-001',
          username: 'testcreator1',
          email: '<EMAIL>',
          role: 'creator'
        }
      ],
      performanceThresholds: {
        maxCreationTime: 30000,    // 30秒
        maxRenderTime: 16.67,      // 60 FPS
        maxMemoryUsage: 2048,      // 2GB
        minFPS: 30,
        maxLoadTime: 5000          // 5秒
      }
    };

    // 测试套件配置
    this.suiteConfig = {
      functional: {
        enabled: true,
        parallel: true,
        retryCount: 2,
        timeout: 60000
      },
      performance: {
        enabled: true,
        iterations: 10,
        warmupIterations: 3,
        timeout: 300000
      },
      integration: {
        enabled: true,
        setupTimeout: 30000,
        teardownTimeout: 15000
      },
      compatibility: {
        enabled: true,
        browsers: ['chrome', 'firefox', 'safari', 'edge'],
        devices: ['desktop', 'tablet', 'mobile'],
        platforms: ['windows', 'macos', 'linux', 'ios', 'android']
      },
      acceptance: {
        enabled: true,
        userScenarios: [
          'new-user-onboarding',
          'digital-human-creation',
          'marketplace-interaction',
          'collaboration-workflow'
        ],
        timeout: 120000
      }
    };

    // 测试报告配置
    this.reportConfig = {
      outputDir: './test-reports',
      formats: ['json', 'html'],
      includeScreenshots: true,
      includeLogs: true,
      includeMetrics: true,
      emailNotification: {
        enabled: false,
        recipients: ['<EMAIL>'],
        onFailureOnly: true
      }
    };
  }

  /**
   * 设置当前环境
   */
  public setEnvironment(environment: string): void {
    if (!this.environments.has(environment)) {
      throw new Error(`未知的测试环境: ${environment}`);
    }
    this.currentEnvironment = environment;
  }

  /**
   * 获取当前环境配置
   */
  public getCurrentEnvironment(): TestEnvironmentConfig {
    const config = this.environments.get(this.currentEnvironment);
    if (!config) {
      throw new Error(`环境配置不存在: ${this.currentEnvironment}`);
    }
    return config;
  }

  /**
   * 获取所有环境列表
   */
  public getEnvironments(): string[] {
    return Array.from(this.environments.keys());
  }

  /**
   * 添加或更新环境配置
   */
  public setEnvironmentConfig(name: string, config: TestEnvironmentConfig): void {
    this.environments.set(name, config);
  }

  /**
   * 获取测试数据配置
   */
  public getTestData(): TestDataConfig {
    return { ...this.testData };
  }

  /**
   * 更新测试数据配置
   */
  public updateTestData(data: Partial<TestDataConfig>): void {
    this.testData = { ...this.testData, ...data };
  }

  /**
   * 获取测试套件配置
   */
  public getSuiteConfig(): TestSuiteConfig {
    return { ...this.suiteConfig };
  }

  /**
   * 更新测试套件配置
   */
  public updateSuiteConfig(config: Partial<TestSuiteConfig>): void {
    this.suiteConfig = { ...this.suiteConfig, ...config };
  }

  /**
   * 获取测试报告配置
   */
  public getReportConfig(): TestReportConfig {
    return { ...this.reportConfig };
  }

  /**
   * 更新测试报告配置
   */
  public updateReportConfig(config: Partial<TestReportConfig>): void {
    this.reportConfig = { ...this.reportConfig, ...config };
  }

  /**
   * 获取性能阈值
   */
  public getPerformanceThresholds(): TestDataConfig['performanceThresholds'] {
    return { ...this.testData.performanceThresholds };
  }

  /**
   * 更新性能阈值
   */
  public updatePerformanceThresholds(thresholds: Partial<TestDataConfig['performanceThresholds']>): void {
    this.testData.performanceThresholds = { 
      ...this.testData.performanceThresholds, 
      ...thresholds 
    };
  }

  /**
   * 获取测试用户
   */
  public getTestUsers(): TestDataConfig['testUsers'] {
    return [...this.testData.testUsers];
  }

  /**
   * 根据角色获取测试用户
   */
  public getTestUserByRole(role: 'admin' | 'user' | 'creator'): TestDataConfig['testUsers'][0] | undefined {
    return this.testData.testUsers.find(user => user.role === role);
  }

  /**
   * 获取超时配置
   */
  public getTimeout(type: 'short' | 'medium' | 'long'): number {
    const env = this.getCurrentEnvironment();
    return env.timeout[type];
  }

  /**
   * 验证配置完整性
   */
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const env = this.getCurrentEnvironment();

    // 验证环境配置
    if (!env.baseUrl) {
      errors.push('缺少基础URL配置');
    }

    if (!env.apiKey) {
      errors.push('缺少API密钥配置');
    }

    // 验证数据库配置
    if (!env.database.host || !env.database.database) {
      errors.push('数据库配置不完整');
    }

    // 验证存储配置
    if (!env.storage.endpoint || !env.storage.accessKey) {
      errors.push('存储配置不完整');
    }

    // 验证测试数据
    if (this.testData.samplePhotos.length === 0) {
      errors.push('缺少测试照片数据');
    }

    if (this.testData.testUsers.length === 0) {
      errors.push('缺少测试用户数据');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 导出配置为JSON
   */
  public exportConfiguration(): any {
    return {
      currentEnvironment: this.currentEnvironment,
      environments: Object.fromEntries(this.environments),
      testData: this.testData,
      suiteConfig: this.suiteConfig,
      reportConfig: this.reportConfig
    };
  }

  /**
   * 从JSON导入配置
   */
  public importConfiguration(config: any): void {
    if (config.currentEnvironment) {
      this.currentEnvironment = config.currentEnvironment;
    }

    if (config.environments) {
      this.environments = new Map(Object.entries(config.environments));
    }

    if (config.testData) {
      this.testData = config.testData;
    }

    if (config.suiteConfig) {
      this.suiteConfig = config.suiteConfig;
    }

    if (config.reportConfig) {
      this.reportConfig = config.reportConfig;
    }
  }
}
