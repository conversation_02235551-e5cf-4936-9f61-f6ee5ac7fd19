groups:
  - name: rag_system_alerts
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for {{ $labels.job }}."

      # 高响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}."

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      # 磁盘空间告警
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk space is {{ $value | humanizePercentage }} available on {{ $labels.instance }}."

      # 数据库连接告警
      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections."

      # Redis内存使用告警
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}."

      # 文件上传失败率告警
      - alert: HighUploadFailureRate
        expr: rate(upload_failures_total[5m]) / rate(upload_attempts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High upload failure rate"
          description: "Upload failure rate is {{ $value | humanizePercentage }}."

      # RAG查询失败率告警
      - alert: HighRAGQueryFailureRate
        expr: rate(rag_query_failures_total[5m]) / rate(rag_query_attempts_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High RAG query failure rate"
          description: "RAG query failure rate is {{ $value | humanizePercentage }}."

      # 向量数据库响应时间告警
      - alert: VectorDBSlowResponse
        expr: histogram_quantile(0.95, rate(vector_db_request_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Vector database slow response"
          description: "Vector database 95th percentile response time is {{ $value }}s."

      # 知识库处理队列积压告警
      - alert: ProcessingQueueBacklog
        expr: processing_queue_size > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Processing queue backlog"
          description: "Processing queue has {{ $value }} pending items."

  - name: infrastructure_alerts
    rules:
      # 容器重启告警
      - alert: ContainerRestarted
        expr: increase(container_restart_count[1h]) > 0
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Container restarted"
          description: "Container {{ $labels.name }} has restarted {{ $value }} times in the last hour."

      # 网络连接数告警
      - alert: HighNetworkConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High network connections"
          description: "Number of TCP connections is {{ $value }} on {{ $labels.instance }}."

      # 文件描述符使用率告警
      - alert: HighFileDescriptorUsage
        expr: node_filefd_allocated / node_filefd_maximum > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High file descriptor usage"
          description: "File descriptor usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      # 负载平均值告警
      - alert: HighLoadAverage
        expr: node_load1 > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High load average"
          description: "Load average is {{ $value }} on {{ $labels.instance }}."
