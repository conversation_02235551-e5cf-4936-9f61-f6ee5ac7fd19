import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { StorageService } from './storage.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'MINIO_CLIENT',
      useFactory: (configService: ConfigService): Minio.Client => {
        const config = configService.get('production.fileStorage');
        
        return new Minio.Client({
          endPoint: config.endpoint.replace(/^https?:\/\//, ''),
          port: config.endpoint.includes('https') ? 443 : 9000,
          useSSL: config.endpoint.includes('https'),
          accessKey: config.accessKey,
          secretKey: config.secretKey,
          region: config.region,
        });
      },
      inject: [ConfigService],
    },
    StorageService,
  ],
  exports: ['MINIO_CLIENT', StorageService],
})
export class StorageModule {}
