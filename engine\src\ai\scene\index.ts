/**
 * 场景生成AI模块导出
 */

// 类型定义
export * from './SceneGenerationTypes';

// 核心模型
export { SceneUnderstandingModel } from './SceneUnderstandingModel';
export { LayoutGenerationModel, LayoutAlgorithm } from './LayoutGenerationModel';
export { AssetMatchingModel } from './AssetMatchingModel';

// AI管理器
export { SceneGenerationAIManager } from './SceneGenerationAIManager';

// 自然语言理解
export { 
  SceneDescriptionUnderstanding,
  SpatialRelationshipParser,
  SceneIntentClassifier,
  NLPProcessor
} from './SceneDescriptionUnderstanding';

// 语音控制器
export { 
  VoiceSceneGenerationController,
  ConversationManager
} from './VoiceSceneGenerationController';

// 配置接口
export type {
  SceneGenerationAIManagerConfig
} from './SceneGenerationAIManager';

export type {
  SceneUnderstandingConfig
} from './SceneUnderstandingModel';

export type {
  LayoutGenerationConfig
} from './LayoutGenerationModel';

export type {
  AssetMatchingConfig,
  Asset
} from './AssetMatchingModel';

export type {
  VoiceSessionResult,
  VoiceSessionState,
  VoiceInteraction
} from './VoiceSceneGenerationController';

export type {
  NLPProcessorConfig
} from './SceneDescriptionUnderstanding';
