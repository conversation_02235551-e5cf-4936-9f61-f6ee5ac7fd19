import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '../cache/cache.service';
import { EmbeddingService } from '../embedding/embedding.service';
import { VectorSearchService } from '../vector-search/vector-search.service';
import { BindingService } from '../binding/binding.service';
import { LLMService } from '../llm/llm.service';
import { RAGService } from './rag.service';
import { RAGController } from './rag.controller';

@Module({
  imports: [
    ConfigModule,
    CacheModule,
  ],
  controllers: [RAGController],
  providers: [
    EmbeddingService,
    VectorSearchService,
    BindingService,
    LLMService,
    RAGService,
  ],
  exports: [RAGService],
})
export class RAGModule {}
