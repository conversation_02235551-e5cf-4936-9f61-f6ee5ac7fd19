/**
 * BIP动画重定向系统使用示例
 * 展示如何使用BIP动画重定向功能将3ds Max的BIP动画应用到数字人
 */

import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/Transform';
import { DigitalHumanComponent } from '../engine/src/avatar/components/DigitalHumanComponent';
import { 
  BIPAnimationRetargeter, 
  BIPAnimation, 
  RetargetingConfig,
  RetargetingResult
} from '../engine/src/avatar/animation/BIPAnimationRetargeter';
import { BIPSkeletonParser, BIPSkeletonData } from '../engine/src/avatar/bip/BIPSkeletonParser';
import { BIPToStandardMapping, StandardSkeletonData } from '../engine/src/avatar/bip/BIPToStandardMapping';
import * as THREE from 'three';

/**
 * BIP动画重定向示例类
 */
export class BIPAnimationRetargetingExample {
  private world: World;
  private retargeter: BIPAnimationRetargeter;
  private skeletonParser: BIPSkeletonParser;
  private boneMapper: BIPToStandardMapping;
  private digitalHumanEntity: Entity;

  constructor() {
    this.world = new World();
    this.initializeExample();
  }

  /**
   * 初始化示例
   */
  private async initializeExample(): Promise<void> {
    console.log('🎬 初始化BIP动画重定向示例...');

    // 1. 创建BIP动画重定向器
    this.retargeter = new BIPAnimationRetargeter({
      preserveOriginalTiming: true,
      autoScale: true,
      scaleFactor: 1.0,
      smoothInterpolation: true,
      qualityLevel: 'high',
      debug: true
    });

    // 2. 创建BIP骨骼解析器
    this.skeletonParser = new BIPSkeletonParser({
      debug: true,
      validateStructure: true,
      autoFixHierarchy: true
    });

    // 3. 创建骨骼映射器
    this.boneMapper = new BIPToStandardMapping({
      strictMode: false,
      autoFixMissing: true,
      preserveOriginalTransforms: false,
      debug: true
    });

    // 4. 创建数字人实体
    await this.createDigitalHuman();

    // 5. 演示BIP动画重定向
    await this.demonstrateBIPRetargeting();

    console.log('✅ BIP动画重定向示例初始化完成');
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHuman(): Promise<void> {
    console.log('👤 创建数字人实体...');

    // 创建实体
    this.digitalHumanEntity = new Entity(this.world);
    this.digitalHumanEntity.name = 'BIPRetargetingTarget';

    // 添加Transform组件
    const transform = new Transform();
    this.digitalHumanEntity.addComponent(transform);

    // 添加数字人组件
    const digitalHumanComponent = new DigitalHumanComponent(this.digitalHumanEntity, {
      name: 'BIP重定向目标',
      userId: 'retargeting-example',
      bodyMorphTargets: {
        height: 0.0,
        weight: 0.0,
        muscle: 0.0,
        chest: 0.0,
        waist: 0.0,
        hips: 0.0,
        shoulders: 0.0,
        custom: new Map()
      }
    });

    this.digitalHumanEntity.addComponent(digitalHumanComponent);

    // 添加到世界
    this.world.addEntity(this.digitalHumanEntity);

    console.log('✅ 数字人实体创建完成');
  }

  /**
   * 演示BIP动画重定向
   */
  private async demonstrateBIPRetargeting(): Promise<void> {
    console.log('🎭 演示BIP动画重定向...');

    // 1. 创建示例BIP动画数据
    const bipAnimation = await this.createSampleBIPAnimation();
    console.log(`📁 创建示例BIP动画: ${bipAnimation.name}`);

    // 2. 解析BIP骨骼
    const bipSkeleton = await this.createSampleBIPSkeleton();
    console.log(`🦴 创建示例BIP骨骼，共 ${bipSkeleton.bones.size} 个骨骼`);

    // 3. 映射到标准骨骼
    const standardSkeleton = await this.boneMapper.mapToStandardSkeleton(bipSkeleton);
    console.log(`🎯 映射到标准骨骼，共 ${standardSkeleton.bones.size} 个骨骼`);

    // 4. 重定向动画
    console.log('🔄 开始重定向动画...');
    const retargetingResult = await this.retargeter.retargetBIPToSkeleton(
      bipAnimation,
      standardSkeleton
    );

    // 5. 处理重定向结果
    await this.handleRetargetingResult(retargetingResult);

    // 6. 演示不同质量级别的重定向
    await this.demonstrateQualityLevels(bipAnimation, standardSkeleton);

    // 7. 演示批量重定向
    await this.demonstrateBatchRetargeting(standardSkeleton);

    console.log('✅ BIP动画重定向演示完成');
  }

  /**
   * 创建示例BIP动画
   */
  private async createSampleBIPAnimation(): Promise<BIPAnimation> {
    // 创建一个简单的走路动画
    const walkAnimation: BIPAnimation = {
      name: 'Walk_Cycle',
      duration: 2.0, // 2秒
      frameRate: 30,
      positionTracks: [
        {
          boneName: 'Bip01',
          keyframes: [
            { time: 0.0, position: new THREE.Vector3(0, 0, 0) },
            { time: 1.0, position: new THREE.Vector3(0, 0, 1) },
            { time: 2.0, position: new THREE.Vector3(0, 0, 2) }
          ]
        },
        {
          boneName: 'Bip01 L Foot',
          keyframes: [
            { time: 0.0, position: new THREE.Vector3(-0.2, 0, 0) },
            { time: 0.5, position: new THREE.Vector3(-0.2, 0.1, 0.5) },
            { time: 1.0, position: new THREE.Vector3(-0.2, 0, 1) },
            { time: 1.5, position: new THREE.Vector3(-0.2, 0.1, 1.5) },
            { time: 2.0, position: new THREE.Vector3(-0.2, 0, 2) }
          ]
        },
        {
          boneName: 'Bip01 R Foot',
          keyframes: [
            { time: 0.0, position: new THREE.Vector3(0.2, 0, 0.5) },
            { time: 0.5, position: new THREE.Vector3(0.2, 0, 1) },
            { time: 1.0, position: new THREE.Vector3(0.2, 0.1, 1.5) },
            { time: 1.5, position: new THREE.Vector3(0.2, 0, 2) },
            { time: 2.0, position: new THREE.Vector3(0.2, 0.1, 2.5) }
          ]
        }
      ],
      rotationTracks: [
        {
          boneName: 'Bip01 L Thigh',
          keyframes: [
            { time: 0.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)) },
            { time: 0.5, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0.5, 0, 0)) },
            { time: 1.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)) },
            { time: 1.5, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(-0.5, 0, 0)) },
            { time: 2.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)) }
          ]
        },
        {
          boneName: 'Bip01 R Thigh',
          keyframes: [
            { time: 0.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(-0.5, 0, 0)) },
            { time: 0.5, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)) },
            { time: 1.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0.5, 0, 0)) },
            { time: 1.5, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)) },
            { time: 2.0, rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(-0.5, 0, 0)) }
          ]
        }
      ],
      scaleTracks: [],
      sourceSkeleton: await this.createSampleBIPSkeleton()
    };

    return walkAnimation;
  }

  /**
   * 创建示例BIP骨骼
   */
  private async createSampleBIPSkeleton(): Promise<BIPSkeletonData> {
    // 创建一个简化的BIP骨骼结构
    const bipSkeleton: BIPSkeletonData = {
      version: '1.0',
      bones: new Map([
        ['Bip01', { 
          name: 'Bip01', 
          parent: null, 
          position: new THREE.Vector3(0, 1, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 Pelvis']
        }],
        ['Bip01 Pelvis', { 
          name: 'Bip01 Pelvis', 
          parent: 'Bip01', 
          position: new THREE.Vector3(0, 0.9, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 Spine', 'Bip01 L Thigh', 'Bip01 R Thigh']
        }],
        ['Bip01 Spine', { 
          name: 'Bip01 Spine', 
          parent: 'Bip01 Pelvis', 
          position: new THREE.Vector3(0, 1.1, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 Spine1']
        }],
        ['Bip01 Spine1', { 
          name: 'Bip01 Spine1', 
          parent: 'Bip01 Spine', 
          position: new THREE.Vector3(0, 1.3, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 Neck', 'Bip01 L Clavicle', 'Bip01 R Clavicle']
        }],
        ['Bip01 Neck', { 
          name: 'Bip01 Neck', 
          parent: 'Bip01 Spine1', 
          position: new THREE.Vector3(0, 1.5, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 Head']
        }],
        ['Bip01 Head', { 
          name: 'Bip01 Head', 
          parent: 'Bip01 Neck', 
          position: new THREE.Vector3(0, 1.7, 0),
          rotation: new THREE.Quaternion(),
          children: []
        }],
        ['Bip01 L Thigh', { 
          name: 'Bip01 L Thigh', 
          parent: 'Bip01 Pelvis', 
          position: new THREE.Vector3(-0.2, 0.8, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 L Calf']
        }],
        ['Bip01 L Calf', { 
          name: 'Bip01 L Calf', 
          parent: 'Bip01 L Thigh', 
          position: new THREE.Vector3(-0.2, 0.4, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 L Foot']
        }],
        ['Bip01 L Foot', { 
          name: 'Bip01 L Foot', 
          parent: 'Bip01 L Calf', 
          position: new THREE.Vector3(-0.2, 0, 0),
          rotation: new THREE.Quaternion(),
          children: []
        }],
        ['Bip01 R Thigh', { 
          name: 'Bip01 R Thigh', 
          parent: 'Bip01 Pelvis', 
          position: new THREE.Vector3(0.2, 0.8, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 R Calf']
        }],
        ['Bip01 R Calf', { 
          name: 'Bip01 R Calf', 
          parent: 'Bip01 R Thigh', 
          position: new THREE.Vector3(0.2, 0.4, 0),
          rotation: new THREE.Quaternion(),
          children: ['Bip01 R Foot']
        }],
        ['Bip01 R Foot', { 
          name: 'Bip01 R Foot', 
          parent: 'Bip01 R Calf', 
          position: new THREE.Vector3(0.2, 0, 0),
          rotation: new THREE.Quaternion(),
          children: []
        }]
      ]),
      hierarchy: {
        rootBone: 'Bip01',
        depth: 4
      }
    };

    return bipSkeleton;
  }

  /**
   * 处理重定向结果
   * @param result 重定向结果
   */
  private async handleRetargetingResult(result: RetargetingResult): Promise<void> {
    if (result.success && result.animation) {
      console.log('✅ 动画重定向成功!');
      console.log(`  - 动画名称: ${result.animation.name}`);
      console.log(`  - 动画时长: ${result.animation.duration}秒`);
      console.log(`  - 轨道数量: ${result.animation.tracks.length}`);
      console.log(`  - 质量评分: ${result.qualityScore?.toFixed(2) || 'N/A'}`);

      if (result.warnings && result.warnings.length > 0) {
        console.log('⚠️ 警告信息:');
        result.warnings.forEach(warning => console.log(`  - ${warning}`));
      }

      // 应用动画到数字人
      await this.applyAnimationToDigitalHuman(result.animation);
    } else {
      console.error('❌ 动画重定向失败:', result.error);
    }
  }

  /**
   * 演示不同质量级别的重定向
   * @param bipAnimation BIP动画
   * @param standardSkeleton 标准骨骼
   */
  private async demonstrateQualityLevels(
    bipAnimation: BIPAnimation,
    standardSkeleton: StandardSkeletonData
  ): Promise<void> {
    console.log('🎚️ 演示不同质量级别的重定向...');

    const qualityLevels: Array<'low' | 'medium' | 'high' | 'ultra'> = ['low', 'medium', 'high', 'ultra'];

    for (const quality of qualityLevels) {
      console.log(`  测试质量级别: ${quality}`);
      
      const retargeter = new BIPAnimationRetargeter({
        qualityLevel: quality,
        debug: false
      });

      const startTime = performance.now();
      const result = await retargeter.retargetBIPToSkeleton(bipAnimation, standardSkeleton);
      const endTime = performance.now();

      if (result.success) {
        console.log(`    ✅ 成功 - 耗时: ${(endTime - startTime).toFixed(2)}ms, 质量: ${result.qualityScore?.toFixed(2)}`);
      } else {
        console.log(`    ❌ 失败 - ${result.error}`);
      }
    }
  }

  /**
   * 演示批量重定向
   * @param standardSkeleton 标准骨骼
   */
  private async demonstrateBatchRetargeting(standardSkeleton: StandardSkeletonData): Promise<void> {
    console.log('📦 演示批量重定向...');

    // 创建多个示例动画
    const animations = await Promise.all([
      this.createSampleBIPAnimation(),
      this.createRunAnimation(),
      this.createJumpAnimation()
    ]);

    console.log(`  批量处理 ${animations.length} 个动画...`);

    const results: RetargetingResult[] = [];
    const startTime = performance.now();

    for (const animation of animations) {
      const result = await this.retargeter.retargetBIPToSkeleton(animation, standardSkeleton);
      results.push(result);
    }

    const endTime = performance.now();
    const successCount = results.filter(r => r.success).length;
    const averageQuality = results
      .filter(r => r.success && r.qualityScore)
      .reduce((sum, r) => sum + r.qualityScore!, 0) / successCount;

    console.log(`  ✅ 批量处理完成:`);
    console.log(`    - 总耗时: ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`    - 成功率: ${successCount}/${animations.length} (${(successCount / animations.length * 100).toFixed(1)}%)`);
    console.log(`    - 平均质量: ${averageQuality.toFixed(2)}`);
  }

  /**
   * 创建跑步动画
   */
  private async createRunAnimation(): Promise<BIPAnimation> {
    const runAnimation: BIPAnimation = {
      name: 'Run_Cycle',
      duration: 1.0,
      frameRate: 30,
      positionTracks: [],
      rotationTracks: [],
      scaleTracks: [],
      sourceSkeleton: await this.createSampleBIPSkeleton()
    };

    return runAnimation;
  }

  /**
   * 创建跳跃动画
   */
  private async createJumpAnimation(): Promise<BIPAnimation> {
    const jumpAnimation: BIPAnimation = {
      name: 'Jump',
      duration: 1.5,
      frameRate: 30,
      positionTracks: [],
      rotationTracks: [],
      scaleTracks: [],
      sourceSkeleton: await this.createSampleBIPSkeleton()
    };

    return jumpAnimation;
  }

  /**
   * 应用动画到数字人
   * @param animation 标准动画
   */
  private async applyAnimationToDigitalHuman(animation: any): Promise<void> {
    console.log(`🎬 应用动画 ${animation.name} 到数字人...`);
    
    // 这里可以实现将动画应用到数字人的逻辑
    // 例如创建Three.js AnimationClip并播放
    
    console.log('✅ 动画应用完成');
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    console.log('🚀 BIP动画重定向示例正在运行...');
    
    // 监听重定向事件
    this.retargeter.on('retargetingStarted', (animation) => {
      console.log(`🔄 开始重定向动画: ${animation.name}`);
    });

    this.retargeter.on('retargetingCompleted', (result) => {
      console.log(`✅ 动画重定向完成，质量评分: ${result.qualityScore?.toFixed(2)}`);
    });

    this.retargeter.on('retargetingError', (error) => {
      console.error('❌ 动画重定向错误:', error);
    });

    console.log('💡 提示: 查看控制台输出了解重定向过程');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理BIP动画重定向示例...');

    if (this.retargeter) {
      this.retargeter.removeAllListeners();
    }

    console.log('✅ BIP动画重定向示例已清理');
  }
}

// 导出示例类
export default BIPAnimationRetargetingExample;

// 如果直接运行此文件，则启动示例
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const example = new BIPAnimationRetargetingExample();
    await example.run();
  });
}
