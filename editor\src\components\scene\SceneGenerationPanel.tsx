/**
 * 场景生成管理面板
 * 整合文本和语音场景生成功能的主面板
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Statistic,
  Row,
  Col,
  Timeline,
  Tag,
  Tooltip,
  Modal,
  List,
  Avatar
} from 'antd';
import {
  AudioOutlined,
  FileTextOutlined,
  HistoryOutlined,
  SettingOutlined,
  ExportOutlined,
  EyeOutlined,
  DeleteOutlined,
  StarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VoiceSceneGenerationPanel from './VoiceSceneGenerationPanel';
import TextSceneGenerationPanel from './TextSceneGenerationPanel';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

/**
 * 生成历史记录
 */
interface GenerationHistory {
  id: string;
  type: 'voice' | 'text';
  timestamp: number;
  description: string;
  scene: any;
  confidence: number;
  duration: number;
  favorite: boolean;
}

/**
 * 场景生成统计
 */
interface GenerationStats {
  totalGenerated: number;
  voiceGenerated: number;
  textGenerated: number;
  averageConfidence: number;
  averageDuration: number;
  favoriteCount: number;
}

/**
 * 场景生成面板属性
 */
interface SceneGenerationPanelProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 场景生成完成回调 */
  onSceneGenerated?: (scene: any, type: 'voice' | 'text') => void;
  /** 错误回调 */
  onError?: (error: string) => void;
}

/**
 * 场景生成管理面板
 */
const SceneGenerationPanel: React.FC<SceneGenerationPanelProps> = ({
  disabled = false,
  onSceneGenerated,
  onError
}) => {
  const { t } = useTranslation();

  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('text');
  const [generationHistory, setGenerationHistory] = useState<GenerationHistory[]>([]);
  const [stats, setStats] = useState<GenerationStats>({
    totalGenerated: 0,
    voiceGenerated: 0,
    textGenerated: 0,
    averageConfidence: 0,
    averageDuration: 0,
    favoriteCount: 0
  });
  const [showHistory, setShowHistory] = useState(false);
  const [showStats, setShowStats] = useState(false);

  /**
   * 初始化数据
   */
  useEffect(() => {
    loadGenerationHistory();
    calculateStats();
  }, []);

  /**
   * 加载生成历史
   */
  const loadGenerationHistory = () => {
    // 从本地存储加载历史记录
    const savedHistory = localStorage.getItem('sceneGenerationHistory');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setGenerationHistory(history);
      } catch (error) {
        console.error('加载历史记录失败:', error);
      }
    }
  };

  /**
   * 保存生成历史
   */
  const saveGenerationHistory = (history: GenerationHistory[]) => {
    try {
      localStorage.setItem('sceneGenerationHistory', JSON.stringify(history));
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  /**
   * 计算统计数据
   */
  const calculateStats = () => {
    if (generationHistory.length === 0) return;

    const voiceCount = generationHistory.filter(h => h.type === 'voice').length;
    const textCount = generationHistory.filter(h => h.type === 'text').length;
    const favoriteCount = generationHistory.filter(h => h.favorite).length;
    
    const totalConfidence = generationHistory.reduce((sum, h) => sum + h.confidence, 0);
    const totalDuration = generationHistory.reduce((sum, h) => sum + h.duration, 0);

    setStats({
      totalGenerated: generationHistory.length,
      voiceGenerated: voiceCount,
      textGenerated: textCount,
      averageConfidence: totalConfidence / generationHistory.length,
      averageDuration: totalDuration / generationHistory.length,
      favoriteCount
    });
  };

  /**
   * 处理场景生成完成
   */
  const handleSceneGenerated = (scene: any, type: 'voice' | 'text') => {
    const newHistory: GenerationHistory = {
      id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: Date.now(),
      description: scene.description || '',
      scene,
      confidence: scene.confidence || 0.8,
      duration: scene.generationTime || 3000,
      favorite: false
    };

    const updatedHistory = [newHistory, ...generationHistory].slice(0, 50); // 保留最近50条
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
    calculateStats();

    // 回调
    onSceneGenerated?.(scene, type);
  };

  /**
   * 处理语音场景生成
   */
  const handleVoiceSceneGenerated = (scene: any) => {
    handleSceneGenerated(scene, 'voice');
  };

  /**
   * 处理文本场景生成
   */
  const handleTextSceneGenerated = (scene: any) => {
    handleSceneGenerated(scene, 'text');
  };

  /**
   * 切换收藏状态
   */
  const toggleFavorite = (id: string) => {
    const updatedHistory = generationHistory.map(item =>
      item.id === id ? { ...item, favorite: !item.favorite } : item
    );
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
    calculateStats();
  };

  /**
   * 删除历史记录
   */
  const deleteHistoryItem = (id: string) => {
    const updatedHistory = generationHistory.filter(item => item.id !== id);
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
    calculateStats();
  };

  /**
   * 重新生成场景
   */
  const regenerateScene = (historyItem: GenerationHistory) => {
    // 切换到对应的标签页并填充描述
    setActiveTab(historyItem.type);
    
    // 这里应该触发对应面板的重新生成
    // 目前只是简单的提示
    Modal.info({
      title: '重新生成',
      content: `将使用描述"${historyItem.description}"重新生成场景`,
    });
  };

  /**
   * 导出场景
   */
  const exportScene = (historyItem: GenerationHistory) => {
    const dataStr = JSON.stringify(historyItem.scene, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `scene_${historyItem.id}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  /**
   * 渲染统计信息
   */
  const renderStats = () => (
    <Row gutter={16} style={{ marginBottom: '20px' }}>
      <Col span={6}>
        <Statistic
          title="总生成数"
          value={stats.totalGenerated}
          prefix={<EyeOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="语音生成"
          value={stats.voiceGenerated}
          prefix={<AudioOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="文本生成"
          value={stats.textGenerated}
          prefix={<FileTextOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="平均置信度"
          value={stats.averageConfidence}
          precision={2}
          suffix="%"
          formatter={(value) => `${((value as number) * 100).toFixed(1)}%`}
        />
      </Col>
    </Row>
  );

  /**
   * 渲染历史记录
   */
  const renderHistory = () => (
    <List
      dataSource={generationHistory.slice(0, 10)} // 显示最近10条
      renderItem={(item) => (
        <List.Item
          actions={[
            <Tooltip title={item.favorite ? '取消收藏' : '收藏'}>
              <Button
                type="text"
                icon={<StarOutlined />}
                style={{ color: item.favorite ? '#faad14' : undefined }}
                onClick={() => toggleFavorite(item.id)}
              />
            </Tooltip>,
            <Tooltip title="重新生成">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => regenerateScene(item)}
              />
            </Tooltip>,
            <Tooltip title="导出">
              <Button
                type="text"
                icon={<ExportOutlined />}
                onClick={() => exportScene(item)}
              />
            </Tooltip>,
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => deleteHistoryItem(item.id)}
              />
            </Tooltip>
          ]}
        >
          <List.Item.Meta
            avatar={
              <Avatar 
                icon={item.type === 'voice' ? <AudioOutlined /> : <FileTextOutlined />}
                style={{ backgroundColor: item.type === 'voice' ? '#52c41a' : '#1890ff' }}
              />
            }
            title={
              <div>
                <Text strong>{item.description.substring(0, 50)}...</Text>
                <Tag color={item.type === 'voice' ? 'green' : 'blue'} style={{ marginLeft: '8px' }}>
                  {item.type === 'voice' ? '语音' : '文本'}
                </Tag>
                {item.favorite && <StarOutlined style={{ color: '#faad14', marginLeft: '8px' }} />}
              </div>
            }
            description={
              <div>
                <Text type="secondary">
                  {new Date(item.timestamp).toLocaleString()}
                </Text>
                <Text type="secondary" style={{ marginLeft: '16px' }}>
                  置信度: {Math.round(item.confidence * 100)}%
                </Text>
                <Text type="secondary" style={{ marginLeft: '16px' }}>
                  耗时: {(item.duration / 1000).toFixed(1)}s
                </Text>
              </div>
            }
          />
        </List.Item>
      )}
    />
  );

  /**
   * 渲染时间线
   */
  const renderTimeline = () => (
    <Timeline>
      {generationHistory.slice(0, 5).map((item) => (
        <Timeline.Item
          key={item.id}
          color={item.type === 'voice' ? 'green' : 'blue'}
          dot={item.type === 'voice' ? <AudioOutlined /> : <FileTextOutlined />}
        >
          <div>
            <Text strong>{item.description.substring(0, 30)}...</Text>
            <br />
            <Text type="secondary">{new Date(item.timestamp).toLocaleString()}</Text>
            <Tag color={item.confidence > 0.8 ? 'green' : item.confidence > 0.6 ? 'orange' : 'red'} style={{ marginLeft: '8px' }}>
              {Math.round(item.confidence * 100)}%
            </Tag>
          </div>
        </Timeline.Item>
      ))}
    </Timeline>
  );

  return (
    <div className="scene-generation-panel">
      <Card
        title="智能场景生成"
        extra={
          <Space>
            <Tooltip title="查看统计">
              <Button
                icon={<SettingOutlined />}
                onClick={() => setShowStats(true)}
              />
            </Tooltip>
            <Tooltip title="查看历史">
              <Button
                icon={<HistoryOutlined />}
                onClick={() => setShowHistory(true)}
              />
            </Tooltip>
          </Space>
        }
      >
        {/* 统计信息 */}
        {renderStats()}

        {/* 主要功能标签页 */}
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
        >
          <TabPane
            tab={
              <span>
                <FileTextOutlined />
                文本生成
              </span>
            }
            key="text"
          >
            <TextSceneGenerationPanel
              disabled={disabled}
              onSceneGenerated={handleTextSceneGenerated}
              onError={onError}
            />
          </TabPane>
          
          <TabPane
            tab={
              <span>
                <AudioOutlined />
                语音生成
              </span>
            }
            key="voice"
          >
            <VoiceSceneGenerationPanel
              disabled={disabled}
              onSceneGenerated={handleVoiceSceneGenerated}
              onError={onError}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <HistoryOutlined />
                最近生成
              </span>
            }
            key="recent"
          >
            <div style={{ padding: '20px 0' }}>
              <Title level={4}>最近生成的场景</Title>
              {renderTimeline()}
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 统计信息模态框 */}
      <Modal
        title="生成统计"
        open={showStats}
        onCancel={() => setShowStats(false)}
        footer={null}
        width={600}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="总生成数"
              value={stats.totalGenerated}
              prefix={<EyeOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="收藏数"
              value={stats.favoriteCount}
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="语音生成"
              value={stats.voiceGenerated}
              prefix={<AudioOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="文本生成"
              value={stats.textGenerated}
              prefix={<FileTextOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="平均置信度"
              value={stats.averageConfidence}
              precision={2}
              suffix="%"
              formatter={(value) => `${((value as number) * 100).toFixed(1)}%`}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="平均耗时"
              value={stats.averageDuration / 1000}
              precision={1}
              suffix="秒"
            />
          </Col>
        </Row>
      </Modal>

      {/* 历史记录模态框 */}
      <Modal
        title="生成历史"
        open={showHistory}
        onCancel={() => setShowHistory(false)}
        footer={null}
        width={800}
      >
        {renderHistory()}
      </Modal>
    </div>
  );
};

export default SceneGenerationPanel;
