/**
 * M4数字人制作系统集成使用示例
 * 展示完整的数字人制作、编辑、管理和分享工作流
 */

import { M4SystemIntegration } from '../engine/src/avatar/M4SystemIntegration';
import { DigitalHumanCreationData } from '../engine/src/avatar/types/IntegrationTypes';

/**
 * M4系统集成示例类
 */
class M4SystemExample {
  private m4System: M4SystemIntegration;

  constructor() {
    // 配置M4系统
    const config = {
      integration: {
        enablePerformanceMonitoring: true,
        enableAutoOptimization: true,
        enableCloudSync: true,
        maxConcurrentDigitalHumans: 10,
        performanceThresholds: {
          maxMemoryUsage: 2048, // 2GB
          minFPS: 30,
          maxCPUUsage: 80
        }
      },
      optimization: {
        enabled: true,
        triggers: {
          memoryThreshold: 1024,
          fpsThreshold: 30,
          cpuThreshold: 80
        },
        actions: {
          enableLOD: true,
          reduceTextureQuality: true,
          limitConcurrentAnimations: true,
          enableBatching: true,
          cleanupInactive: true
        },
        schedule: {
          interval: 5,
          maintenanceWindow: {
            start: '02:00',
            end: '04:00'
          }
        }
      },
      marketplace: {
        apiBaseUrl: 'https://api.digitalhuman-marketplace.com',
        apiKey: 'your-api-key-here'
      },
      storage: {
        endpoint: 'localhost:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin'
      },
      testing: {
        enableAutoTesting: true,
        testInterval: 60 // 每小时运行一次测试
      }
    };

    this.m4System = new M4SystemIntegration(config);
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 系统事件
    this.m4System.on('systemInitialized', (data) => {
      console.log('✅ M4系统初始化完成:', data);
    });

    this.m4System.on('systemStarted', () => {
      console.log('🚀 M4系统启动成功');
    });

    this.m4System.on('digitalHumanCreated', (data) => {
      console.log('👤 数字人创建成功:', data.id);
    });

    this.m4System.on('healthStatusChanged', (status) => {
      console.log('🏥 系统健康状态变更:', status);
    });

    this.m4System.on('optimizationApplied', (data) => {
      console.log('⚡ 性能优化已应用:', data.suggestions.length, '个建议');
    });

    this.m4System.on('systemTestsCompleted', (results) => {
      console.log('🧪 系统测试完成:', results);
    });
  }

  /**
   * 运行完整示例
   */
  public async runExample(): Promise<void> {
    try {
      console.log('🎯 开始M4数字人制作系统集成示例...\n');

      // 1. 初始化和启动系统
      await this.initializeSystem();

      // 2. 演示各种数字人创建方式
      await this.demonstrateDigitalHumanCreation();

      // 3. 演示批量操作
      await this.demonstrateBatchOperations();

      // 4. 演示系统监控和优化
      await this.demonstrateSystemMonitoring();

      // 5. 演示测试和质量保证
      await this.demonstrateTestingAndQA();

      // 6. 演示用户体验功能
      await this.demonstrateUserExperience();

      // 7. 演示市场功能
      await this.demonstrateMarketplace();

      console.log('\n✅ M4系统集成示例完成！');

    } catch (error) {
      console.error('❌ 示例执行失败:', error);
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 初始化系统
   */
  private async initializeSystem(): Promise<void> {
    console.log('📋 1. 初始化M4系统...');
    
    await this.m4System.initialize();
    await this.m4System.start();
    
    const status = this.m4System.getSystemStatus();
    console.log(`   - 系统状态: ${status.systemHealth}`);
    console.log(`   - 活跃模块: ${status.activeModules.join(', ')}`);
    console.log('');
  }

  /**
   * 演示数字人创建
   */
  private async demonstrateDigitalHumanCreation(): Promise<void> {
    console.log('👤 2. 演示数字人创建功能...');

    // 2.1 从照片创建数字人
    console.log('   📸 从照片创建数字人...');
    const photoCreationData: DigitalHumanCreationData = {
      type: 'photo',
      userId: 'user-001',
      name: '照片数字人',
      photoData: {
        photo: new File([], 'portrait.jpg'),
        userId: 'user-001',
        options: {
          quality: 'high',
          generateBody: true,
          autoRigging: true
        }
      }
    };

    const photoResult = await this.m4System.createDigitalHuman(photoCreationData);
    console.log(`   - 创建结果: ${photoResult.success ? '成功' : '失败'}`);
    if (photoResult.digitalHumanId) {
      console.log(`   - 数字人ID: ${photoResult.digitalHumanId}`);
    }

    // 2.2 从文件上传创建数字人
    console.log('   📁 从文件上传创建数字人...');
    const fileCreationData: DigitalHumanCreationData = {
      type: 'upload',
      userId: 'user-002',
      name: '上传数字人',
      fileData: {
        file: new File([], 'digital-human.dhp'),
        userId: 'user-002'
      }
    };

    const fileResult = await this.m4System.createDigitalHuman(fileCreationData);
    console.log(`   - 创建结果: ${fileResult.success ? '成功' : '失败'}`);

    // 2.3 从BIP骨骼创建数字人
    console.log('   🦴 从BIP骨骼创建数字人...');
    const bipCreationData: DigitalHumanCreationData = {
      type: 'bip_skeleton',
      userId: 'user-003',
      name: 'BIP数字人',
      bipData: {
        bipFile: new File([], 'skeleton.bip'),
        userId: 'user-003',
        targetDigitalHuman: null, // 将在实际实现中提供
        options: {
          autoMapping: true,
          resolveConflicts: true,
          generateMissingBones: false
        }
      }
    };

    const bipResult = await this.m4System.createDigitalHuman(bipCreationData);
    console.log(`   - 创建结果: ${bipResult.success ? '成功' : '失败'}`);
    console.log('');
  }

  /**
   * 演示批量操作
   */
  private async demonstrateBatchOperations(): Promise<void> {
    console.log('📦 3. 演示批量操作功能...');

    const batchCreationData: DigitalHumanCreationData[] = [
      {
        type: 'photo',
        userId: 'batch-user-1',
        name: '批量数字人1',
        photoData: {
          photo: new File([], 'batch1.jpg'),
          userId: 'batch-user-1'
        }
      },
      {
        type: 'photo',
        userId: 'batch-user-2',
        name: '批量数字人2',
        photoData: {
          photo: new File([], 'batch2.jpg'),
          userId: 'batch-user-2'
        }
      },
      {
        type: 'photo',
        userId: 'batch-user-3',
        name: '批量数字人3',
        photoData: {
          photo: new File([], 'batch3.jpg'),
          userId: 'batch-user-3'
        }
      }
    ];

    const batchResult = await this.m4System.batchCreateDigitalHumans(batchCreationData);
    console.log(`   - 总数: ${batchResult.totalCount}`);
    console.log(`   - 成功: ${batchResult.successCount}`);
    console.log(`   - 失败: ${batchResult.failureCount}`);
    console.log(`   - 平均耗时: ${batchResult.summary.averageTime.toFixed(2)}ms`);
    console.log('');
  }

  /**
   * 演示系统监控和优化
   */
  private async demonstrateSystemMonitoring(): Promise<void> {
    console.log('📊 4. 演示系统监控和优化...');

    // 4.1 获取系统统计信息
    const statistics = this.m4System.getSystemStatistics();
    console.log('   📈 系统统计信息:');
    console.log(`   - 活跃数字人: ${statistics.activeDigitalHumans}`);
    console.log(`   - 内存使用: ${statistics.totalMemoryUsage}MB`);
    console.log(`   - 平均渲染时间: ${statistics.averageRenderTime.toFixed(2)}ms`);
    console.log(`   - 系统运行时间: ${(statistics.systemUptime / 1000).toFixed(0)}秒`);

    // 4.2 执行健康检查
    console.log('   🏥 执行系统健康检查...');
    const healthCheck = await this.m4System.performHealthCheck();
    console.log(`   - 整体健康状态: ${healthCheck.overall}`);
    console.log(`   - 存储服务: ${healthCheck.components.storage}`);
    console.log(`   - AI服务: ${healthCheck.components.ai}`);
    console.log(`   - BIP服务: ${healthCheck.components.bip}`);
    console.log(`   - 市场服务: ${healthCheck.components.marketplace}`);

    // 4.3 获取优化建议
    console.log('   💡 获取优化建议...');
    const suggestions = this.m4System.getOptimizationSuggestions();
    console.log(`   - 发现 ${suggestions.length} 个优化建议`);
    
    for (const suggestion of suggestions.slice(0, 3)) {
      console.log(`   - ${suggestion.type}: ${suggestion.description}`);
      
      // 应用高优先级建议
      if (suggestion.priority === 'high') {
        const applied = await this.m4System.applyOptimizationSuggestion(suggestion);
        console.log(`     应用结果: ${applied ? '成功' : '失败'}`);
      }
    }

    // 4.4 调整质量设置
    console.log('   ⚙️ 调整质量设置...');
    this.m4System.setQualityLevel('high');
    const currentQuality = this.m4System.getCurrentQuality();
    if (currentQuality) {
      console.log(`   - 渲染分辨率: ${currentQuality.rendering.resolution}`);
      console.log(`   - 纹理最大尺寸: ${currentQuality.textures.maxSize}px`);
      console.log(`   - 动画最大FPS: ${currentQuality.animations.maxFPS}`);
    }
    console.log('');
  }

  /**
   * 演示测试和质量保证
   */
  private async demonstrateTestingAndQA(): Promise<void> {
    console.log('🧪 5. 演示测试和质量保证...');

    console.log('   🔍 运行系统测试套件...');
    const testResults = await this.m4System.runSystemTests();
    
    console.log('   📋 测试结果汇总:');
    for (const [suiteName, result] of testResults) {
      console.log(`   - ${suiteName}: ${result.passedTests}/${result.totalTests} 通过`);
      console.log(`     耗时: ${result.duration}ms`);
      
      if (result.failedTests > 0) {
        console.log(`     失败的测试:`);
        result.results
          .filter(r => !r.passed)
          .forEach(r => console.log(`       - ${r.testName}: ${r.error}`));
      }
    }
    console.log('');
  }

  /**
   * 演示用户体验功能
   */
  private async demonstrateUserExperience(): Promise<void> {
    console.log('🎨 6. 演示用户体验功能...');
    
    // 这里会在实际实现中展示用户体验优化功能
    console.log('   ✨ 用户体验优化功能演示');
    console.log('   - 实时预览: 已启用');
    console.log('   - 撤销重做: 支持100步历史');
    console.log('   - 预设系统: 已加载默认预设');
    console.log('   - 自动保存: 每30秒保存一次');
    console.log('');
  }

  /**
   * 演示市场功能
   */
  private async demonstrateMarketplace(): Promise<void> {
    console.log('🛒 7. 演示数字人市场功能...');
    
    // 这里会在实际实现中展示市场功能
    console.log('   🔍 市场功能演示');
    console.log('   - 搜索功能: 已实现');
    console.log('   - 发布功能: 已实现');
    console.log('   - 下载功能: 已实现');
    console.log('   - 评价系统: 已实现');
    console.log('');
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    console.log('🧹 清理系统资源...');
    
    await this.m4System.stop();
    this.m4System.dispose();
    
    console.log('   ✅ 资源清理完成');
  }
}

/**
 * 运行示例
 */
async function runM4SystemExample(): Promise<void> {
  const example = new M4SystemExample();
  await example.runExample();
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runM4SystemExample().catch(console.error);
}

export { M4SystemExample, runM4SystemExample };
