/**
 * 数字人编辑器使用示例
 * 演示如何使用数字人编辑器进行完整的数字人制作流程
 */
import { DigitalHumanEditor } from '../engine/src/ui/DigitalHumanEditor';

/**
 * 数字人编辑器示例类
 */
class DigitalHumanEditorExample {
  private editor: DigitalHumanEditor | null = null;
  private container: HTMLElement;

  constructor() {
    // 创建编辑器容器
    this.container = this.createEditorContainer();
    document.body.appendChild(this.container);
  }

  /**
   * 创建编辑器容器
   */
  private createEditorContainer(): HTMLElement {
    const container = document.createElement('div');
    container.id = 'digital-human-editor-container';
    container.style.cssText = `
      width: 100vw;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      background: #f5f5f5;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // 添加基础样式
    this.addEditorStyles();

    return container;
  }

  /**
   * 添加编辑器样式
   */
  private addEditorStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      /* 数字人编辑器样式 */
      .digital-human-editor {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: #fff;
      }

      /* 工具栏样式 */
      .editor-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        background: #2c3e50;
        color: white;
        border-bottom: 1px solid #34495e;
      }

      .toolbar-left, .toolbar-center, .toolbar-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 内容区域样式 */
      .editor-content {
        display: flex;
        flex: 1;
        overflow: hidden;
      }

      /* 侧边栏样式 */
      .editor-sidebar {
        width: 320px;
        background: #ecf0f1;
        border-right: 1px solid #bdc3c7;
        display: flex;
        flex-direction: column;
      }

      .sidebar-tabs {
        display: flex;
        flex-direction: column;
        background: #34495e;
      }

      .tab-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: none;
        border: none;
        color: #bdc3c7;
        cursor: pointer;
        transition: all 0.2s;
        text-align: left;
      }

      .tab-btn:hover {
        background: #2c3e50;
        color: white;
      }

      .tab-btn.active {
        background: #3498db;
        color: white;
      }

      .sidebar-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }

      /* 主内容区域样式 */
      .editor-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #fff;
      }

      .main-content {
        flex: 1;
        overflow: hidden;
        position: relative;
      }

      /* 欢迎屏幕样式 */
      .welcome-screen {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .welcome-content {
        text-align: center;
        max-width: 500px;
        padding: 40px;
      }

      .welcome-content i {
        font-size: 64px;
        margin-bottom: 24px;
        opacity: 0.8;
      }

      .welcome-content h2 {
        font-size: 32px;
        margin-bottom: 16px;
        font-weight: 300;
      }

      .welcome-content p {
        font-size: 18px;
        margin-bottom: 32px;
        opacity: 0.9;
      }

      .quick-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
      }

      /* 状态栏样式 */
      .editor-status-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 16px;
        background: #ecf0f1;
        border-top: 1px solid #bdc3c7;
        font-size: 12px;
        color: #7f8c8d;
      }

      .status-right {
        display: flex;
        gap: 16px;
      }

      /* 按钮样式 */
      .btn {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
        text-decoration: none;
      }

      .btn-primary {
        background: #3498db;
        color: white;
      }

      .btn-primary:hover {
        background: #2980b9;
      }

      .btn-secondary {
        background: #95a5a6;
        color: white;
      }

      .btn-secondary:hover {
        background: #7f8c8d;
      }

      .btn-outline {
        background: transparent;
        color: #3498db;
        border: 1px solid #3498db;
      }

      .btn-outline:hover {
        background: #3498db;
        color: white;
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      /* 面板样式 */
      .multi-action-panel,
      .bip-upload-panel,
      .digital-human-upload-panel {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #ecf0f1;
        margin-bottom: 16px;
      }

      .panel-header h3 {
        margin: 0;
        font-size: 18px;
        color: #2c3e50;
      }

      /* 图标字体 */
      .icon-plus::before { content: '+'; }
      .icon-save::before { content: '💾'; }
      .icon-export::before { content: '📤'; }
      .icon-settings::before { content: '⚙️'; }
      .icon-overview::before { content: '📊'; }
      .icon-animation::before { content: '🎭'; }
      .icon-skeleton::before { content: '🦴'; }
      .icon-upload::before { content: '📁'; }
      .icon-camera::before { content: '📷'; }
      .icon-digital-human::before { content: '🤖'; }
      .icon-play::before { content: '▶️'; }
      .icon-stop::before { content: '⏹️'; }
      .icon-delete::before { content: '🗑️'; }
      .icon-close::before { content: '❌'; }
      .icon-check::before { content: '✅'; }
      .icon-error::before { content: '❌'; }
      .icon-folder::before { content: '📁'; }
      .icon-image::before { content: '🖼️'; }
      .icon-clear::before { content: '🧹'; }
      .icon-info::before { content: 'ℹ️'; }
      .icon-upload-cloud::before { content: '☁️'; }
      .icon-file::before { content: '📄'; }
      .icon-file-bip::before { content: '🦴'; }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .editor-sidebar {
          width: 280px;
        }
        
        .toolbar-center {
          display: none;
        }
      }

      @media (max-width: 480px) {
        .editor-content {
          flex-direction: column;
        }
        
        .editor-sidebar {
          width: 100%;
          height: 200px;
        }
        
        .sidebar-tabs {
          flex-direction: row;
          overflow-x: auto;
        }
        
        .tab-btn {
          white-space: nowrap;
        }
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 初始化编辑器
   */
  public async initialize(): Promise<void> {
    console.log('初始化数字人编辑器示例...');

    // 创建编辑器实例
    this.editor = new DigitalHumanEditor({
      container: this.container,
      debug: true,
      storageConfig: {
        provider: 'minio',
        config: {
          endpoint: 'localhost:9000',
          accessKey: 'minioadmin',
          secretKey: 'minioadmin',
          useSSL: false
        },
        debug: true
      },
      showToolbar: true,
      showStatusBar: true
    });

    // 设置编辑器事件监听器
    this.setupEditorEvents();

    // 启动编辑器
    await this.editor.start();

    console.log('数字人编辑器已启动');
  }

  /**
   * 设置编辑器事件监听器
   */
  private setupEditorEvents(): void {
    if (!this.editor) return;

    // 监听编辑器启动
    this.editor.on('editorStarted', () => {
      console.log('编辑器启动完成');
      this.showWelcomeMessage();
    });

    // 监听其他编辑器事件
    this.editor.on('digitalHumanCreated', (entity: any) => {
      console.log('数字人创建完成:', entity.name);
    });

    this.editor.on('digitalHumanImported', (result: any) => {
      console.log('数字人导入完成:', result);
    });
  }

  /**
   * 显示欢迎消息
   */
  private showWelcomeMessage(): void {
    console.log(`
    🎉 欢迎使用数字人制作系统！

    功能特性：
    📊 概览 - 查看数字人概况和统计信息
    🎭 动作管理 - 管理多个动作和动画融合
    🦴 BIP上传 - 上传和集成BIP骨骼文件
    📁 数字人导入 - 导入各种格式的数字人文件
    📷 照片生成 - 从照片生成3D数字人

    快速开始：
    1. 点击左侧标签切换不同功能
    2. 使用"照片生成"从照片创建数字人
    3. 使用"数字人导入"导入现有模型
    4. 使用"BIP上传"添加动作和动画
    5. 使用"动作管理"管理和播放动作

    提示：所有操作都会自动保存到云端存储
    `);
  }

  /**
   * 演示完整工作流程
   */
  public async demonstrateWorkflow(): Promise<void> {
    if (!this.editor) {
      console.error('编辑器未初始化');
      return;
    }

    console.log('\n🚀 开始演示数字人制作工作流程...');

    // 等待用户交互
    console.log(`
    📋 工作流程演示：

    1. 📷 照片生成数字人
       - 切换到"照片生成"标签
       - 上传人脸照片
       - 选择生成质量
       - 等待3D模型生成

    2. 🦴 添加骨骼和动画
       - 切换到"BIP上传"标签
       - 上传BIP骨骼文件
       - 系统自动集成骨骼

    3. 🎭 管理动作
       - 切换到"动作管理"标签
       - 查看导入的动作
       - 播放和测试动作
       - 解决动作冲突

    4. 💾 保存和导出
       - 使用工具栏保存按钮
       - 导出为标准格式

    请按照上述步骤操作编辑器界面！
    `);
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    try {
      await this.initialize();
      await this.demonstrateWorkflow();

      console.log('\n✅ 数字人编辑器示例运行成功！');
      console.log('编辑器将继续运行，您可以进行交互操作...');

    } catch (error) {
      console.error('❌ 示例运行失败:', error);
    }
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.editor) {
      this.editor.dispose();
      this.editor = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    console.log('编辑器示例资源已清理');
  }
}

// 页面加载完成后运行示例
document.addEventListener('DOMContentLoaded', () => {
  const example = new DigitalHumanEditorExample();
  example.run().catch(console.error);

  // 页面卸载时清理资源
  window.addEventListener('beforeunload', () => {
    example.dispose();
  });
});

// 导出示例类
export { DigitalHumanEditorExample };
