/**
 * 动画重定向器
 * 将动画从一个骨骼系统重定向到另一个骨骼系统
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPSkeletonData } from '../bip/BIPSkeletonParser';
import { StandardSkeletonData, StandardBoneType } from '../bip/BIPToStandardMapping';
import {
  BIPAnimation,
  AnimationClip,
  AnimationTrack,
  Keyframe,
  InterpolationType
} from './MultiActionFusionTypes';

/**
 * 重定向配置
 */
export interface RetargetingConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否保持原始时间 */
  preserveOriginalTiming?: boolean;
  /** 是否自动缩放 */
  autoScale?: boolean;
  /** 缩放因子 */
  scaleFactor?: number;
  /** 是否平滑插值 */
  smoothInterpolation?: boolean;
}

/**
 * 重定向结果
 */
export interface RetargetingResult {
  /** 是否成功 */
  success: boolean;
  /** 重定向后的动画 */
  animation?: AnimationClip;
  /** 警告信息 */
  warnings?: string[];
  /** 错误信息 */
  error?: string;
}

/**
 * 骨骼映射信息
 */
export interface BoneMapping {
  /** 源骨骼名称 */
  sourceBone: string;
  /** 目标骨骼类型 */
  targetBone: StandardBoneType;
  /** 变换矩阵 */
  transformMatrix: THREE.Matrix4;
  /** 是否需要缩放 */
  needsScaling: boolean;
}

/**
 * 动画重定向器
 */
export class AnimationRetargeter extends EventEmitter {
  /** 配置 */
  private config: RetargetingConfig;

  /** 骨骼映射缓存 */
  private boneMappingCache: Map<string, BoneMapping[]> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: RetargetingConfig = {}) {
    super();

    this.config = {
      debug: false,
      preserveOriginalTiming: true,
      autoScale: true,
      scaleFactor: 1.0,
      smoothInterpolation: true,
      ...config
    };
  }

  /**
   * 重定向动画
   * @param sourceAnimation 源动画
   * @param sourceSkeleton 源骨骼系统
   * @param targetSkeleton 目标骨骼系统
   * @returns 重定向结果
   */
  public async retargetAnimation(
    sourceAnimation: BIPAnimation,
    sourceSkeleton: BIPSkeletonData,
    targetSkeleton: StandardSkeletonData
  ): Promise<RetargetingResult> {
    try {
      if (this.config.debug) {
        console.log(`[AnimationRetargeter] 开始重定向动画: ${sourceAnimation.name}`);
      }

      this.emit('retargetingStarted', sourceAnimation);

      // 1. 创建骨骼映射
      const boneMapping = this.createBoneMapping(sourceSkeleton, targetSkeleton);

      // 2. 重定向动画轨道
      const retargetedTracks = await this.retargetAnimationTracks(
        sourceAnimation,
        boneMapping
      );

      // 3. 创建重定向后的动画
      const retargetedAnimation: AnimationClip = {
        name: sourceAnimation.name,
        duration: sourceAnimation.duration,
        tracks: retargetedTracks
      };

      // 4. 后处理
      await this.postProcessAnimation(retargetedAnimation);

      const result: RetargetingResult = {
        success: true,
        animation: retargetedAnimation,
        warnings: []
      };

      this.emit('retargetingCompleted', result);

      if (this.config.debug) {
        console.log(`[AnimationRetargeter] 动画重定向完成: ${sourceAnimation.name}`);
      }

      return result;

    } catch (error) {
      const result: RetargetingResult = {
        success: false,
        error: error.message
      };

      this.emit('retargetingError', error);

      if (this.config.debug) {
        console.error('[AnimationRetargeter] 动画重定向失败', error);
      }

      return result;
    }
  }

  /**
   * 创建骨骼映射
   * @param sourceSkeleton 源骨骼系统
   * @param targetSkeleton 目标骨骼系统
   * @returns 骨骼映射
   */
  private createBoneMapping(
    sourceSkeleton: BIPSkeletonData,
    targetSkeleton: StandardSkeletonData
  ): BoneMapping[] {
    const cacheKey = `${sourceSkeleton.version}_${targetSkeleton.rootBoneType}`;
    
    // 检查缓存
    if (this.boneMappingCache.has(cacheKey)) {
      return this.boneMappingCache.get(cacheKey)!;
    }

    const mappings: BoneMapping[] = [];

    // 为每个目标骨骼找到对应的源骨骼
    for (const [targetBoneType, targetBone] of targetSkeleton.bones) {
      const sourceBone = this.findCorrespondingSourceBone(
        targetBoneType,
        sourceSkeleton
      );

      if (sourceBone) {
        const mapping: BoneMapping = {
          sourceBone: sourceBone.name,
          targetBone: targetBoneType,
          transformMatrix: this.calculateTransformMatrix(sourceBone, targetBone),
          needsScaling: this.needsScaling(sourceBone, targetBone)
        };

        mappings.push(mapping);
      }
    }

    // 缓存映射
    this.boneMappingCache.set(cacheKey, mappings);

    if (this.config.debug) {
      console.log(`[AnimationRetargeter] 创建骨骼映射，共 ${mappings.length} 个映射`);
    }

    return mappings;
  }

  /**
   * 查找对应的源骨骼
   * @param targetBoneType 目标骨骼类型
   * @param sourceSkeleton 源骨骼系统
   * @returns 对应的源骨骼
   */
  private findCorrespondingSourceBone(
    targetBoneType: StandardBoneType,
    sourceSkeleton: BIPSkeletonData
  ): any {
    // 骨骼名称映射表
    const boneNameMapping: Record<StandardBoneType, string[]> = {
      [StandardBoneType.HIPS]: ['Bip01', 'Bip01 Pelvis'],
      [StandardBoneType.SPINE]: ['Bip01 Spine'],
      [StandardBoneType.CHEST]: ['Bip01 Spine1', 'Bip01 Spine2'],
      [StandardBoneType.NECK]: ['Bip01 Neck'],
      [StandardBoneType.HEAD]: ['Bip01 Head'],
      
      [StandardBoneType.LEFT_SHOULDER]: ['Bip01 L Clavicle'],
      [StandardBoneType.LEFT_UPPER_ARM]: ['Bip01 L UpperArm'],
      [StandardBoneType.LEFT_LOWER_ARM]: ['Bip01 L Forearm'],
      [StandardBoneType.LEFT_HAND]: ['Bip01 L Hand'],
      
      [StandardBoneType.RIGHT_SHOULDER]: ['Bip01 R Clavicle'],
      [StandardBoneType.RIGHT_UPPER_ARM]: ['Bip01 R UpperArm'],
      [StandardBoneType.RIGHT_LOWER_ARM]: ['Bip01 R Forearm'],
      [StandardBoneType.RIGHT_HAND]: ['Bip01 R Hand'],
      
      [StandardBoneType.LEFT_UPPER_LEG]: ['Bip01 L Thigh'],
      [StandardBoneType.LEFT_LOWER_LEG]: ['Bip01 L Calf'],
      [StandardBoneType.LEFT_FOOT]: ['Bip01 L Foot'],
      [StandardBoneType.LEFT_TOES]: ['Bip01 L Toe0'],
      
      [StandardBoneType.RIGHT_UPPER_LEG]: ['Bip01 R Thigh'],
      [StandardBoneType.RIGHT_LOWER_LEG]: ['Bip01 R Calf'],
      [StandardBoneType.RIGHT_FOOT]: ['Bip01 R Foot'],
      [StandardBoneType.RIGHT_TOES]: ['Bip01 R Toe0'],
      
      // 手指映射
      [StandardBoneType.LEFT_THUMB]: ['Bip01 L Finger0'],
      [StandardBoneType.LEFT_INDEX]: ['Bip01 L Finger1'],
      [StandardBoneType.LEFT_MIDDLE]: ['Bip01 L Finger2'],
      [StandardBoneType.LEFT_RING]: ['Bip01 L Finger3'],
      [StandardBoneType.LEFT_PINKY]: ['Bip01 L Finger4'],
      
      [StandardBoneType.RIGHT_THUMB]: ['Bip01 R Finger0'],
      [StandardBoneType.RIGHT_INDEX]: ['Bip01 R Finger1'],
      [StandardBoneType.RIGHT_MIDDLE]: ['Bip01 R Finger2'],
      [StandardBoneType.RIGHT_RING]: ['Bip01 R Finger3'],
      [StandardBoneType.RIGHT_PINKY]: ['Bip01 R Finger4'],
      
      [StandardBoneType.ROOT]: ['Bip01']
    };

    const possibleNames = boneNameMapping[targetBoneType] || [];

    // 查找匹配的骨骼
    for (const name of possibleNames) {
      const bone = sourceSkeleton.bones.find(b => b.name === name);
      if (bone) {
        return bone;
      }
    }

    return null;
  }

  /**
   * 计算变换矩阵
   * @param sourceBone 源骨骼
   * @param targetBone 目标骨骼
   * @returns 变换矩阵
   */
  private calculateTransformMatrix(sourceBone: any, targetBone: any): THREE.Matrix4 {
    // TODO: 实现变换矩阵计算
    // 这里需要根据源骨骼和目标骨骼的变换计算重定向矩阵
    return new THREE.Matrix4();
  }

  /**
   * 判断是否需要缩放
   * @param sourceBone 源骨骼
   * @param targetBone 目标骨骼
   * @returns 是否需要缩放
   */
  private needsScaling(sourceBone: any, targetBone: any): boolean {
    // TODO: 实现缩放判断逻辑
    return this.config.autoScale || false;
  }

  /**
   * 重定向动画轨道
   * @param sourceAnimation 源动画
   * @param boneMapping 骨骼映射
   * @returns 重定向后的轨道
   */
  private async retargetAnimationTracks(
    sourceAnimation: BIPAnimation,
    boneMapping: BoneMapping[]
  ): Promise<AnimationTrack[]> {
    const retargetedTracks: AnimationTrack[] = [];

    // 处理位置轨道
    for (const sourceTrack of sourceAnimation.positionTracks) {
      const mapping = boneMapping.find(m => m.sourceBone === sourceTrack.boneName);
      if (mapping) {
        const retargetedTrack = await this.retargetPositionTrack(sourceTrack, mapping);
        retargetedTracks.push(retargetedTrack);
      }
    }

    // 处理旋转轨道
    for (const sourceTrack of sourceAnimation.rotationTracks) {
      const mapping = boneMapping.find(m => m.sourceBone === sourceTrack.boneName);
      if (mapping) {
        const retargetedTrack = await this.retargetRotationTrack(sourceTrack, mapping);
        retargetedTracks.push(retargetedTrack);
      }
    }

    // 处理缩放轨道
    for (const sourceTrack of sourceAnimation.scaleTracks) {
      const mapping = boneMapping.find(m => m.sourceBone === sourceTrack.boneName);
      if (mapping) {
        const retargetedTrack = await this.retargetScaleTrack(sourceTrack, mapping);
        retargetedTracks.push(retargetedTrack);
      }
    }

    return retargetedTracks;
  }

  /**
   * 重定向位置轨道
   * @param sourceTrack 源轨道
   * @param mapping 骨骼映射
   * @returns 重定向后的轨道
   */
  private async retargetPositionTrack(
    sourceTrack: AnimationTrack,
    mapping: BoneMapping
  ): Promise<AnimationTrack> {
    const retargetedKeyframes: Keyframe[] = [];

    for (const keyframe of sourceTrack.keyframes) {
      const sourcePosition = keyframe.value as THREE.Vector3;
      
      // 应用变换矩阵
      const retargetedPosition = sourcePosition.clone().applyMatrix4(mapping.transformMatrix);
      
      // 应用缩放
      if (mapping.needsScaling) {
        retargetedPosition.multiplyScalar(this.config.scaleFactor || 1.0);
      }

      retargetedKeyframes.push({
        time: keyframe.time,
        value: retargetedPosition
      });
    }

    return {
      boneName: mapping.targetBone,
      keyframes: retargetedKeyframes,
      interpolation: sourceTrack.interpolation
    };
  }

  /**
   * 重定向旋转轨道
   * @param sourceTrack 源轨道
   * @param mapping 骨骼映射
   * @returns 重定向后的轨道
   */
  private async retargetRotationTrack(
    sourceTrack: AnimationTrack,
    mapping: BoneMapping
  ): Promise<AnimationTrack> {
    const retargetedKeyframes: Keyframe[] = [];

    for (const keyframe of sourceTrack.keyframes) {
      const sourceRotation = keyframe.value as THREE.Quaternion;
      
      // TODO: 实现旋转重定向逻辑
      // 这里需要根据骨骼方向差异调整旋转
      const retargetedRotation = sourceRotation.clone();

      retargetedKeyframes.push({
        time: keyframe.time,
        value: retargetedRotation
      });
    }

    return {
      boneName: mapping.targetBone,
      keyframes: retargetedKeyframes,
      interpolation: sourceTrack.interpolation
    };
  }

  /**
   * 重定向缩放轨道
   * @param sourceTrack 源轨道
   * @param mapping 骨骼映射
   * @returns 重定向后的轨道
   */
  private async retargetScaleTrack(
    sourceTrack: AnimationTrack,
    mapping: BoneMapping
  ): Promise<AnimationTrack> {
    const retargetedKeyframes: Keyframe[] = [];

    for (const keyframe of sourceTrack.keyframes) {
      const sourceScale = keyframe.value as THREE.Vector3;
      
      // 通常缩放不需要重定向，直接复制
      const retargetedScale = sourceScale.clone();

      retargetedKeyframes.push({
        time: keyframe.time,
        value: retargetedScale
      });
    }

    return {
      boneName: mapping.targetBone,
      keyframes: retargetedKeyframes,
      interpolation: sourceTrack.interpolation
    };
  }

  /**
   * 后处理动画
   * @param animation 动画
   */
  private async postProcessAnimation(animation: AnimationClip): Promise<void> {
    if (this.config.smoothInterpolation) {
      await this.smoothAnimationTracks(animation.tracks);
    }

    // TODO: 添加其他后处理步骤
    // 1. 关键帧优化
    // 2. 曲线平滑
    // 3. 循环检测和修复
  }

  /**
   * 平滑动画轨道
   * @param tracks 动画轨道
   */
  private async smoothAnimationTracks(tracks: AnimationTrack[]): Promise<void> {
    for (const track of tracks) {
      // TODO: 实现轨道平滑算法
      if (this.config.debug) {
        console.log(`[AnimationRetargeter] 平滑轨道: ${track.boneName}`);
      }
    }
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.boneMappingCache.clear();
    
    if (this.config.debug) {
      console.log('[AnimationRetargeter] 缓存已清理');
    }
  }

  /**
   * 销毁重定向器
   */
  public dispose(): void {
    this.clearCache();
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[AnimationRetargeter] 重定向器已销毁');
    }
  }
}
