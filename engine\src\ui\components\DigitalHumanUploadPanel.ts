/**
 * 数字人上传面板
 * 用于上传和导入数字人文件
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { 
  DigitalHumanImportSystem, 
  SupportedFileFormat, 
  ImportResult,
  FileValidationResult 
} from '../../avatar';

/**
 * 面板配置
 */
export interface DigitalHumanUploadPanelConfig {
  /** 容器元素 */
  container: HTMLElement;
  /** 支持的文件格式 */
  supportedFormats?: SupportedFileFormat[];
  /** 最大文件大小 (MB) */
  maxFileSize?: number;
  /** 是否显示预览 */
  showPreview?: boolean;
  /** 是否允许批量上传 */
  allowBatch?: boolean;
}

/**
 * 上传模式
 */
export enum UploadMode {
  SINGLE_FILE = 'single_file',
  PHOTO_TO_3D = 'photo_to_3d',
  BATCH_IMPORT = 'batch_import'
}

/**
 * 数字人上传面板
 */
export class DigitalHumanUploadPanel extends EventEmitter {
  /** 配置 */
  private config: DigitalHumanUploadPanelConfig;

  /** 容器元素 */
  private container: HTMLElement;

  /** 导入系统 */
  private importSystem: DigitalHumanImportSystem | null = null;

  /** 当前上传模式 */
  private currentMode: UploadMode = UploadMode.SINGLE_FILE;

  /** 待上传文件 */
  private pendingFiles: File[] = [];

  /** UI元素 */
  private elements: {
    modeSelector?: HTMLElement;
    uploadArea?: HTMLElement;
    filePreview?: HTMLElement;
    uploadButton?: HTMLElement;
    progressArea?: HTMLElement;
    resultsArea?: HTMLElement;
  } = {};

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: DigitalHumanUploadPanelConfig) {
    super();

    this.config = {
      supportedFormats: [
        SupportedFileFormat.GLTF,
        SupportedFileFormat.GLB,
        SupportedFileFormat.VRM,
        SupportedFileFormat.CUSTOM_DH
      ],
      maxFileSize: 200, // 200MB
      showPreview: true,
      allowBatch: true,
      ...config
    };

    this.container = config.container;
    this.initializeUI();
    this.setupEventListeners();
  }

  /**
   * 设置导入系统
   * @param system 导入系统
   */
  public setImportSystem(system: DigitalHumanImportSystem): void {
    this.importSystem = system;
    this.setupImportSystemEvents();
  }

  /**
   * 初始化UI
   */
  private initializeUI(): void {
    this.container.className = 'digital-human-upload-panel';
    this.container.innerHTML = `
      <div class="panel-header">
        <h3>数字人导入</h3>
        <div class="mode-selector" id="mode-selector">
          <button class="mode-btn active" data-mode="${UploadMode.SINGLE_FILE}">
            <i class="icon-file"></i>
            单文件导入
          </button>
          <button class="mode-btn" data-mode="${UploadMode.PHOTO_TO_3D}">
            <i class="icon-camera"></i>
            照片生成
          </button>
          ${this.config.allowBatch ? `
          <button class="mode-btn" data-mode="${UploadMode.BATCH_IMPORT}">
            <i class="icon-folder"></i>
            批量导入
          </button>
          ` : ''}
        </div>
      </div>

      <div class="upload-content">
        <!-- 单文件导入模式 -->
        <div class="upload-mode" id="mode-${UploadMode.SINGLE_FILE}">
          <div class="upload-area" id="upload-area-single">
            <div class="upload-zone">
              <i class="icon-upload-cloud"></i>
              <h4>上传数字人文件</h4>
              <p>支持 ${this.getSupportedFormatsText()} 格式</p>
              <p class="text-muted">最大文件大小: ${this.config.maxFileSize}MB</p>
              <button class="btn btn-primary" id="select-file-btn">
                <i class="icon-folder"></i>
                选择文件
              </button>
              <input type="file" id="file-input-single" 
                     accept="${this.getAcceptAttribute()}" 
                     style="display: none;">
            </div>
          </div>
        </div>

        <!-- 照片生成模式 -->
        <div class="upload-mode" id="mode-${UploadMode.PHOTO_TO_3D}" style="display: none;">
          <div class="upload-area" id="upload-area-photo">
            <div class="upload-zone">
              <i class="icon-camera"></i>
              <h4>上传人脸照片</h4>
              <p>上传清晰的正面人脸照片，系统将自动生成3D数字人</p>
              <p class="text-muted">建议使用高分辨率照片以获得更好效果</p>
              <button class="btn btn-primary" id="select-photo-btn">
                <i class="icon-image"></i>
                选择照片
              </button>
              <input type="file" id="file-input-photo" 
                     accept="image/*" 
                     style="display: none;">
            </div>
            <div class="photo-options" style="display: none;">
              <h5>生成选项</h5>
              <div class="option-group">
                <label>
                  <span>质量:</span>
                  <select id="quality-select">
                    <option value="medium">中等</option>
                    <option value="high">高质量</option>
                    <option value="ultra">超高质量</option>
                  </select>
                </label>
              </div>
              <div class="option-group">
                <label>
                  <input type="checkbox" id="generate-textures" checked>
                  <span>生成纹理贴图</span>
                </label>
              </div>
              <div class="option-group">
                <label>
                  <input type="checkbox" id="generate-normals" checked>
                  <span>生成法线贴图</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量导入模式 -->
        ${this.config.allowBatch ? `
        <div class="upload-mode" id="mode-${UploadMode.BATCH_IMPORT}" style="display: none;">
          <div class="upload-area" id="upload-area-batch">
            <div class="upload-zone">
              <i class="icon-folder"></i>
              <h4>批量导入数字人</h4>
              <p>选择多个数字人文件进行批量导入</p>
              <button class="btn btn-primary" id="select-files-btn">
                <i class="icon-folder"></i>
                选择多个文件
              </button>
              <input type="file" id="file-input-batch" 
                     accept="${this.getAcceptAttribute()}" 
                     multiple 
                     style="display: none;">
            </div>
          </div>
        </div>
        ` : ''}

        <!-- 文件预览区域 -->
        <div class="file-preview" id="file-preview" style="display: none;">
          <div class="preview-header">
            <h4>文件预览</h4>
            <button class="btn btn-outline" id="clear-files-btn">
              <i class="icon-clear"></i>
              清空
            </button>
          </div>
          <div class="preview-content" id="preview-content">
            <!-- 预览内容将在这里动态生成 -->
          </div>
          <div class="preview-actions">
            <button class="btn btn-success" id="start-import-btn">
              <i class="icon-upload"></i>
              开始导入
            </button>
          </div>
        </div>

        <!-- 进度区域 -->
        <div class="progress-area" id="progress-area" style="display: none;">
          <div class="progress-header">
            <h4>导入进度</h4>
            <button class="btn btn-outline" id="cancel-import-btn">
              <i class="icon-close"></i>
              取消
            </button>
          </div>
          <div class="progress-content">
            <div class="overall-progress">
              <div class="progress-bar">
                <div class="progress-fill" id="overall-progress-fill"></div>
              </div>
              <div class="progress-text">
                <span id="overall-progress-text">准备中...</span>
                <span id="overall-progress-percentage">0%</span>
              </div>
            </div>
            <div class="current-file" id="current-file">
              等待开始...
            </div>
          </div>
        </div>

        <!-- 结果区域 -->
        <div class="results-area" id="results-area" style="display: none;">
          <div class="results-header">
            <h4>导入结果</h4>
            <button class="btn btn-outline" id="close-results-btn">
              <i class="icon-close"></i>
              关闭
            </button>
          </div>
          <div class="results-content" id="results-content">
            <!-- 结果内容将在这里动态生成 -->
          </div>
          <div class="results-actions">
            <button class="btn btn-primary" id="import-more-btn">
              <i class="icon-plus"></i>
              继续导入
            </button>
          </div>
        </div>
      </div>
    `;

    // 获取UI元素引用
    this.elements.modeSelector = this.container.querySelector('#mode-selector') as HTMLElement;
    this.elements.uploadArea = this.container.querySelector('.upload-content') as HTMLElement;
    this.elements.filePreview = this.container.querySelector('#file-preview') as HTMLElement;
    this.elements.progressArea = this.container.querySelector('#progress-area') as HTMLElement;
    this.elements.resultsArea = this.container.querySelector('#results-area') as HTMLElement;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 模式切换
    const modeButtons = this.container.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mode = (e.target as HTMLElement).getAttribute('data-mode') as UploadMode;
        this.switchMode(mode);
      });
    });

    // 文件选择 - 单文件
    const selectFileBtn = this.container.querySelector('#select-file-btn') as HTMLButtonElement;
    const fileInputSingle = this.container.querySelector('#file-input-single') as HTMLInputElement;
    
    if (selectFileBtn && fileInputSingle) {
      selectFileBtn.addEventListener('click', () => fileInputSingle.click());
      fileInputSingle.addEventListener('change', (e) => this.handleSingleFileSelection(e));
    }

    // 文件选择 - 照片
    const selectPhotoBtn = this.container.querySelector('#select-photo-btn') as HTMLButtonElement;
    const fileInputPhoto = this.container.querySelector('#file-input-photo') as HTMLInputElement;
    
    if (selectPhotoBtn && fileInputPhoto) {
      selectPhotoBtn.addEventListener('click', () => fileInputPhoto.click());
      fileInputPhoto.addEventListener('change', (e) => this.handlePhotoSelection(e));
    }

    // 文件选择 - 批量
    const selectFilesBtn = this.container.querySelector('#select-files-btn') as HTMLButtonElement;
    const fileInputBatch = this.container.querySelector('#file-input-batch') as HTMLInputElement;
    
    if (selectFilesBtn && fileInputBatch) {
      selectFilesBtn.addEventListener('click', () => fileInputBatch.click());
      fileInputBatch.addEventListener('change', (e) => this.handleBatchFileSelection(e));
    }

    // 其他按钮
    const clearFilesBtn = this.container.querySelector('#clear-files-btn') as HTMLButtonElement;
    const startImportBtn = this.container.querySelector('#start-import-btn') as HTMLButtonElement;
    const cancelImportBtn = this.container.querySelector('#cancel-import-btn') as HTMLButtonElement;
    const closeResultsBtn = this.container.querySelector('#close-results-btn') as HTMLButtonElement;
    const importMoreBtn = this.container.querySelector('#import-more-btn') as HTMLButtonElement;

    if (clearFilesBtn) clearFilesBtn.addEventListener('click', () => this.clearFiles());
    if (startImportBtn) startImportBtn.addEventListener('click', () => this.startImport());
    if (cancelImportBtn) cancelImportBtn.addEventListener('click', () => this.cancelImport());
    if (closeResultsBtn) closeResultsBtn.addEventListener('click', () => this.closeResults());
    if (importMoreBtn) importMoreBtn.addEventListener('click', () => this.importMore());

    // 拖拽上传
    this.setupDragAndDrop();
  }

  /**
   * 设置拖拽上传
   */
  private setupDragAndDrop(): void {
    const uploadZones = this.container.querySelectorAll('.upload-zone');
    
    uploadZones.forEach(zone => {
      zone.addEventListener('dragover', (e) => this.handleDragOver(e));
      zone.addEventListener('drop', (e) => this.handleFileDrop(e));
      zone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
    });
  }

  /**
   * 设置导入系统事件
   */
  private setupImportSystemEvents(): void {
    if (!this.importSystem) return;

    // 监听导入进度
    this.importSystem.on('importProgress', (taskId: string, progress: any) => {
      this.updateImportProgress(progress);
    });

    // 监听导入完成
    this.importSystem.on('importCompleted', (taskId: string, result: ImportResult) => {
      this.handleImportCompleted(result);
    });

    // 监听导入错误
    this.importSystem.on('importError', (taskId: string, error: any) => {
      this.handleImportError(error);
    });
  }

  /**
   * 切换上传模式
   * @param mode 上传模式
   */
  private switchMode(mode: UploadMode): void {
    this.currentMode = mode;

    // 更新模式按钮状态
    const modeButtons = this.container.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
      btn.classList.toggle('active', btn.getAttribute('data-mode') === mode);
    });

    // 显示对应的上传区域
    const uploadModes = this.container.querySelectorAll('.upload-mode');
    uploadModes.forEach(modeElement => {
      const modeId = modeElement.id.replace('mode-', '');
      modeElement.style.display = modeId === mode ? 'block' : 'none';
    });

    // 清空当前文件
    this.clearFiles();
  }

  /**
   * 处理单文件选择
   * @param event 文件选择事件
   */
  private handleSingleFileSelection(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.addFiles([input.files[0]]);
    }
  }

  /**
   * 处理照片选择
   * @param event 文件选择事件
   */
  private handlePhotoSelection(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.addFiles([input.files[0]]);
      this.showPhotoOptions();
    }
  }

  /**
   * 处理批量文件选择
   * @param event 文件选择事件
   */
  private handleBatchFileSelection(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.addFiles(Array.from(input.files));
    }
  }

  /**
   * 处理拖拽悬停
   * @param event 拖拽事件
   */
  private handleDragOver(event: DragEvent): void {
    event.preventDefault();
    (event.currentTarget as HTMLElement).classList.add('drag-over');
  }

  /**
   * 处理文件拖拽放下
   * @param event 拖拽事件
   */
  private handleFileDrop(event: DragEvent): void {
    event.preventDefault();
    (event.currentTarget as HTMLElement).classList.remove('drag-over');

    const files = Array.from(event.dataTransfer?.files || []);
    this.addFiles(files);
  }

  /**
   * 处理拖拽离开
   * @param event 拖拽事件
   */
  private handleDragLeave(event: DragEvent): void {
    (event.currentTarget as HTMLElement).classList.remove('drag-over');
  }

  /**
   * 添加文件
   * @param files 文件列表
   */
  private async addFiles(files: File[]): Promise<void> {
    if (!this.importSystem) {
      this.showError('导入系统未初始化');
      return;
    }

    // 验证文件
    const validFiles: File[] = [];
    for (const file of files) {
      const validation = await this.importSystem.validateFile(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        this.showError(`文件 "${file.name}" 验证失败: ${validation.errors.join(', ')}`);
      }
    }

    if (validFiles.length === 0) return;

    // 根据模式限制文件数量
    if (this.currentMode === UploadMode.SINGLE_FILE && validFiles.length > 1) {
      this.pendingFiles = [validFiles[0]];
      this.showWarning('单文件模式只能选择一个文件');
    } else if (this.currentMode === UploadMode.PHOTO_TO_3D && validFiles.length > 1) {
      this.pendingFiles = [validFiles[0]];
      this.showWarning('照片生成模式只能选择一张照片');
    } else {
      this.pendingFiles = validFiles;
    }

    this.showFilePreview();
  }

  /**
   * 显示文件预览
   */
  private showFilePreview(): void {
    if (this.pendingFiles.length === 0) return;

    const previewContent = this.container.querySelector('#preview-content') as HTMLElement;
    
    const html = this.pendingFiles.map(file => this.renderFilePreview(file)).join('');
    previewContent.innerHTML = html;

    this.elements.filePreview!.style.display = 'block';
  }

  /**
   * 渲染文件预览
   * @param file 文件
   * @returns HTML字符串
   */
  private renderFilePreview(file: File): string {
    const sizeText = this.formatFileSize(file.size);
    const isImage = file.type.startsWith('image/');

    return `
      <div class="file-preview-item">
        <div class="file-icon">
          ${isImage ? 
            `<img src="${URL.createObjectURL(file)}" alt="预览" class="image-preview">` :
            `<i class="icon-file"></i>`
          }
        </div>
        <div class="file-info">
          <div class="file-name">${file.name}</div>
          <div class="file-details">
            <span class="file-size">${sizeText}</span>
            <span class="file-type">${file.type || '未知类型'}</span>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 显示照片选项
   */
  private showPhotoOptions(): void {
    const photoOptions = this.container.querySelector('.photo-options') as HTMLElement;
    if (photoOptions) {
      photoOptions.style.display = 'block';
    }
  }

  /**
   * 开始导入
   */
  private async startImport(): Promise<void> {
    if (!this.importSystem || this.pendingFiles.length === 0) return;

    this.showProgress();

    try {
      for (let i = 0; i < this.pendingFiles.length; i++) {
        const file = this.pendingFiles[i];
        
        this.updateCurrentFile(`正在导入: ${file.name} (${i + 1}/${this.pendingFiles.length})`);

        const options = this.getImportOptions();
        await this.importSystem.importDigitalHuman(file, options);
      }

    } catch (error) {
      this.handleImportError(error);
    }
  }

  /**
   * 获取导入选项
   * @returns 导入选项
   */
  private getImportOptions(): any {
    const options: any = {};

    if (this.currentMode === UploadMode.PHOTO_TO_3D) {
      const qualitySelect = this.container.querySelector('#quality-select') as HTMLSelectElement;
      const generateTextures = this.container.querySelector('#generate-textures') as HTMLInputElement;
      const generateNormals = this.container.querySelector('#generate-normals') as HTMLInputElement;

      options.quality = qualitySelect?.value || 'medium';
      options.generateTextures = generateTextures?.checked !== false;
      options.generateNormalMaps = generateNormals?.checked !== false;
    }

    return options;
  }

  /**
   * 获取支持格式文本
   * @returns 格式文本
   */
  private getSupportedFormatsText(): string {
    return this.config.supportedFormats?.map(format => format.toUpperCase()).join(', ') || '';
  }

  /**
   * 获取accept属性
   * @returns accept属性值
   */
  private getAcceptAttribute(): string {
    const extensions = this.config.supportedFormats?.map(format => `.${format}`) || [];
    return extensions.join(',');
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化的大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 清空文件
   */
  private clearFiles(): void {
    this.pendingFiles = [];
    this.elements.filePreview!.style.display = 'none';
  }

  /**
   * 显示进度
   */
  private showProgress(): void {
    this.elements.filePreview!.style.display = 'none';
    this.elements.progressArea!.style.display = 'block';
  }

  /**
   * 更新导入进度
   * @param progress 进度信息
   */
  private updateImportProgress(progress: any): void {
    const progressFill = this.container.querySelector('#overall-progress-fill') as HTMLElement;
    const progressText = this.container.querySelector('#overall-progress-text') as HTMLElement;
    const progressPercentage = this.container.querySelector('#overall-progress-percentage') as HTMLElement;

    if (progressFill) progressFill.style.width = `${progress.progress}%`;
    if (progressText) progressText.textContent = progress.message;
    if (progressPercentage) progressPercentage.textContent = `${progress.progress}%`;
  }

  /**
   * 更新当前文件
   * @param text 文件信息
   */
  private updateCurrentFile(text: string): void {
    const currentFile = this.container.querySelector('#current-file') as HTMLElement;
    if (currentFile) currentFile.textContent = text;
  }

  /**
   * 处理导入完成
   * @param result 导入结果
   */
  private handleImportCompleted(result: ImportResult): void {
    this.hideProgress();
    this.showResults([result]);
    this.emit('importCompleted', result);
  }

  /**
   * 处理导入错误
   * @param error 错误
   */
  private handleImportError(error: any): void {
    this.hideProgress();
    this.showError(`导入失败: ${error.message}`);
    this.emit('importError', error);
  }

  /**
   * 显示结果
   * @param results 结果列表
   */
  private showResults(results: ImportResult[]): void {
    const resultsContent = this.container.querySelector('#results-content') as HTMLElement;
    
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    resultsContent.innerHTML = `
      <div class="results-summary">
        <div class="result-stat success">
          <i class="icon-check"></i>
          <span>成功: ${successCount}</span>
        </div>
        <div class="result-stat error">
          <i class="icon-error"></i>
          <span>失败: ${errorCount}</span>
        </div>
      </div>
      <div class="results-list">
        ${results.map(result => this.renderResultItem(result)).join('')}
      </div>
    `;

    this.elements.resultsArea!.style.display = 'block';
  }

  /**
   * 渲染结果项
   * @param result 结果
   * @returns HTML字符串
   */
  private renderResultItem(result: ImportResult): string {
    const statusClass = result.success ? 'success' : 'error';
    const statusIcon = result.success ? 'icon-check' : 'icon-error';

    return `
      <div class="result-item ${statusClass}">
        <i class="${statusIcon}"></i>
        <div class="result-info">
          <div class="result-title">
            ${result.success ? '导入成功' : '导入失败'}
          </div>
          ${result.error ? `<div class="result-error">${result.error}</div>` : ''}
          ${result.digitalHuman ? `<div class="result-details">数字人ID: ${result.digitalHuman.id}</div>` : ''}
        </div>
      </div>
    `;
  }

  /**
   * 取消导入
   */
  private cancelImport(): void {
    // TODO: 实现取消导入逻辑
    this.hideProgress();
  }

  /**
   * 关闭结果
   */
  private closeResults(): void {
    this.elements.resultsArea!.style.display = 'none';
    this.clearFiles();
  }

  /**
   * 继续导入
   */
  private importMore(): void {
    this.closeResults();
    this.switchMode(this.currentMode);
  }

  /**
   * 隐藏进度
   */
  private hideProgress(): void {
    this.elements.progressArea!.style.display = 'none';
  }

  /**
   * 显示错误
   * @param message 错误消息
   */
  private showError(message: string): void {
    // TODO: 实现错误提示
    console.error(message);
  }

  /**
   * 显示警告
   * @param message 警告消息
   */
  private showWarning(message: string): void {
    // TODO: 实现警告提示
    console.warn(message);
  }

  /**
   * 销毁面板
   */
  public dispose(): void {
    this.pendingFiles = [];
    this.removeAllListeners();
  }
}
