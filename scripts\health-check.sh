#!/bin/bash

# RAG数字人交互系统健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
API_GATEWAY_URL="http://localhost:8080"
KNOWLEDGE_SERVICE_URL="http://localhost:3000"
BINDING_SERVICE_URL="http://localhost:3001"
RAG_ENGINE_URL="http://localhost:3002"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
REDIS_HOST="localhost"
REDIS_PORT="6379"
MINIO_URL="http://localhost:9000"
CHROMA_URL="http://localhost:8000"

# 检查HTTP服务
check_http_service() {
    local service_name=$1
    local url=$2
    local endpoint=${3:-"/health"}
    
    log_info "检查 $service_name..."
    
    if curl -f -s "$url$endpoint" > /dev/null; then
        log_success "$service_name 运行正常"
        return 0
    else
        log_error "$service_name 检查失败"
        return 1
    fi
}

# 检查PostgreSQL
check_postgres() {
    log_info "检查PostgreSQL..."
    
    if pg_isready -h $POSTGRES_HOST -p $POSTGRES_PORT > /dev/null 2>&1; then
        log_success "PostgreSQL 运行正常"
        return 0
    else
        log_error "PostgreSQL 检查失败"
        return 1
    fi
}

# 检查Redis
check_redis() {
    log_info "检查Redis..."
    
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping > /dev/null 2>&1; then
        log_success "Redis 运行正常"
        return 0
    else
        log_error "Redis 检查失败"
        return 1
    fi
}

# 检查MinIO
check_minio() {
    log_info "检查MinIO..."
    
    if curl -f -s "$MINIO_URL/minio/health/live" > /dev/null; then
        log_success "MinIO 运行正常"
        return 0
    else
        log_error "MinIO 检查失败"
        return 1
    fi
}

# 检查Chroma
check_chroma() {
    log_info "检查Chroma..."
    
    if curl -f -s "$CHROMA_URL/api/v1/heartbeat" > /dev/null; then
        log_success "Chroma 运行正常"
        return 0
    else
        log_error "Chroma 检查失败"
        return 1
    fi
}

# 检查API功能
check_api_functionality() {
    log_info "检查API功能..."
    
    # 检查API文档
    if curl -f -s "$API_GATEWAY_URL/api/docs" > /dev/null; then
        log_success "API文档可访问"
    else
        log_error "API文档不可访问"
        return 1
    fi
    
    # 检查知识库列表API
    if curl -f -s "$API_GATEWAY_URL/api/knowledge-bases" \
        -H "Authorization: Bearer test-token" > /dev/null; then
        log_success "知识库API可访问"
    else
        log_warning "知识库API需要认证或不可访问"
    fi
    
    # 检查数字人列表API
    if curl -f -s "$API_GATEWAY_URL/api/digital-humans" \
        -H "Authorization: Bearer test-token" > /dev/null; then
        log_success "数字人API可访问"
    else
        log_warning "数字人API需要认证或不可访问"
    fi
    
    return 0
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存使用率
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        log_warning "内存使用率过高: ${memory_usage}%"
    else
        log_success "内存使用率正常: ${memory_usage}%"
    fi
    
    # 检查磁盘使用率
    disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
    else
        log_success "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查CPU负载
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    cpu_cores=$(nproc)
    if (( $(echo "$load_avg > $cpu_cores" | bc -l) )); then
        log_warning "CPU负载过高: $load_avg (核心数: $cpu_cores)"
    else
        log_success "CPU负载正常: $load_avg (核心数: $cpu_cores)"
    fi
}

# 检查Docker容器状态
check_docker_containers() {
    log_info "检查Docker容器状态..."
    
    if ! command -v docker &> /dev/null; then
        log_warning "Docker未安装，跳过容器检查"
        return 0
    fi
    
    containers=("rag-postgres" "rag-redis" "rag-minio" "rag-chroma" 
                "rag-knowledge-service" "rag-binding-service" 
                "rag-engine" "rag-api-gateway" "rag-nginx")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "unknown")
            if [ "$status" = "healthy" ] || [ "$status" = "unknown" ]; then
                log_success "$container 运行正常"
            else
                log_error "$container 状态异常: $status"
            fi
        else
            log_error "$container 未运行"
        fi
    done
}

# 生成健康检查报告
generate_report() {
    local total_checks=$1
    local failed_checks=$2
    
    log_info "========== 健康检查报告 =========="
    log_info "检查时间: $(date)"
    log_info "总检查项: $total_checks"
    log_info "失败项: $failed_checks"
    log_info "成功率: $(( (total_checks - failed_checks) * 100 / total_checks ))%"
    
    if [ $failed_checks -eq 0 ]; then
        log_success "所有检查项通过!"
        return 0
    else
        log_error "有 $failed_checks 项检查失败!"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始系统健康检查..."
    
    total_checks=0
    failed_checks=0
    
    # 基础设施检查
    ((total_checks++))
    check_postgres || ((failed_checks++))
    
    ((total_checks++))
    check_redis || ((failed_checks++))
    
    ((total_checks++))
    check_minio || ((failed_checks++))
    
    ((total_checks++))
    check_chroma || ((failed_checks++))
    
    # 应用服务检查
    ((total_checks++))
    check_http_service "知识库服务" "$KNOWLEDGE_SERVICE_URL" || ((failed_checks++))
    
    ((total_checks++))
    check_http_service "绑定服务" "$BINDING_SERVICE_URL" || ((failed_checks++))
    
    ((total_checks++))
    check_http_service "RAG引擎" "$RAG_ENGINE_URL" || ((failed_checks++))
    
    ((total_checks++))
    check_http_service "API网关" "$API_GATEWAY_URL" || ((failed_checks++))
    
    # API功能检查
    ((total_checks++))
    check_api_functionality || ((failed_checks++))
    
    # 系统资源检查
    check_system_resources
    
    # Docker容器检查
    check_docker_containers
    
    # 生成报告
    generate_report $total_checks $failed_checks
}

# 执行主函数
main "$@"
