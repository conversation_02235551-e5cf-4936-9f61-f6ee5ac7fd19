/**
 * 数字人导入系统使用示例
 * 演示如何使用数字人导入系统上传和导入数字人文件
 */
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { StorageService } from '../engine/src/storage/StorageService';
import {
  DigitalHumanImportSystem,
  SupportedFileFormat,
  DigitalHumanConverter,
  DigitalHumanFileValidator,
  DIGITAL_HUMAN_FORMAT_VERSION
} from '../engine/src/avatar';

/**
 * 数字人导入示例类
 */
class DigitalHumanImportExample {
  private engine: Engine;
  private world: World;
  private storageService: StorageService;
  private importSystem: DigitalHumanImportSystem;
  private converter: DigitalHumanConverter;

  constructor() {
    // 初始化引擎
    this.engine = new Engine();
    this.world = this.engine.getWorld();

    // 初始化存储服务
    this.storageService = new StorageService({
      provider: 'minio',
      config: {
        endpoint: 'localhost:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin',
        useSSL: false
      },
      debug: true
    });

    // 初始化导入系统
    this.importSystem = new DigitalHumanImportSystem(this.world, this.storageService, {
      debug: true,
      maxFileSize: 200, // 200MB
      autoFix: true,
      strictValidation: false,
      supportedFormats: [
        SupportedFileFormat.GLTF,
        SupportedFileFormat.GLB,
        SupportedFileFormat.VRM,
        SupportedFileFormat.CUSTOM_DH
      ]
    });

    // 初始化转换器
    this.converter = new DigitalHumanConverter({
      debug: true,
      optimize: true,
      compressionLevel: 7
    });

    // 添加系统到世界
    this.world.addSystem(this.importSystem);
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    console.log('初始化数字人导入示例...');

    // 初始化存储服务
    await this.storageService.initialize();

    // 设置事件监听器
    this.setupEventListeners();

    console.log('数字人导入系统已准备就绪');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听导入进度
    this.importSystem.on('importProgress', (taskId: string, progress: any) => {
      console.log(`导入进度 [${taskId}]: ${progress.stage} - ${progress.progress}% - ${progress.message}`);
    });

    // 监听导入完成
    this.importSystem.on('importCompleted', (taskId: string, result: any) => {
      console.log(`导入完成 [${taskId}]:`, {
        success: result.success,
        digitalHumanId: result.digitalHuman?.id,
        assetCount: result.assetIds?.length || 0,
        warnings: result.warnings?.length || 0
      });
    });

    // 监听导入错误
    this.importSystem.on('importError', (taskId: string, error: any) => {
      console.error(`导入失败 [${taskId}]:`, error.message);
    });

    // 监听转换事件
    this.converter.on('conversionStarted', (from: string, to: string) => {
      console.log(`开始转换: ${from} -> ${to}`);
    });

    this.converter.on('conversionCompleted', (result: any) => {
      console.log('转换完成:', {
        success: result.success,
        format: result.format,
        warnings: result.warnings?.length || 0
      });
    });

    this.converter.on('conversionError', (error: any) => {
      console.error('转换失败:', error.message);
    });
  }

  /**
   * 演示文件验证
   */
  public async demonstrateFileValidation(): Promise<void> {
    console.log('\n=== 文件验证演示 ===');

    // 创建模拟文件
    const mockFiles = this.createMockFiles();

    for (const file of mockFiles) {
      console.log(`\n验证文件: ${file.name}`);
      
      try {
        const validation = await this.importSystem.validateFile(file);
        
        console.log('验证结果:', {
          isValid: validation.isValid,
          format: validation.format,
          fileSize: `${(validation.fileSize / 1024 / 1024).toFixed(2)} MB`,
          errors: validation.errors.length,
          warnings: validation.warnings.length
        });

        if (validation.errors.length > 0) {
          console.log('错误:', validation.errors);
        }

        if (validation.warnings.length > 0) {
          console.log('警告:', validation.warnings);
        }

      } catch (error) {
        console.error('验证失败:', error.message);
      }
    }
  }

  /**
   * 演示数字人导入
   */
  public async demonstrateDigitalHumanImport(): Promise<void> {
    console.log('\n=== 数字人导入演示 ===');

    // 创建有效的数字人文件
    const dhFile = this.createValidDigitalHumanFile();
    const file = new File([JSON.stringify(dhFile)], 'example-digital-human.dh', {
      type: 'application/json'
    });

    console.log(`导入数字人文件: ${file.name}`);

    try {
      const result = await this.importSystem.importDigitalHuman(file, {
        autoFix: true,
        generateThumbnail: true
      });

      if (result.success) {
        console.log('导入成功!');
        console.log('数字人ID:', result.digitalHuman?.id);
        console.log('资产ID列表:', result.assetIds);
        
        if (result.warnings && result.warnings.length > 0) {
          console.log('警告:', result.warnings);
        }
      } else {
        console.error('导入失败:', result.error);
      }

    } catch (error) {
      console.error('导入异常:', error.message);
    }
  }

  /**
   * 演示格式转换
   */
  public async demonstrateFormatConversion(): Promise<void> {
    console.log('\n=== 格式转换演示 ===');

    // 创建模拟GLTF数据
    const mockGLTF = this.createMockGLTFData();
    const metadata = {
      name: 'Converted Digital Human',
      creator: 'Example Creator',
      description: 'Converted from GLTF format'
    };

    console.log('转换GLTF到数字人格式...');

    try {
      const result = await this.converter.convertFromGLTF(mockGLTF, metadata);

      if (result.success && result.data) {
        console.log('转换成功!');
        console.log('数字人名称:', result.data.metadata.name);
        console.log('模型格式:', result.data.model.format);
        console.log('包含骨骼:', !!result.data.skeleton);
        console.log('包含动画:', !!result.data.animations);

        // 验证转换结果
        const validation = DigitalHumanFileValidator.validate(result.data);
        console.log('验证结果:', {
          isValid: validation.isValid,
          errors: validation.errors.length,
          warnings: validation.warnings.length
        });

        // 演示反向转换
        console.log('\n转换数字人格式到GLTF...');
        const reverseResult = await this.converter.convertToGLTF(result.data);
        
        if (reverseResult.success) {
          console.log('反向转换成功!');
        } else {
          console.error('反向转换失败:', reverseResult.error);
        }

      } else {
        console.error('转换失败:', result.error);
      }

    } catch (error) {
      console.error('转换异常:', error.message);
    }
  }

  /**
   * 创建模拟文件
   */
  private createMockFiles(): File[] {
    const files: File[] = [];

    // 有效的数字人文件
    const validDH = this.createValidDigitalHumanFile();
    files.push(new File([JSON.stringify(validDH)], 'valid-digital-human.dh', {
      type: 'application/json'
    }));

    // 无效的数字人文件
    const invalidDH = { invalid: 'data' };
    files.push(new File([JSON.stringify(invalidDH)], 'invalid-digital-human.dh', {
      type: 'application/json'
    }));

    // 过大的文件
    const largeData = 'x'.repeat(250 * 1024 * 1024); // 250MB
    files.push(new File([largeData], 'large-file.dh', {
      type: 'application/json'
    }));

    // 不支持的格式
    files.push(new File(['test'], 'unsupported.xyz', {
      type: 'application/octet-stream'
    }));

    return files;
  }

  /**
   * 创建有效的数字人文件
   */
  private createValidDigitalHumanFile(): any {
    return DigitalHumanFileValidator.createDefault('Example Digital Human', 'Example Creator');
  }

  /**
   * 创建模拟GLTF数据
   */
  private createMockGLTFData(): any {
    return {
      asset: {
        version: '2.0',
        generator: 'Mock Generator'
      },
      scenes: [
        {
          nodes: [0]
        }
      ],
      nodes: [
        {
          name: 'Root',
          mesh: 0
        }
      ],
      meshes: [
        {
          name: 'ExampleMesh',
          primitives: [
            {
              attributes: {
                POSITION: 0
              },
              material: 0
            }
          ]
        }
      ],
      materials: [
        {
          name: 'ExampleMaterial',
          pbrMetallicRoughness: {
            baseColorFactor: [1.0, 1.0, 1.0, 1.0],
            metallicFactor: 0.0,
            roughnessFactor: 1.0
          }
        }
      ],
      accessors: [
        {
          bufferView: 0,
          componentType: 5126,
          count: 3,
          type: 'VEC3'
        }
      ],
      bufferViews: [
        {
          buffer: 0,
          byteLength: 36,
          byteOffset: 0
        }
      ],
      buffers: [
        {
          byteLength: 36,
          uri: 'data:application/octet-stream;base64,AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=='
        }
      ]
    };
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    try {
      await this.initialize();
      
      await this.demonstrateFileValidation();
      await this.demonstrateFormatConversion();
      await this.demonstrateDigitalHumanImport();
      
      console.log('\n数字人导入示例运行完成！');
      
      // 启动引擎更新循环
      this.startUpdateLoop();
      
    } catch (error) {
      console.error('示例运行失败:', error);
    }
  }

  /**
   * 启动更新循环
   */
  private startUpdateLoop(): void {
    let lastTime = performance.now();
    
    const update = (currentTime: number) => {
      const deltaTime = (currentTime - lastTime) / 1000;
      lastTime = currentTime;
      
      // 更新引擎
      this.engine.update(deltaTime);
      
      // 继续下一帧
      requestAnimationFrame(update);
    };
    
    requestAnimationFrame(update);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.importSystem.dispose();
    this.converter.dispose();
    this.storageService.dispose();
    this.engine.dispose();
    
    console.log('示例资源已清理');
  }
}

// 运行示例
const example = new DigitalHumanImportExample();
example.run().catch(console.error);

// 导出示例类
export { DigitalHumanImportExample };
