/**
 * 语音识别服务
 * 集成Web Speech API和专业语音识别服务，支持实时语音输入、多语言识别、语音命令处理
 */

/**
 * 语音识别配置接口
 */
export interface SpeechRecognitionConfig {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  grammars?: string[];
  noiseReduction: boolean;
  echoCancellation: boolean;
  autoGainControl: boolean;
  sampleRate: number;
  provider: 'web-speech' | 'azure' | 'google' | 'baidu';
  apiKey?: string;
  region?: string;
}

/**
 * 语音识别结果接口
 */
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  alternatives: SpeechAlternative[];
  timestamp: Date;
  duration: number;
  language: string;
}

/**
 * 语音识别备选结果接口
 */
export interface SpeechAlternative {
  transcript: string;
  confidence: number;
}

/**
 * 语音命令接口
 */
export interface VoiceCommand {
  id: string;
  patterns: string[];
  action: string;
  parameters?: Record<string, any>;
  confidence: number;
  enabled: boolean;
}

/**
 * 语音识别状态枚举
 */
export enum SpeechRecognitionState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  ERROR = 'error',
  STOPPED = 'stopped'
}

/**
 * 语音识别事件类型
 */
export enum SpeechRecognitionEventType {
  START = 'start',
  END = 'end',
  RESULT = 'result',
  ERROR = 'error',
  NO_MATCH = 'nomatch',
  SOUND_START = 'soundstart',
  SOUND_END = 'soundend',
  SPEECH_START = 'speechstart',
  SPEECH_END = 'speechend'
}

/**
 * Web Speech API 语音识别实现
 */
export class WebSpeechRecognition {
  private recognition: SpeechRecognition | null = null;
  private isSupported: boolean = false;
  private config: SpeechRecognitionConfig;
  private state: SpeechRecognitionState = SpeechRecognitionState.IDLE;

  // 事件回调
  public onResult?: (result: SpeechRecognitionResult) => void;
  public onError?: (error: string) => void;
  public onStateChange?: (state: SpeechRecognitionState) => void;

  constructor(config: SpeechRecognitionConfig) {
    this.config = config;
    this.checkSupport();
    this.initializeRecognition();
  }

  /**
   * 检查浏览器支持
   */
  private checkSupport(): void {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    this.isSupported = !!SpeechRecognition;
    
    if (!this.isSupported) {
      console.warn('Web Speech API 不被当前浏览器支持');
    }
  }

  /**
   * 初始化语音识别
   */
  private initializeRecognition(): void {
    if (!this.isSupported) return;

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();

    // 配置识别器
    this.recognition.lang = this.config.language;
    this.recognition.continuous = this.config.continuous;
    this.recognition.interimResults = this.config.interimResults;
    this.recognition.maxAlternatives = this.config.maxAlternatives;

    // 设置事件监听器
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      this.setState(SpeechRecognitionState.LISTENING);
      console.log('语音识别开始');
    };

    this.recognition.onend = () => {
      this.setState(SpeechRecognitionState.IDLE);
      console.log('语音识别结束');
    };

    this.recognition.onresult = (event: SpeechRecognitionEvent) => {
      this.handleRecognitionResult(event);
    };

    this.recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      this.setState(SpeechRecognitionState.ERROR);
      const errorMessage = `语音识别错误: ${event.error}`;
      console.error(errorMessage);
      
      if (this.onError) {
        this.onError(errorMessage);
      }
    };

    this.recognition.onnomatch = () => {
      console.log('未识别到匹配的语音');
    };

    this.recognition.onsoundstart = () => {
      console.log('检测到声音');
    };

    this.recognition.onsoundend = () => {
      console.log('声音结束');
    };

    this.recognition.onspeechstart = () => {
      console.log('检测到语音');
    };

    this.recognition.onspeechend = () => {
      console.log('语音结束');
    };
  }

  /**
   * 处理识别结果
   */
  private handleRecognitionResult(event: SpeechRecognitionEvent): void {
    const results = event.results;
    const lastResult = results[results.length - 1];
    
    if (lastResult) {
      const alternatives: SpeechAlternative[] = [];
      
      for (let i = 0; i < lastResult.length; i++) {
        alternatives.push({
          transcript: lastResult[i].transcript,
          confidence: lastResult[i].confidence
        });
      }

      const result: SpeechRecognitionResult = {
        transcript: lastResult[0].transcript,
        confidence: lastResult[0].confidence,
        isFinal: lastResult.isFinal,
        alternatives,
        timestamp: new Date(),
        duration: 0, // Web Speech API 不提供持续时间
        language: this.config.language
      };

      console.log('识别结果:', result.transcript, '置信度:', result.confidence);

      if (this.onResult) {
        this.onResult(result);
      }
    }
  }

  /**
   * 开始语音识别
   */
  public start(): boolean {
    if (!this.isSupported || !this.recognition) {
      console.error('语音识别不可用');
      return false;
    }

    if (this.state === SpeechRecognitionState.LISTENING) {
      console.warn('语音识别已在运行');
      return false;
    }

    try {
      this.recognition.start();
      return true;
    } catch (error) {
      console.error('启动语音识别失败:', error);
      return false;
    }
  }

  /**
   * 停止语音识别
   */
  public stop(): void {
    if (this.recognition && this.state === SpeechRecognitionState.LISTENING) {
      this.recognition.stop();
    }
  }

  /**
   * 中止语音识别
   */
  public abort(): void {
    if (this.recognition) {
      this.recognition.abort();
      this.setState(SpeechRecognitionState.STOPPED);
    }
  }

  /**
   * 设置状态
   */
  private setState(newState: SpeechRecognitionState): void {
    if (this.state !== newState) {
      this.state = newState;
      if (this.onStateChange) {
        this.onStateChange(newState);
      }
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): SpeechRecognitionState {
    return this.state;
  }

  /**
   * 检查是否支持
   */
  public isSupported(): boolean {
    return this.isSupported;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<SpeechRecognitionConfig>): void {
    Object.assign(this.config, config);
    
    if (this.recognition) {
      this.recognition.lang = this.config.language;
      this.recognition.continuous = this.config.continuous;
      this.recognition.interimResults = this.config.interimResults;
      this.recognition.maxAlternatives = this.config.maxAlternatives;
    }
  }
}

/**
 * 语音命令处理器
 */
export class VoiceCommandProcessor {
  private commands: Map<string, VoiceCommand> = new Map();
  private commandPatterns: RegExp[] = [];

  /**
   * 添加语音命令
   */
  public addCommand(command: VoiceCommand): void {
    this.commands.set(command.id, command);
    this.updatePatterns();
  }

  /**
   * 移除语音命令
   */
  public removeCommand(commandId: string): void {
    this.commands.delete(commandId);
    this.updatePatterns();
  }

  /**
   * 更新命令模式
   */
  private updatePatterns(): void {
    this.commandPatterns = [];
    
    for (const command of this.commands.values()) {
      if (command.enabled) {
        for (const pattern of command.patterns) {
          try {
            this.commandPatterns.push(new RegExp(pattern, 'i'));
          } catch (error) {
            console.warn(`无效的命令模式: ${pattern}`, error);
          }
        }
      }
    }
  }

  /**
   * 处理语音输入
   */
  public processVoiceInput(transcript: string): VoiceCommand | null {
    const normalizedTranscript = transcript.toLowerCase().trim();
    
    for (const command of this.commands.values()) {
      if (!command.enabled) continue;
      
      for (const pattern of command.patterns) {
        const regex = new RegExp(pattern, 'i');
        const match = normalizedTranscript.match(regex);
        
        if (match) {
          console.log(`匹配到语音命令: ${command.id}`, match);
          return {
            ...command,
            parameters: {
              ...command.parameters,
              match: match[0],
              groups: match.slice(1)
            }
          };
        }
      }
    }
    
    return null;
  }

  /**
   * 获取所有命令
   */
  public getAllCommands(): VoiceCommand[] {
    return Array.from(this.commands.values());
  }

  /**
   * 启用/禁用命令
   */
  public setCommandEnabled(commandId: string, enabled: boolean): void {
    const command = this.commands.get(commandId);
    if (command) {
      command.enabled = enabled;
      this.updatePatterns();
    }
  }
}

/**
 * 语音识别服务主类
 */
export class SpeechRecognitionService {
  private webSpeechRecognition: WebSpeechRecognition;
  private voiceCommandProcessor: VoiceCommandProcessor;
  private config: SpeechRecognitionConfig;
  private isActive: boolean = false;

  // 事件回调
  public onTranscript?: (transcript: string, confidence: number) => void;
  public onCommand?: (command: VoiceCommand) => void;
  public onError?: (error: string) => void;
  public onStateChange?: (state: SpeechRecognitionState) => void;

  constructor(config: SpeechRecognitionConfig) {
    this.config = config;
    this.webSpeechRecognition = new WebSpeechRecognition(config);
    this.voiceCommandProcessor = new VoiceCommandProcessor();
    
    this.setupEventHandlers();
    this.initializeDefaultCommands();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.webSpeechRecognition.onResult = (result) => {
      this.handleRecognitionResult(result);
    };

    this.webSpeechRecognition.onError = (error) => {
      if (this.onError) {
        this.onError(error);
      }
    };

    this.webSpeechRecognition.onStateChange = (state) => {
      if (this.onStateChange) {
        this.onStateChange(state);
      }
    };
  }

  /**
   * 处理识别结果
   */
  private handleRecognitionResult(result: SpeechRecognitionResult): void {
    console.log('语音识别结果:', result);

    // 触发转录事件
    if (this.onTranscript) {
      this.onTranscript(result.transcript, result.confidence);
    }

    // 处理语音命令
    if (result.isFinal) {
      const command = this.voiceCommandProcessor.processVoiceInput(result.transcript);
      if (command && this.onCommand) {
        this.onCommand(command);
      }
    }
  }

  /**
   * 初始化默认命令
   */
  private initializeDefaultCommands(): void {
    const defaultCommands: VoiceCommand[] = [
      {
        id: 'start-tour',
        patterns: ['开始导览', '开始参观', 'start tour', 'begin tour'],
        action: 'start_navigation',
        confidence: 0.8,
        enabled: true
      },
      {
        id: 'stop-tour',
        patterns: ['停止导览', '结束参观', 'stop tour', 'end tour'],
        action: 'stop_navigation',
        confidence: 0.8,
        enabled: true
      },
      {
        id: 'explain-exhibit',
        patterns: ['介绍这个', '解释一下', '这是什么', 'explain this', 'what is this'],
        action: 'explain_current',
        confidence: 0.7,
        enabled: true
      },
      {
        id: 'next-exhibit',
        patterns: ['下一个', '继续', 'next', 'continue'],
        action: 'next_point',
        confidence: 0.8,
        enabled: true
      },
      {
        id: 'previous-exhibit',
        patterns: ['上一个', '返回', 'previous', 'go back'],
        action: 'previous_point',
        confidence: 0.8,
        enabled: true
      },
      {
        id: 'repeat',
        patterns: ['重复一遍', '再说一次', 'repeat', 'say again'],
        action: 'repeat_last',
        confidence: 0.8,
        enabled: true
      }
    ];

    defaultCommands.forEach(command => {
      this.voiceCommandProcessor.addCommand(command);
    });
  }

  /**
   * 开始语音识别
   */
  public start(): boolean {
    if (this.isActive) {
      console.warn('语音识别已在运行');
      return false;
    }

    const success = this.webSpeechRecognition.start();
    if (success) {
      this.isActive = true;
      console.log('语音识别服务已启动');
    }

    return success;
  }

  /**
   * 停止语音识别
   */
  public stop(): void {
    if (this.isActive) {
      this.webSpeechRecognition.stop();
      this.isActive = false;
      console.log('语音识别服务已停止');
    }
  }

  /**
   * 中止语音识别
   */
  public abort(): void {
    this.webSpeechRecognition.abort();
    this.isActive = false;
    console.log('语音识别服务已中止');
  }

  /**
   * 添加自定义命令
   */
  public addVoiceCommand(command: VoiceCommand): void {
    this.voiceCommandProcessor.addCommand(command);
  }

  /**
   * 移除语音命令
   */
  public removeVoiceCommand(commandId: string): void {
    this.voiceCommandProcessor.removeCommand(commandId);
  }

  /**
   * 获取所有语音命令
   */
  public getVoiceCommands(): VoiceCommand[] {
    return this.voiceCommandProcessor.getAllCommands();
  }

  /**
   * 启用/禁用语音命令
   */
  public setCommandEnabled(commandId: string, enabled: boolean): void {
    this.voiceCommandProcessor.setCommandEnabled(commandId, enabled);
  }

  /**
   * 检查是否支持语音识别
   */
  public isSupported(): boolean {
    return this.webSpeechRecognition.isSupported();
  }

  /**
   * 获取当前状态
   */
  public getState(): SpeechRecognitionState {
    return this.webSpeechRecognition.getState();
  }

  /**
   * 检查是否活跃
   */
  public isActive(): boolean {
    return this.isActive;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<SpeechRecognitionConfig>): void {
    Object.assign(this.config, config);
    this.webSpeechRecognition.updateConfig(config);
  }

  /**
   * 获取配置
   */
  public getConfig(): SpeechRecognitionConfig {
    return { ...this.config };
  }

  /**
   * 测试语音识别
   */
  public async testRecognition(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve(false);
        return;
      }

      const originalOnResult = this.webSpeechRecognition.onResult;
      const originalOnError = this.webSpeechRecognition.onError;

      let testCompleted = false;

      const completeTest = (success: boolean) => {
        if (testCompleted) return;
        testCompleted = true;

        // 恢复原始事件处理器
        this.webSpeechRecognition.onResult = originalOnResult;
        this.webSpeechRecognition.onError = originalOnError;

        this.stop();
        resolve(success);
      };

      // 设置测试事件处理器
      this.webSpeechRecognition.onResult = () => {
        completeTest(true);
      };

      this.webSpeechRecognition.onError = () => {
        completeTest(false);
      };

      // 启动测试
      const started = this.start();
      if (!started) {
        completeTest(false);
        return;
      }

      // 5秒后超时
      setTimeout(() => {
        completeTest(false);
      }, 5000);
    });
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return [
      'zh-CN', // 中文（简体）
      'zh-TW', // 中文（繁体）
      'en-US', // 英语（美国）
      'en-GB', // 英语（英国）
      'ja-JP', // 日语
      'ko-KR', // 韩语
      'fr-FR', // 法语
      'de-DE', // 德语
      'es-ES', // 西班牙语
      'it-IT', // 意大利语
      'pt-BR', // 葡萄牙语（巴西）
      'ru-RU', // 俄语
      'ar-SA', // 阿拉伯语
      'hi-IN', // 印地语
      'th-TH'  // 泰语
    ];
  }

  /**
   * 设置语言
   */
  public setLanguage(language: string): void {
    const supportedLanguages = this.getSupportedLanguages();
    if (!supportedLanguages.includes(language)) {
      console.warn(`不支持的语言: ${language}`);
      return;
    }

    this.updateConfig({ language });
    console.log(`语音识别语言已设置为: ${language}`);
  }

  /**
   * 获取识别统计信息
   */
  public getStatistics(): any {
    return {
      isSupported: this.isSupported(),
      isActive: this.isActive,
      currentLanguage: this.config.language,
      commandCount: this.voiceCommandProcessor.getAllCommands().length,
      enabledCommandCount: this.voiceCommandProcessor.getAllCommands().filter(cmd => cmd.enabled).length,
      state: this.getState(),
      config: this.getConfig()
    };
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    this.stop();

    // 清理事件回调
    this.onTranscript = undefined;
    this.onCommand = undefined;
    this.onError = undefined;
    this.onStateChange = undefined;

    console.log('语音识别服务已销毁');
  }
}

/**
 * 语音识别工厂类
 */
export class SpeechRecognitionFactory {
  /**
   * 创建语音识别服务
   */
  public static createService(config?: Partial<SpeechRecognitionConfig>): SpeechRecognitionService {
    const defaultConfig: SpeechRecognitionConfig = {
      language: 'zh-CN',
      continuous: true,
      interimResults: true,
      maxAlternatives: 3,
      noiseReduction: true,
      echoCancellation: true,
      autoGainControl: true,
      sampleRate: 16000,
      provider: 'web-speech'
    };

    const finalConfig = { ...defaultConfig, ...config };
    return new SpeechRecognitionService(finalConfig);
  }

  /**
   * 检查浏览器支持
   */
  public static checkBrowserSupport(): {
    supported: boolean;
    features: {
      webSpeech: boolean;
      mediaDevices: boolean;
      getUserMedia: boolean;
    };
    recommendations: string[];
  } {
    const features = {
      webSpeech: !!(window as any).SpeechRecognition || !!(window as any).webkitSpeechRecognition,
      mediaDevices: !!navigator.mediaDevices,
      getUserMedia: !!navigator.mediaDevices?.getUserMedia
    };

    const supported = features.webSpeech && features.mediaDevices && features.getUserMedia;

    const recommendations: string[] = [];
    if (!features.webSpeech) {
      recommendations.push('请使用支持Web Speech API的浏览器（如Chrome、Edge）');
    }
    if (!features.mediaDevices) {
      recommendations.push('请确保浏览器支持MediaDevices API');
    }
    if (!features.getUserMedia) {
      recommendations.push('请确保浏览器支持getUserMedia API');
    }
    if (!supported) {
      recommendations.push('建议使用最新版本的Chrome或Edge浏览器');
    }

    return {
      supported,
      features,
      recommendations
    };
  }

  /**
   * 获取推荐配置
   */
  public static getRecommendedConfig(scenario: 'exhibition' | 'conversation' | 'command'): SpeechRecognitionConfig {
    const baseConfig: SpeechRecognitionConfig = {
      language: 'zh-CN',
      continuous: true,
      interimResults: true,
      maxAlternatives: 3,
      noiseReduction: true,
      echoCancellation: true,
      autoGainControl: true,
      sampleRate: 16000,
      provider: 'web-speech'
    };

    switch (scenario) {
      case 'exhibition':
        return {
          ...baseConfig,
          continuous: true,
          interimResults: true,
          maxAlternatives: 5
        };

      case 'conversation':
        return {
          ...baseConfig,
          continuous: false,
          interimResults: false,
          maxAlternatives: 1
        };

      case 'command':
        return {
          ...baseConfig,
          continuous: true,
          interimResults: false,
          maxAlternatives: 3
        };

      default:
        return baseConfig;
    }
  }
}
