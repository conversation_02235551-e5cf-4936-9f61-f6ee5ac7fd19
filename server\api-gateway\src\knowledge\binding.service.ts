import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class BindingService {
  private readonly bindingServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.bindingServiceUrl = this.configService.get(
      'BINDING_SERVICE_URL',
      'http://localhost:3001',
    );
  }

  /**
   * 绑定知识库到数字人
   */
  async bindKnowledgeBases(digitalHumanId: string, data: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.post(
        `${this.bindingServiceUrl}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
        data,
      ),
    );
    return response.data;
  }

  /**
   * 获取数字人的知识库绑定
   */
  async getDigitalHumanKnowledgeBases(digitalHumanId: string): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.get(
        `${this.bindingServiceUrl}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
      ),
    );
    return response.data;
  }

  /**
   * 解绑知识库
   */
  async unbindKnowledgeBase(
    digitalHumanId: string,
    knowledgeBaseId: string,
    data: any,
  ): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.delete(
        `${this.bindingServiceUrl}/api/digital-humans/${digitalHumanId}/knowledge-bases/${knowledgeBaseId}`,
        { data },
      ),
    );
    return response.data;
  }

  /**
   * 更新绑定配置
   */
  async updateBindingConfig(
    digitalHumanId: string,
    knowledgeBaseId: string,
    data: any,
  ): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.put(
        `${this.bindingServiceUrl}/api/digital-humans/${digitalHumanId}/knowledge-bases/${knowledgeBaseId}`,
        data,
      ),
    );
    return response.data;
  }

  /**
   * 重新排序绑定优先级
   */
  async reorderBindings(digitalHumanId: string, data: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.put(
        `${this.bindingServiceUrl}/api/digital-humans/${digitalHumanId}/knowledge-bases/reorder`,
        data,
      ),
    );
    return response.data;
  }

  /**
   * 获取知识库的绑定统计
   */
  async getKnowledgeBaseBindingStats(knowledgeBaseId: string): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.get(
        `${this.bindingServiceUrl}/api/knowledge-bases/${knowledgeBaseId}/binding-stats`,
      ),
    );
    return response.data;
  }

  /**
   * 创建数字人
   */
  async createDigitalHuman(data: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.post(`${this.bindingServiceUrl}/api/digital-humans`, data),
    );
    return response.data;
  }

  /**
   * 获取数字人列表
   */
  async getDigitalHumans(query: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.get(`${this.bindingServiceUrl}/api/digital-humans`, {
        params: query,
      }),
    );
    return response.data;
  }

  /**
   * 获取数字人详情
   */
  async getDigitalHuman(id: string): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.get(`${this.bindingServiceUrl}/api/digital-humans/${id}`),
    );
    return response.data;
  }

  /**
   * 更新数字人
   */
  async updateDigitalHuman(id: string, data: any): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.put(`${this.bindingServiceUrl}/api/digital-humans/${id}`, data),
    );
    return response.data;
  }

  /**
   * 删除数字人
   */
  async deleteDigitalHuman(id: string): Promise<any> {
    const response = await firstValueFrom(
      this.httpService.delete(`${this.bindingServiceUrl}/api/digital-humans/${id}`),
    );
    return response.data;
  }
}
