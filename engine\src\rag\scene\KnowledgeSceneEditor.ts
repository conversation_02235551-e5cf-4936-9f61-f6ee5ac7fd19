/**
 * 知识场景编辑器
 * 扩展现有场景编辑器，支持知识点标注和管理
 */

import * as THREE from 'three';
import { v4 as uuidv4 } from 'uuid';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { System } from '../../core/System';
import { World } from '../../core/World';
import { Transform } from '../../core/components/Transform';

/**
 * 知识点数据接口
 */
export interface KnowledgeData {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  tags: string[];
  category: string;
  priority: number;
  relatedTopics: string[];
  metadata?: Record<string, any>;
}

/**
 * 知识点实体
 */
export class KnowledgePoint {
  public marker: THREE.Object3D | null = null;
  public isVisible: boolean = true;
  public isSelected: boolean = false;

  constructor(
    public id: string,
    public position: THREE.Vector3,
    public knowledge: KnowledgeData
  ) {}

  /**
   * 创建知识点的3D标记
   */
  public createMarker(): THREE.Object3D {
    const group = new THREE.Group();
    
    // 创建球体标记
    const geometry = new THREE.SphereGeometry(0.2);
    const material = new THREE.MeshBasicMaterial({ 
      color: this.getCategoryColor(),
      transparent: true,
      opacity: 0.8
    });
    
    const sphere = new THREE.Mesh(geometry, material);
    group.add(sphere);
    
    // 创建文字标签
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    canvas.width = 256;
    canvas.height = 64;
    
    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.fillStyle = '#000000';
    context.font = '16px Arial';
    context.textAlign = 'center';
    context.fillText(this.knowledge.title, canvas.width / 2, canvas.height / 2);
    
    const texture = new THREE.CanvasTexture(canvas);
    const labelMaterial = new THREE.SpriteMaterial({ map: texture });
    const label = new THREE.Sprite(labelMaterial);
    label.position.set(0, 0.5, 0);
    label.scale.set(1, 0.25, 1);
    
    group.add(label);
    
    // 存储知识点引用
    group.userData = { knowledgePoint: this };
    
    this.marker = group;
    return group;
  }

  /**
   * 根据类别获取颜色
   */
  private getCategoryColor(): number {
    const colorMap: Record<string, number> = {
      'general': 0x00ff00,
      'important': 0xff0000,
      'technical': 0x0000ff,
      'historical': 0xffff00,
      'cultural': 0xff00ff,
      'default': 0x888888
    };
    
    return colorMap[this.knowledge.category] || colorMap['default'];
  }

  /**
   * 更新标记可见性
   */
  public setVisible(visible: boolean): void {
    this.isVisible = visible;
    if (this.marker) {
      this.marker.visible = visible;
    }
  }

  /**
   * 设置选中状态
   */
  public setSelected(selected: boolean): void {
    this.isSelected = selected;
    if (this.marker) {
      const sphere = this.marker.children[0] as THREE.Mesh;
      const material = sphere.material as THREE.MeshBasicMaterial;
      
      if (selected) {
        material.color.setHex(0xffffff);
        material.opacity = 1.0;
      } else {
        material.color.setHex(this.getCategoryColor());
        material.opacity = 0.8;
      }
    }
  }

  /**
   * 更新位置
   */
  public updatePosition(position: THREE.Vector3): void {
    this.position.copy(position);
    if (this.marker) {
      this.marker.position.copy(position);
    }
  }

  /**
   * 销毁标记
   */
  public dispose(): void {
    if (this.marker) {
      this.marker.parent?.remove(this.marker);
      
      // 清理几何体和材质
      this.marker.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.geometry.dispose();
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => mat.dispose());
          } else {
            child.material.dispose();
          }
        }
        if (child instanceof THREE.Sprite) {
          child.material.dispose();
        }
      });
      
      this.marker = null;
    }
  }
}

/**
 * 知识点组件
 */
export class KnowledgePointComponent extends Component {
  public static readonly TYPE = 'KnowledgePoint';
  
  constructor(
    entity: Entity,
    public knowledgePoint: KnowledgePoint
  ) {
    super(entity, KnowledgePointComponent.TYPE);
  }
}

/**
 * 知识场景编辑器
 */
export class KnowledgeSceneEditor extends System {
  public static readonly TYPE = 'KnowledgeSceneEditor';
  
  private knowledgePoints: Map<string, KnowledgePoint> = new Map();
  private scene: THREE.Scene | null = null;
  private selectedKnowledgePoint: KnowledgePoint | null = null;
  private raycaster: THREE.Raycaster = new THREE.Raycaster();
  private mouse: THREE.Vector2 = new THREE.Vector2();
  private camera: THREE.Camera | null = null;
  
  // 事件回调
  public onKnowledgePointAdded?: (knowledgePoint: KnowledgePoint) => void;
  public onKnowledgePointSelected?: (knowledgePoint: KnowledgePoint | null) => void;
  public onKnowledgePointUpdated?: (knowledgePoint: KnowledgePoint) => void;
  public onKnowledgePointRemoved?: (knowledgePointId: string) => void;

  constructor(world: World, scene: THREE.Scene, camera: THREE.Camera) {
    super(world, KnowledgeSceneEditor.TYPE);
    this.scene = scene;
    this.camera = camera;
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听鼠标点击事件
    document.addEventListener('click', this.onMouseClick.bind(this));
    document.addEventListener('mousemove', this.onMouseMove.bind(this));
  }

  /**
   * 添加知识点
   */
  public addKnowledgePoint(position: THREE.Vector3, knowledge: KnowledgeData): string {
    const id = uuidv4();
    const knowledgePoint = new KnowledgePoint(id, position.clone(), knowledge);
    
    this.knowledgePoints.set(id, knowledgePoint);
    
    // 在场景中创建可视化标记
    this.createKnowledgeMarker(knowledgePoint);
    
    // 创建实体和组件
    const entity = this.world.createEntity(`knowledge-point-${id}`);
    entity.addComponent(new Transform(entity, { position: position.clone() }));
    entity.addComponent(new KnowledgePointComponent(entity, knowledgePoint));
    
    // 触发事件
    if (this.onKnowledgePointAdded) {
      this.onKnowledgePointAdded(knowledgePoint);
    }
    
    return id;
  }

  /**
   * 创建知识点标记
   */
  private createKnowledgeMarker(knowledgePoint: KnowledgePoint): void {
    if (!this.scene) return;
    
    const marker = knowledgePoint.createMarker();
    marker.position.copy(knowledgePoint.position);
    this.scene.add(marker);
  }

  /**
   * 移除知识点
   */
  public removeKnowledgePoint(id: string): boolean {
    const knowledgePoint = this.knowledgePoints.get(id);
    if (!knowledgePoint) return false;
    
    // 清理3D标记
    knowledgePoint.dispose();
    
    // 移除实体
    const entity = this.world.getEntity(`knowledge-point-${id}`);
    if (entity) {
      this.world.removeEntity(entity);
    }
    
    // 从映射中移除
    this.knowledgePoints.delete(id);
    
    // 如果是当前选中的知识点，清除选择
    if (this.selectedKnowledgePoint === knowledgePoint) {
      this.selectedKnowledgePoint = null;
      if (this.onKnowledgePointSelected) {
        this.onKnowledgePointSelected(null);
      }
    }
    
    // 触发事件
    if (this.onKnowledgePointRemoved) {
      this.onKnowledgePointRemoved(id);
    }
    
    return true;
  }

  /**
   * 获取知识点
   */
  public getKnowledgePoint(id: string): KnowledgePoint | undefined {
    return this.knowledgePoints.get(id);
  }

  /**
   * 获取所有知识点
   */
  public getAllKnowledgePoints(): KnowledgePoint[] {
    return Array.from(this.knowledgePoints.values());
  }

  /**
   * 更新知识点
   */
  public updateKnowledgePoint(id: string, updates: Partial<KnowledgeData>): boolean {
    const knowledgePoint = this.knowledgePoints.get(id);
    if (!knowledgePoint) return false;

    // 更新知识数据
    Object.assign(knowledgePoint.knowledge, updates);

    // 重新创建标记以反映更新
    if (knowledgePoint.marker && this.scene) {
      this.scene.remove(knowledgePoint.marker);
      knowledgePoint.dispose();
      this.createKnowledgeMarker(knowledgePoint);
    }

    // 触发事件
    if (this.onKnowledgePointUpdated) {
      this.onKnowledgePointUpdated(knowledgePoint);
    }

    return true;
  }

  /**
   * 鼠标点击事件处理
   */
  private onMouseClick(event: MouseEvent): void {
    if (!this.camera || !this.scene) return;

    // 更新鼠标位置
    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // 执行射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera);

    // 获取所有知识点标记
    const markers: THREE.Object3D[] = [];
    this.knowledgePoints.forEach(kp => {
      if (kp.marker && kp.isVisible) {
        markers.push(kp.marker);
      }
    });

    const intersects = this.raycaster.intersectObjects(markers, true);

    if (intersects.length > 0) {
      // 找到被点击的知识点
      let clickedKnowledgePoint: KnowledgePoint | null = null;

      for (const intersect of intersects) {
        let obj = intersect.object;
        while (obj && !obj.userData.knowledgePoint) {
          obj = obj.parent!;
        }

        if (obj && obj.userData.knowledgePoint) {
          clickedKnowledgePoint = obj.userData.knowledgePoint;
          break;
        }
      }

      if (clickedKnowledgePoint) {
        this.selectKnowledgePoint(clickedKnowledgePoint);
      }
    } else {
      // 点击空白区域，取消选择
      this.selectKnowledgePoint(null);
    }
  }

  /**
   * 鼠标移动事件处理
   */
  private onMouseMove(event: MouseEvent): void {
    // 更新鼠标位置用于悬停效果
    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  }

  /**
   * 选择知识点
   */
  public selectKnowledgePoint(knowledgePoint: KnowledgePoint | null): void {
    // 取消之前的选择
    if (this.selectedKnowledgePoint) {
      this.selectedKnowledgePoint.setSelected(false);
    }

    // 设置新的选择
    this.selectedKnowledgePoint = knowledgePoint;
    if (knowledgePoint) {
      knowledgePoint.setSelected(true);
    }

    // 触发事件
    if (this.onKnowledgePointSelected) {
      this.onKnowledgePointSelected(knowledgePoint);
    }
  }

  /**
   * 获取当前选中的知识点
   */
  public getSelectedKnowledgePoint(): KnowledgePoint | null {
    return this.selectedKnowledgePoint;
  }

  /**
   * 按类别过滤知识点
   */
  public getKnowledgePointsByCategory(category: string): KnowledgePoint[] {
    return Array.from(this.knowledgePoints.values())
      .filter(kp => kp.knowledge.category === category);
  }

  /**
   * 按标签搜索知识点
   */
  public searchKnowledgePointsByTag(tag: string): KnowledgePoint[] {
    return Array.from(this.knowledgePoints.values())
      .filter(kp => kp.knowledge.tags.includes(tag));
  }

  /**
   * 搜索知识点
   */
  public searchKnowledgePoints(query: string): KnowledgePoint[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.knowledgePoints.values())
      .filter(kp =>
        kp.knowledge.title.toLowerCase().includes(lowerQuery) ||
        kp.knowledge.content.toLowerCase().includes(lowerQuery) ||
        kp.knowledge.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      );
  }

  /**
   * 设置知识点可见性
   */
  public setKnowledgePointVisibility(id: string, visible: boolean): boolean {
    const knowledgePoint = this.knowledgePoints.get(id);
    if (!knowledgePoint) return false;

    knowledgePoint.setVisible(visible);
    return true;
  }

  /**
   * 设置类别可见性
   */
  public setCategoryVisibility(category: string, visible: boolean): void {
    this.knowledgePoints.forEach(kp => {
      if (kp.knowledge.category === category) {
        kp.setVisible(visible);
      }
    });
  }

  /**
   * 导出知识点数据
   */
  public exportKnowledgePoints(): any {
    const data = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      knowledgePoints: Array.from(this.knowledgePoints.values()).map(kp => ({
        id: kp.id,
        position: {
          x: kp.position.x,
          y: kp.position.y,
          z: kp.position.z
        },
        knowledge: kp.knowledge,
        isVisible: kp.isVisible
      }))
    };

    return data;
  }

  /**
   * 导入知识点数据
   */
  public importKnowledgePoints(data: any): boolean {
    try {
      if (!data.knowledgePoints || !Array.isArray(data.knowledgePoints)) {
        return false;
      }

      // 清除现有知识点
      this.clearAllKnowledgePoints();

      // 导入新的知识点
      for (const kpData of data.knowledgePoints) {
        const position = new THREE.Vector3(
          kpData.position.x,
          kpData.position.y,
          kpData.position.z
        );

        const id = this.addKnowledgePoint(position, kpData.knowledge);

        // 设置可见性
        if (kpData.isVisible !== undefined) {
          this.setKnowledgePointVisibility(id, kpData.isVisible);
        }
      }

      return true;
    } catch (error) {
      console.error('导入知识点数据失败:', error);
      return false;
    }
  }

  /**
   * 清除所有知识点
   */
  public clearAllKnowledgePoints(): void {
    const ids = Array.from(this.knowledgePoints.keys());
    ids.forEach(id => this.removeKnowledgePoint(id));
  }

  /**
   * 系统更新
   */
  public update(deltaTime: number): void {
    // 这里可以添加需要每帧更新的逻辑
    // 例如：动画效果、距离检测等
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除所有知识点
    this.clearAllKnowledgePoints();

    // 移除事件监听器
    document.removeEventListener('click', this.onMouseClick.bind(this));
    document.removeEventListener('mousemove', this.onMouseMove.bind(this));

    // 清理引用
    this.scene = null;
    this.camera = null;
    this.selectedKnowledgePoint = null;
  }
}
