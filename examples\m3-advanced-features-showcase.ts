/**
 * M3高级功能综合展示
 * 展示数字人制作系统M3阶段的所有高级功能，包括：
 * - 高级换装系统
 * - BIP动画重定向
 * - 表情和动作系统
 * - 动作序列编排
 * - 版本管理
 * - 场景交互
 */

import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/Transform';
import { DigitalHumanComponent } from '../engine/src/avatar/components/DigitalHumanComponent';
import { FacialAnimationComponent } from '../engine/src/avatar/components/FacialAnimationComponent';

// 高级换装系统
import { ClothingSystem } from '../engine/src/avatar/clothing/ClothingSystem';
import { ClothingEditor } from '../engine/src/ui/clothing/ClothingEditor';

// BIP动画重定向
import { BIPAnimationRetargeter } from '../engine/src/avatar/animation/BIPAnimationRetargeter';

// 表情和动作系统
import { AdvancedExpressionSystem } from '../engine/src/avatar/animation/AdvancedExpressionSystem';

// 动作序列编排
import { ActionSequenceComposer } from '../engine/src/avatar/animation/ActionSequenceComposer';

// 版本管理
import { DigitalHumanVersionManager } from '../engine/src/avatar/version/DigitalHumanVersionManager';

// 场景交互
import { SceneInteractionSystem } from '../engine/src/avatar/interaction/SceneInteractionSystem';

import * as THREE from 'three';

/**
 * M3高级功能展示类
 */
export class M3AdvancedFeaturesShowcase {
  private world: World;
  private digitalHumanEntity: Entity;

  // 系统实例
  private clothingSystem: ClothingSystem;
  private bipRetargeter: BIPAnimationRetargeter;
  private expressionSystem: AdvancedExpressionSystem;
  private actionComposer: ActionSequenceComposer;
  private versionManager: DigitalHumanVersionManager;
  private interactionSystem: SceneInteractionSystem;

  // UI组件
  private clothingEditor: ClothingEditor;

  // Three.js组件
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;

  constructor() {
    this.initializeShowcase();
  }

  /**
   * 初始化展示
   */
  private async initializeShowcase(): Promise<void> {
    console.log('🚀 初始化M3高级功能综合展示...');

    // 1. 创建基础环境
    await this.setupBasicEnvironment();

    // 2. 初始化所有高级系统
    await this.initializeAdvancedSystems();

    // 3. 创建数字人实体
    await this.createDigitalHuman();

    // 4. 设置UI界面
    await this.setupUserInterface();

    // 5. 演示所有功能
    await this.demonstrateAllFeatures();

    console.log('✅ M3高级功能综合展示初始化完成');
  }

  /**
   * 设置基础环境
   */
  private async setupBasicEnvironment(): Promise<void> {
    console.log('🌍 设置基础环境...');

    // 创建世界
    this.world = new World();

    // 创建Three.js场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x222222);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 2, 5);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);

    console.log('✅ 基础环境设置完成');
  }

  /**
   * 初始化高级系统
   */
  private async initializeAdvancedSystems(): Promise<void> {
    console.log('⚙️ 初始化高级系统...');

    // 1. 高级换装系统
    this.clothingSystem = new ClothingSystem(this.world, {
      enablePhysics: true,
      enableAutoFitting: true,
      enableCollisionDetection: true,
      fittingQualityThreshold: 0.8,
      maxClothingItems: 1000,
      debug: true
    });
    this.world.addSystem(this.clothingSystem);

    // 2. BIP动画重定向系统
    this.bipRetargeter = new BIPAnimationRetargeter({
      preserveOriginalTiming: true,
      autoScale: true,
      scaleFactor: 1.0,
      smoothInterpolation: true,
      qualityLevel: 'high',
      debug: true
    });

    // 3. 表情和动作系统
    this.expressionSystem = new AdvancedExpressionSystem(this.world, {
      enableMicroExpressions: true,
      enableAutoBlink: true,
      enableBreathing: true,
      enableEmotionAnalysis: false,
      enableCulturalAdaptation: true,
      defaultBlendSpeed: 2.0,
      debug: true
    });
    this.world.addSystem(this.expressionSystem);

    // 4. 动作序列编排系统
    this.actionComposer = new ActionSequenceComposer({
      defaultFrameRate: 30,
      maxTracks: 10,
      maxDuration: 300,
      autoSave: true,
      autoSaveInterval: 30,
      debug: true
    });

    // 5. 版本管理系统
    this.versionManager = new DigitalHumanVersionManager({
      maxVersions: 100,
      maxSnapshots: 50,
      autoSnapshot: true,
      autoSnapshotInterval: 300,
      enableCompression: true,
      debug: true
    });

    // 6. 场景交互系统
    this.interactionSystem = new SceneInteractionSystem(this.world, {
      enableDragControl: true,
      enableEnvironmentAdaptation: true,
      enableIntelligentBehavior: true,
      enableCollisionDetection: true,
      enablePhysicsSimulation: false,
      interactionCheckFrequency: 60,
      maxInteractionDistance: 10.0,
      debug: true
    });
    this.interactionSystem.setCamera(this.camera);
    this.interactionSystem.setScene(this.scene);
    this.world.addSystem(this.interactionSystem);

    console.log('✅ 所有高级系统初始化完成');
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHuman(): Promise<void> {
    console.log('👤 创建数字人实体...');

    // 创建实体
    this.digitalHumanEntity = new Entity(this.world);
    this.digitalHumanEntity.name = 'M3ShowcaseDigitalHuman';

    // 添加Transform组件
    const transform = new Transform();
    this.digitalHumanEntity.addComponent(transform);

    // 添加数字人组件
    const digitalHumanComponent = new DigitalHumanComponent(this.digitalHumanEntity, {
      name: 'M3展示数字人',
      userId: 'm3-showcase',
      bodyMorphTargets: {
        height: 0.1,
        weight: 0.0,
        muscle: 0.2,
        chest: 0.0,
        waist: 0.0,
        hips: 0.0,
        shoulders: 0.1,
        custom: new Map()
      }
    });

    // 创建简单的几何体表示
    const geometry = new THREE.CapsuleGeometry(0.3, 1.7, 4, 8);
    const material = new THREE.MeshLambertMaterial({ color: 0x8888ff });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    this.scene.add(mesh);
    digitalHumanComponent.mesh = mesh;

    this.digitalHumanEntity.addComponent(digitalHumanComponent);

    // 添加面部动画组件
    const facialAnimationComponent = new FacialAnimationComponent(this.digitalHumanEntity);
    this.digitalHumanEntity.addComponent(facialAnimationComponent);

    // 添加到世界
    this.world.addEntity(this.digitalHumanEntity);

    console.log('✅ 数字人实体创建完成');
  }

  /**
   * 设置用户界面
   */
  private async setupUserInterface(): Promise<void> {
    console.log('🎨 设置用户界面...');

    // 创建控制面板
    const controlPanel = document.createElement('div');
    controlPanel.id = 'control-panel';
    controlPanel.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      width: 300px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      z-index: 1000;
    `;

    controlPanel.innerHTML = `
      <h3>M3高级功能控制面板</h3>
      <div class="feature-section">
        <h4>换装系统</h4>
        <button onclick="window.showcase.demonstrateClothing()">演示换装</button>
        <button onclick="window.showcase.openClothingEditor()">打开换装编辑器</button>
      </div>
      <div class="feature-section">
        <h4>表情系统</h4>
        <button onclick="window.showcase.demonstrateExpressions()">演示表情</button>
        <button onclick="window.showcase.playExpressionSequence()">播放表情序列</button>
      </div>
      <div class="feature-section">
        <h4>动作系统</h4>
        <button onclick="window.showcase.demonstrateActions()">演示动作</button>
        <button onclick="window.showcase.openActionComposer()">打开动作编排器</button>
      </div>
      <div class="feature-section">
        <h4>版本管理</h4>
        <button onclick="window.showcase.createVersion()">创建版本</button>
        <button onclick="window.showcase.demonstrateVersionControl()">演示版本控制</button>
      </div>
      <div class="feature-section">
        <h4>场景交互</h4>
        <button onclick="window.showcase.demonstrateInteraction()">演示交互</button>
        <button onclick="window.showcase.enableDragMode()">启用拖拽模式</button>
      </div>
      <div class="feature-section">
        <h4>BIP动画</h4>
        <button onclick="window.showcase.demonstrateBIPRetargeting()">演示BIP重定向</button>
      </div>
    `;

    document.body.appendChild(controlPanel);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .feature-section {
        margin: 10px 0;
        padding: 8px;
        border: 1px solid #444;
        border-radius: 4px;
      }
      .feature-section h4 {
        margin: 0 0 8px 0;
        color: #4CAF50;
      }
      .feature-section button {
        margin: 2px;
        padding: 4px 8px;
        background: #333;
        color: white;
        border: 1px solid #555;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
      }
      .feature-section button:hover {
        background: #555;
      }
    `;
    document.head.appendChild(style);

    // 暴露到全局以便按钮调用
    (window as any).showcase = this;

    console.log('✅ 用户界面设置完成');
  }

  /**
   * 演示所有功能
   */
  private async demonstrateAllFeatures(): Promise<void> {
    console.log('🎭 开始演示所有M3高级功能...');

    // 自动演示序列
    await this.wait(2000);
    await this.demonstrateClothing();
    
    await this.wait(3000);
    await this.demonstrateExpressions();
    
    await this.wait(3000);
    await this.demonstrateActions();
    
    await this.wait(3000);
    await this.createVersion();
    
    await this.wait(3000);
    await this.demonstrateInteraction();

    console.log('✅ 所有功能演示完成');
  }

  // ==================== 功能演示方法 ====================

  /**
   * 演示换装功能
   */
  public async demonstrateClothing(): Promise<void> {
    console.log('👕 演示换装功能...');
    
    // 模拟装备不同服装
    const clothingItems = ['casual_tshirt_001', 'formal_shirt_001', 'casual_jeans_001'];
    
    for (const itemId of clothingItems) {
      await this.clothingSystem.equipClothing(this.digitalHumanEntity.id, itemId);
      await this.wait(1500);
    }
    
    console.log('✅ 换装功能演示完成');
  }

  /**
   * 打开换装编辑器
   */
  public openClothingEditor(): void {
    console.log('🎨 打开换装编辑器...');
    
    if (!this.clothingEditor) {
      const container = document.createElement('div');
      container.style.cssText = `
        position: fixed;
        top: 50px;
        right: 10px;
        width: 400px;
        height: 600px;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 8px;
        z-index: 1001;
      `;
      document.body.appendChild(container);

      this.clothingEditor = new ClothingEditor(this.clothingSystem, {
        container,
        showPreview: true,
        enableDragDrop: true,
        theme: 'dark',
        language: 'zh'
      });

      this.clothingEditor.setCurrentEntity(this.digitalHumanEntity.id);
    }
  }

  /**
   * 演示表情功能
   */
  public async demonstrateExpressions(): Promise<void> {
    console.log('😊 演示表情功能...');
    
    const expressions = ['happy', 'sad', 'surprised', 'angry'];
    
    for (const expression of expressions) {
      this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, expression, 1.5);
      await this.wait(2000);
    }
    
    console.log('✅ 表情功能演示完成');
  }

  /**
   * 播放表情序列
   */
  public playExpressionSequence(): void {
    console.log('🎬 播放表情序列...');
    this.expressionSystem.playExpressionSequence(this.digitalHumanEntity.id, 'thinking_process');
  }

  /**
   * 演示动作功能
   */
  public async demonstrateActions(): Promise<void> {
    console.log('🤸 演示动作功能...');
    
    // 创建简单的动作序列
    const sequenceId = this.actionComposer.createSequence('演示动作序列', 'M3功能展示动作');
    
    if (sequenceId) {
      const sequence = this.actionComposer.getCurrentSequence();
      if (sequence && sequence.tracks.length > 0) {
        const bodyTrack = sequence.tracks[0];
        
        // 添加一些动作节点
        this.actionComposer.addSequenceNode(sequenceId, bodyTrack.id, 'idle_basic', 0, 2);
        this.actionComposer.addSequenceNode(sequenceId, bodyTrack.id, 'wave_hello', 2, 3);
        this.actionComposer.addSequenceNode(sequenceId, bodyTrack.id, 'jump_basic', 5, 1.5);
        
        // 播放序列
        this.actionComposer.play(sequenceId);
      }
    }
    
    console.log('✅ 动作功能演示完成');
  }

  /**
   * 打开动作编排器
   */
  public openActionComposer(): void {
    console.log('🎬 打开动作编排器...');
    alert('动作编排器界面将在这里打开（需要完整的UI实现）');
  }

  /**
   * 创建版本
   */
  public createVersion(): void {
    console.log('📝 创建版本...');
    
    const versionId = this.versionManager.createVersion(
      this.digitalHumanEntity.id,
      'minor' as any,
      'M3功能展示版本',
      'showcase-user'
    );
    
    console.log(`✅ 创建版本: ${versionId}`);
  }

  /**
   * 演示版本控制
   */
  public demonstrateVersionControl(): void {
    console.log('🔄 演示版本控制...');
    
    // 创建几个版本
    this.createVersion();
    
    setTimeout(() => {
      // 演示撤销
      this.versionManager.undo(this.digitalHumanEntity.id);
      console.log('执行撤销操作');
      
      setTimeout(() => {
        // 演示重做
        this.versionManager.redo(this.digitalHumanEntity.id);
        console.log('执行重做操作');
      }, 1000);
    }, 1000);
  }

  /**
   * 演示交互功能
   */
  public demonstrateInteraction(): void {
    console.log('🎯 演示交互功能...');
    
    // 触发点击交互
    this.interactionSystem.triggerInteraction(this.digitalHumanEntity.id, 'click' as any);
    
    setTimeout(() => {
      // 触发悬停交互
      this.interactionSystem.triggerInteraction(this.digitalHumanEntity.id, 'hover' as any);
    }, 1000);
  }

  /**
   * 启用拖拽模式
   */
  public enableDragMode(): void {
    console.log('🖱️ 启用拖拽模式...');
    this.interactionSystem.setDragEnabled(this.digitalHumanEntity.id, true);
    alert('拖拽模式已启用，尝试拖拽数字人！');
  }

  /**
   * 演示BIP重定向
   */
  public async demonstrateBIPRetargeting(): Promise<void> {
    console.log('🎭 演示BIP动画重定向...');
    
    // 这里需要实际的BIP动画数据
    // 简化演示
    console.log('BIP动画重定向功能已准备就绪');
    alert('BIP动画重定向演示（需要实际的BIP动画文件）');
  }

  /**
   * 等待指定时间
   * @param ms 毫秒
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 运行展示
   */
  public async run(): Promise<void> {
    console.log('🚀 M3高级功能综合展示正在运行...');

    // 启动渲染循环
    const animate = () => {
      requestAnimationFrame(animate);
      
      // 更新世界
      this.world.update(1/60);
      
      // 渲染场景
      this.renderer.render(this.scene, this.camera);
    };
    
    animate();

    // 窗口调整事件
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });

    console.log('💡 使用控制面板体验各种M3高级功能！');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理M3展示资源...');

    // 清理所有系统
    this.clothingSystem?.dispose();
    this.expressionSystem?.dispose();
    this.actionComposer?.dispose();
    this.versionManager?.dispose();
    this.interactionSystem?.dispose();

    // 清理Three.js
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
    this.renderer.dispose();

    // 清理UI
    const controlPanel = document.getElementById('control-panel');
    if (controlPanel) {
      controlPanel.remove();
    }

    console.log('✅ M3展示资源已清理');
  }
}

// 导出展示类
export default M3AdvancedFeaturesShowcase;

// 如果直接运行此文件，则启动展示
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const showcase = new M3AdvancedFeaturesShowcase();
    await showcase.run();
  });
}
