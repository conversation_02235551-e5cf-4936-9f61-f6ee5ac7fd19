/**
 * 场景描述理解引擎
 * 提供高级的自然语言理解功能，专门用于场景描述的解析和理解
 */
import {
  SceneUnderstanding,
  SceneElement,
  SceneElementType,
  SpatialRelation,
  SpatialRelationType,
  SceneIntent,
  SceneConstraint,
  NLPResult
} from './SceneGenerationTypes';

/**
 * 场景描述理解引擎主类
 */
export class SceneDescriptionUnderstanding {
  private nlpProcessor: NLPProcessor;
  private spatialParser: SpatialRelationshipParser;
  private intentClassifier: SceneIntentClassifier;

  constructor() {
    this.nlpProcessor = new NLPProcessor({
      language: 'zh-CN',
      enableNER: true,
      enablePOS: true
    });

    this.spatialParser = new SpatialRelationshipParser();
    this.intentClassifier = new SceneIntentClassifier();
  }

  /**
   * 解析场景描述
   */
  async parseDescription(description: string): Promise<SceneUnderstanding> {
    // 1. 基础NLP处理
    const nlpResult = await this.nlpProcessor.process(description);

    // 2. 提取场景元素
    const elements = this.extractSceneElements(nlpResult);

    // 3. 解析空间关系
    const spatialRelations = this.spatialParser.parse(nlpResult, elements);

    // 4. 识别场景意图
    const intent = await this.intentClassifier.classify(description, elements);

    // 5. 提取约束条件
    const constraints = this.extractConstraints(nlpResult, elements);

    return {
      elements,
      spatialRelations,
      intent,
      constraints,
      confidence: this.calculateUnderstandingConfidence(nlpResult)
    };
  }

  /**
   * 提取场景元素
   */
  private extractSceneElements(nlpResult: NLPResult): SceneElement[] {
    const elements: SceneElement[] = [];

    // 提取实体
    for (const entity of nlpResult.entities) {
      if (this.isSceneObject(entity)) {
        elements.push({
          type: SceneElementType.OBJECT,
          name: entity.text,
          category: this.categorizeObject(entity.text),
          attributes: this.extractAttributes(entity, nlpResult)
        });
      }
    }

    // 提取环境描述
    const environmentKeywords = this.extractEnvironmentKeywords(nlpResult);
    if (environmentKeywords.length > 0) {
      elements.push({
        type: SceneElementType.ENVIRONMENT,
        name: 'scene_environment',
        category: 'environment',
        attributes: {
          style: environmentKeywords.find(k => k.type === 'style')?.value,
          lighting: environmentKeywords.find(k => k.type === 'lighting')?.value,
          weather: environmentKeywords.find(k => k.type === 'weather')?.value,
          timeOfDay: environmentKeywords.find(k => k.type === 'timeOfDay')?.value
        }
      });
    }

    return elements;
  }

  /**
   * 检查是否为场景对象
   */
  private isSceneObject(entity: any): boolean {
    return entity.label === 'OBJECT';
  }

  /**
   * 对象分类
   */
  private categorizeObject(objectName: string): string {
    const categories: Record<string, string> = {
      '桌子': 'furniture',
      '椅子': 'furniture',
      '沙发': 'furniture',
      '床': 'furniture',
      '柜子': 'furniture',
      '灯': 'lighting',
      '电视': 'electronics',
      '电脑': 'electronics',
      '书': 'decoration',
      '花': 'decoration',
      '植物': 'decoration'
    };

    return categories[objectName] || 'misc';
  }

  /**
   * 提取属性
   */
  private extractAttributes(entity: any, nlpResult: NLPResult): Record<string, any> {
    const attributes: Record<string, any> = {};
    const fullText = nlpResult.tokens.join(' ');

    // 提取颜色
    const colorEntities = nlpResult.entities.filter(e => e.label === 'COLOR');
    if (colorEntities.length > 0) {
      attributes.color = colorEntities[0].text;
    }

    // 提取尺寸
    const sizeMatches = fullText.match(/(大|小|中等|巨大|微小)/g);
    if (sizeMatches) {
      attributes.size = sizeMatches[0];
    }

    // 提取材质
    const materialMatches = fullText.match(/(木质|金属|塑料|玻璃|布料|皮革|石材)/g);
    if (materialMatches) {
      attributes.material = materialMatches[0];
    }

    return attributes;
  }

  /**
   * 提取环境关键词
   */
  private extractEnvironmentKeywords(nlpResult: NLPResult): Array<{type: string, value: string}> {
    const keywords: Array<{type: string, value: string}> = [];
    const fullText = nlpResult.tokens.join(' ');

    // 提取风格
    const styleMatches = fullText.match(/(现代|古典|简约|豪华|工业|田园|北欧|中式|欧式)/g);
    if (styleMatches) {
      keywords.push({ type: 'style', value: styleMatches[0] });
    }

    // 提取光照
    const lightingMatches = fullText.match(/(明亮|昏暗|温暖|冷色|自然光|人工光)/g);
    if (lightingMatches) {
      keywords.push({ type: 'lighting', value: lightingMatches[0] });
    }

    return keywords;
  }

  /**
   * 提取约束条件
   */
  private extractConstraints(nlpResult: NLPResult, elements: SceneElement[]): SceneConstraint[] {
    const constraints: SceneConstraint[] = [];

    // 这里应该实现约束条件的提取逻辑
    // 例如：尺寸限制、颜色要求、风格一致性等

    return constraints;
  }

  /**
   * 计算理解置信度
   */
  private calculateUnderstandingConfidence(nlpResult: NLPResult): number {
    let confidence = 0.5; // 基础置信度

    // 根据识别到的实体数量调整置信度
    if (nlpResult.entities.length > 0) {
      confidence += 0.2;
    }

    // 根据词性标注质量调整置信度
    if (nlpResult.posTags.length > 0) {
      confidence += 0.1;
    }

    // 根据情感分析结果调整置信度
    if (nlpResult.sentiment && nlpResult.sentiment.score > 0.6) {
      confidence += 0.2;
    }

    return Math.min(confidence, 1.0);
  }
}

/**
 * NLP处理器配置
 */
export interface NLPProcessorConfig {
  /** 语言 */
  language?: string;
  /** 是否启用命名实体识别 */
  enableNER?: boolean;
  /** 是否启用词性标注 */
  enablePOS?: boolean;
  /** 是否启用依存分析 */
  enableDependency?: boolean;
  /** 是否启用情感分析 */
  enableSentiment?: boolean;
}

/**
 * 空间关系解析器
 */
export class SpatialRelationshipParser {
  private spatialKeywords: Map<string, SpatialRelationType>;
  private distanceKeywords: Map<string, number>;

  constructor() {
    this.initializeSpatialKeywords();
    this.initializeDistanceKeywords();
  }

  /**
   * 初始化空间关系关键词
   */
  private initializeSpatialKeywords(): void {
    this.spatialKeywords = new Map([
      // 垂直关系
      ['上面', SpatialRelationType.ABOVE],
      ['上方', SpatialRelationType.ABOVE],
      ['顶部', SpatialRelationType.ABOVE],
      ['下面', SpatialRelationType.BELOW],
      ['下方', SpatialRelationType.BELOW],
      ['底部', SpatialRelationType.BELOW],
      
      // 水平关系
      ['左边', SpatialRelationType.LEFT],
      ['左侧', SpatialRelationType.LEFT],
      ['右边', SpatialRelationType.RIGHT],
      ['右侧', SpatialRelationType.RIGHT],
      ['前面', SpatialRelationType.FRONT],
      ['前方', SpatialRelationType.FRONT],
      ['后面', SpatialRelationType.BACK],
      ['后方', SpatialRelationType.BACK],
      
      // 包含关系
      ['里面', SpatialRelationType.INSIDE],
      ['内部', SpatialRelationType.INSIDE],
      ['中间', SpatialRelationType.INSIDE],
      ['外面', SpatialRelationType.OUTSIDE],
      ['外部', SpatialRelationType.OUTSIDE],
      
      // 距离关系
      ['旁边', SpatialRelationType.ADJACENT],
      ['附近', SpatialRelationType.NEAR],
      ['靠近', SpatialRelationType.NEAR],
      ['远离', SpatialRelationType.FAR],
      ['对面', SpatialRelationType.OPPOSITE],
      ['相对', SpatialRelationType.OPPOSITE]
    ]);
  }

  /**
   * 初始化距离关键词
   */
  private initializeDistanceKeywords(): void {
    this.distanceKeywords = new Map([
      ['紧挨着', 0.5],
      ['很近', 1.0],
      ['附近', 2.0],
      ['不远', 3.0],
      ['较远', 5.0],
      ['很远', 8.0]
    ]);
  }

  /**
   * 解析空间关系
   */
  parse(nlpResult: NLPResult, elements: SceneElement[]): SpatialRelation[] {
    const relations: SpatialRelation[] = [];
    const text = nlpResult.tokens.join(' ');

    // 使用正则表达式匹配空间关系模式
    const patterns = [
      // "A在B的左边"
      /(\w+)在(\w+)的(\w+)/g,
      // "A位于B左边"
      /(\w+)位于(\w+)(\w+)/g,
      // "A放在B上面"
      /(\w+)放在(\w+)(\w+)/g,
      // "A靠近B"
      /(\w+)(\w+)(\w+)/g
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const relation = this.parseRelationMatch(match, elements);
        if (relation) {
          relations.push(relation);
        }
      }
    });

    return relations;
  }

  /**
   * 解析关系匹配结果
   */
  private parseRelationMatch(match: RegExpExecArray, elements: SceneElement[]): SpatialRelation | null {
    if (match.length < 4) return null;

    const sourceObject = match[1];
    const targetObject = match[2];
    const relationKeyword = match[3];

    // 检查对象是否存在于元素列表中
    const sourceExists = elements.some(e => e.name.includes(sourceObject));
    const targetExists = elements.some(e => e.name.includes(targetObject));

    if (!sourceExists || !targetExists) return null;

    // 获取空间关系类型
    const relationType = this.spatialKeywords.get(relationKeyword);
    if (!relationType) return null;

    // 获取距离信息
    const distance = this.distanceKeywords.get(relationKeyword) || undefined;

    return {
      type: relationType,
      source: sourceObject,
      target: targetObject,
      distance,
      confidence: 0.8
    };
  }
}

/**
 * 场景意图分类器
 */
export class SceneIntentClassifier {
  private sceneTypeKeywords: Map<string, string>;
  private styleKeywords: Map<string, string>;
  private functionalityKeywords: Map<string, string[]>;

  constructor() {
    this.initializeKeywords();
  }

  /**
   * 初始化关键词映射
   */
  private initializeKeywords(): void {
    // 场景类型关键词
    this.sceneTypeKeywords = new Map([
      ['办公室', 'office'],
      ['办公', 'office'],
      ['工作', 'office'],
      ['客厅', 'living_room'],
      ['起居室', 'living_room'],
      ['休息', 'living_room'],
      ['卧室', 'bedroom'],
      ['睡觉', 'bedroom'],
      ['休息室', 'bedroom'],
      ['厨房', 'kitchen'],
      ['烹饪', 'kitchen'],
      ['做饭', 'kitchen'],
      ['教室', 'classroom'],
      ['学习', 'classroom'],
      ['上课', 'classroom'],
      ['会议室', 'meeting_room'],
      ['会议', 'meeting_room'],
      ['讨论', 'meeting_room'],
      ['餐厅', 'restaurant'],
      ['用餐', 'restaurant'],
      ['吃饭', 'restaurant']
    ]);

    // 风格关键词
    this.styleKeywords = new Map([
      ['现代', 'modern'],
      ['当代', 'contemporary'],
      ['简约', 'minimalist'],
      ['极简', 'minimalist'],
      ['古典', 'classical'],
      ['传统', 'traditional'],
      ['工业', 'industrial'],
      ['北欧', 'scandinavian'],
      ['乡村', 'rustic'],
      ['田园', 'rustic'],
      ['豪华', 'luxury'],
      ['奢华', 'luxury'],
      ['复古', 'vintage'],
      ['怀旧', 'vintage']
    ]);

    // 功能关键词
    this.functionalityKeywords = new Map([
      ['工作', ['work', 'productivity']],
      ['办公', ['work', 'business']],
      ['休息', ['relax', 'comfort']],
      ['放松', ['relax', 'leisure']],
      ['会议', ['meeting', 'collaboration']],
      ['讨论', ['discussion', 'communication']],
      ['学习', ['study', 'education']],
      ['读书', ['reading', 'study']],
      ['娱乐', ['entertainment', 'fun']],
      ['游戏', ['gaming', 'entertainment']],
      ['用餐', ['dining', 'eating']],
      ['烹饪', ['cooking', 'food_preparation']]
    ]);
  }

  /**
   * 分类场景意图
   */
  async classify(description: string, elements: SceneElement[]): Promise<SceneIntent> {
    const sceneType = this.classifySceneType(description);
    const style = this.classifyStyle(description);
    const functionality = this.extractFunctionality(description);
    const mood = this.extractMood(description);
    const timeOfDay = this.extractTimeOfDay(description);
    const weather = this.extractWeather(description);

    return {
      sceneType,
      style,
      functionality,
      mood,
      timeOfDay,
      weather
    };
  }

  /**
   * 分类场景类型
   */
  private classifySceneType(description: string): string {
    let bestMatch = 'general';
    let maxScore = 0;

    this.sceneTypeKeywords.forEach((sceneType, keyword) => {
      const regex = new RegExp(keyword, 'gi');
      const matches = description.match(regex);
      if (matches) {
        const score = matches.length;
        if (score > maxScore) {
          maxScore = score;
          bestMatch = sceneType;
        }
      }
    });

    return bestMatch;
  }

  /**
   * 分类风格
   */
  private classifyStyle(description: string): string {
    let bestMatch = 'modern';
    let maxScore = 0;

    this.styleKeywords.forEach((style, keyword) => {
      const regex = new RegExp(keyword, 'gi');
      const matches = description.match(regex);
      if (matches) {
        const score = matches.length;
        if (score > maxScore) {
          maxScore = score;
          bestMatch = style;
        }
      }
    });

    return bestMatch;
  }

  /**
   * 提取功能需求
   */
  private extractFunctionality(description: string): string[] {
    const functionality: Set<string> = new Set();

    this.functionalityKeywords.forEach((functions, keyword) => {
      const regex = new RegExp(keyword, 'gi');
      if (regex.test(description)) {
        functions.forEach(func => functionality.add(func));
      }
    });

    return Array.from(functionality);
  }

  /**
   * 提取情感色调
   */
  private extractMood(description: string): string | undefined {
    const moodKeywords = new Map([
      ['温馨', 'cozy'],
      ['舒适', 'comfortable'],
      ['专业', 'professional'],
      ['正式', 'formal'],
      ['活泼', 'playful'],
      ['有趣', 'fun'],
      ['安静', 'quiet'],
      ['宁静', 'peaceful'],
      ['热闹', 'lively'],
      ['繁忙', 'busy']
    ]);

    for (const [keyword, mood] of moodKeywords) {
      if (description.includes(keyword)) {
        return mood;
      }
    }

    return undefined;
  }

  /**
   * 提取时间设定
   */
  private extractTimeOfDay(description: string): string | undefined {
    const timeKeywords = new Map([
      ['早晨', 'morning'],
      ['上午', 'morning'],
      ['中午', 'noon'],
      ['下午', 'afternoon'],
      ['傍晚', 'evening'],
      ['晚上', 'evening'],
      ['夜晚', 'night'],
      ['深夜', 'late_night']
    ]);

    for (const [keyword, time] of timeKeywords) {
      if (description.includes(keyword)) {
        return time;
      }
    }

    return undefined;
  }

  /**
   * 提取天气设定
   */
  private extractWeather(description: string): string | undefined {
    const weatherKeywords = new Map([
      ['晴天', 'sunny'],
      ['阳光', 'sunny'],
      ['多云', 'cloudy'],
      ['阴天', 'overcast'],
      ['雨天', 'rainy'],
      ['下雨', 'rainy'],
      ['雪天', 'snowy'],
      ['下雪', 'snowy']
    ]);

    for (const [keyword, weather] of weatherKeywords) {
      if (description.includes(keyword)) {
        return weather;
      }
    }

    return undefined;
  }
}

/**
 * NLP处理器
 */
export class NLPProcessor {
  private config: NLPProcessorConfig;

  constructor(config: NLPProcessorConfig = {}) {
    this.config = {
      language: 'zh-CN',
      enableNER: true,
      enablePOS: true,
      enableDependency: false,
      enableSentiment: true,
      ...config
    };
  }

  /**
   * 处理文本
   */
  async process(text: string): Promise<NLPResult> {
    // 分词
    const tokens = this.tokenize(text);
    
    // 词性标注
    const posTags = this.config.enablePOS ? this.posTag(tokens) : [];
    
    // 命名实体识别
    const entities = this.config.enableNER ? this.extractEntities(text) : [];
    
    // 情感分析
    const sentiment = this.config.enableSentiment ? this.analyzeSentiment(text) : undefined;

    return {
      tokens,
      posTags,
      entities,
      sentiment
    };
  }

  /**
   * 分词
   */
  private tokenize(text: string): string[] {
    // 简化的中文分词实现
    // 实际应该使用专业的分词工具如jieba
    return text.split(/[\s，。！？；：、]+/).filter(token => token.length > 0);
  }

  /**
   * 词性标注
   */
  private posTag(tokens: string[]): Array<{ word: string; tag: string }> {
    // 简化的词性标注实现
    return tokens.map(token => ({
      word: token,
      tag: this.guessPartOfSpeech(token)
    }));
  }

  /**
   * 猜测词性
   */
  private guessPartOfSpeech(word: string): string {
    // 简化的词性猜测
    const nounKeywords = ['桌子', '椅子', '沙发', '床', '柜子', '灯', '房间', '办公室'];
    const adjKeywords = ['大', '小', '红色', '蓝色', '现代', '古典', '温馨'];
    const verbKeywords = ['放置', '摆放', '设置', '添加', '创建'];

    if (nounKeywords.some(keyword => word.includes(keyword))) return 'N';
    if (adjKeywords.some(keyword => word.includes(keyword))) return 'A';
    if (verbKeywords.some(keyword => word.includes(keyword))) return 'V';
    
    return 'N'; // 默认为名词
  }

  /**
   * 命名实体识别
   */
  private extractEntities(text: string): Array<{ text: string; label: string; start: number; end: number }> {
    const entities: Array<{ text: string; label: string; start: number; end: number }> = [];
    
    // 对象实体
    const objectKeywords = [
      '桌子', '椅子', '沙发', '床', '柜子', '灯', '电视', '电脑', '书', '花', '植物',
      '冰箱', '洗衣机', '空调', '窗帘', '地毯', '画', '镜子', '钟表', '音响'
    ];
    
    // 环境实体
    const environmentKeywords = [
      '房间', '客厅', '卧室', '厨房', '办公室', '教室', '会议室', '餐厅', '浴室', '阳台'
    ];
    
    // 颜色实体
    const colorKeywords = [
      '红色', '蓝色', '绿色', '黄色', '黑色', '白色', '灰色', '棕色', '粉色', '紫色'
    ];

    // 提取对象实体
    objectKeywords.forEach(keyword => {
      const index = text.indexOf(keyword);
      if (index !== -1) {
        entities.push({
          text: keyword,
          label: 'OBJECT',
          start: index,
          end: index + keyword.length
        });
      }
    });

    // 提取环境实体
    environmentKeywords.forEach(keyword => {
      const index = text.indexOf(keyword);
      if (index !== -1) {
        entities.push({
          text: keyword,
          label: 'ENVIRONMENT',
          start: index,
          end: index + keyword.length
        });
      }
    });

    // 提取颜色实体
    colorKeywords.forEach(keyword => {
      const index = text.indexOf(keyword);
      if (index !== -1) {
        entities.push({
          text: keyword,
          label: 'COLOR',
          start: index,
          end: index + keyword.length
        });
      }
    });

    return entities;
  }

  /**
   * 情感分析
   */
  private analyzeSentiment(text: string): { label: string; score: number } {
    // 简化的情感分析实现
    const positiveKeywords = ['好', '棒', '美', '舒适', '温馨', '喜欢', '满意'];
    const negativeKeywords = ['差', '坏', '丑', '不舒服', '讨厌', '不满意'];

    let positiveScore = 0;
    let negativeScore = 0;

    positiveKeywords.forEach(keyword => {
      if (text.includes(keyword)) positiveScore++;
    });

    negativeKeywords.forEach(keyword => {
      if (text.includes(keyword)) negativeScore++;
    });

    if (positiveScore > negativeScore) {
      return { label: 'positive', score: 0.7 };
    } else if (negativeScore > positiveScore) {
      return { label: 'negative', score: 0.7 };
    } else {
      return { label: 'neutral', score: 0.5 };
    }
  }
}
