/**
 * RAG数字人交互系统示例
 * 展示第一阶段基础框架的功能：知识场景编辑器、数字人路径系统、知识库管理
 */

import * as THREE from 'three';
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { Scene } from '../engine/src/core/Scene';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/components/Transform';
import { Camera } from '../engine/src/core/components/Camera';
import { MeshRenderer } from '../engine/src/rendering/components/MeshRenderer';

// RAG系统组件
import { KnowledgeSceneEditor, KnowledgeData } from '../engine/src/rag/scene/KnowledgeSceneEditor';
import { DigitalHumanPathEditor, PathPointType } from '../engine/src/rag/navigation/DigitalHumanPathEditor';
import { DigitalHumanNavigationComponent, NavigationState } from '../engine/src/rag/navigation/DigitalHumanNavigationComponent';

/**
 * RAG数字人交互系统示例类
 */
export class RAGDigitalHumanSystemExample {
  private engine: Engine;
  private world: World;
  private scene: Scene;
  private camera: THREE.Camera;
  private renderer: THREE.WebGLRenderer;
  
  // RAG系统组件
  private knowledgeSceneEditor: KnowledgeSceneEditor;
  private pathEditor: DigitalHumanPathEditor;
  private digitalHuman: Entity;
  private navigationComponent: DigitalHumanNavigationComponent;
  
  // 示例数据
  private knowledgePoints: KnowledgeData[] = [];
  private currentMode: 'edit' | 'navigate' | 'interact' = 'edit';

  constructor(canvas: HTMLCanvasElement) {
    // 初始化引擎
    this.engine = new Engine({
      canvas,
      autoStart: true,
      debug: true
    });

    this.world = this.engine.getWorld();
    this.renderer = this.engine.getRenderer();
    
    this.initializeScene();
    this.initializeRAGSystems();
    this.setupExampleData();
    this.setupEventListeners();
    this.setupUI();
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建场景
    this.scene = new Scene(this.world, {
      name: 'RAG数字人交互示例场景'
    });
    
    this.world.addScene(this.scene);
    this.world.setActiveScene(this.scene);

    // 创建相机
    const cameraEntity = this.world.createEntity('主相机');
    const cameraComponent = new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000
    });
    
    cameraEntity.addComponent(new Transform(cameraEntity, {
      position: new THREE.Vector3(0, 5, 10),
      rotation: new THREE.Euler(-0.3, 0, 0)
    }));
    cameraEntity.addComponent(cameraComponent);
    
    this.camera = cameraComponent.camera;

    // 创建基础环境
    this.createEnvironment();
    
    // 创建数字人
    this.createDigitalHuman();
  }

  /**
   * 创建环境
   */
  private createEnvironment(): void {
    // 创建地面
    const groundEntity = this.world.createEntity('地面');
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    
    groundEntity.addComponent(new Transform(groundEntity, {
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(-Math.PI / 2, 0, 0)
    }));
    groundEntity.addComponent(new MeshRenderer(groundEntity, { mesh: groundMesh }));

    // 创建一些展示物体
    this.createExhibits();
    
    // 添加光照
    this.setupLighting();
  }

  /**
   * 创建展示物体
   */
  private createExhibits(): void {
    const exhibits = [
      { name: '展品A', position: new THREE.Vector3(-5, 1, -3), color: 0xff6b6b },
      { name: '展品B', position: new THREE.Vector3(0, 1, -5), color: 0x4ecdc4 },
      { name: '展品C', position: new THREE.Vector3(5, 1, -3), color: 0x45b7d1 },
      { name: '展品D', position: new THREE.Vector3(-3, 1, 2), color: 0xf9ca24 },
      { name: '展品E', position: new THREE.Vector3(3, 1, 2), color: 0x6c5ce7 }
    ];

    exhibits.forEach(exhibit => {
      const entity = this.world.createEntity(exhibit.name);
      const geometry = new THREE.BoxGeometry(1, 2, 1);
      const material = new THREE.MeshLambertMaterial({ color: exhibit.color });
      const mesh = new THREE.Mesh(geometry, material);
      
      entity.addComponent(new Transform(entity, { position: exhibit.position }));
      entity.addComponent(new MeshRenderer(entity, { mesh }));
    });
  }

  /**
   * 设置光照
   */
  private setupLighting(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.getThreeScene().add(ambientLight);
    
    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    this.scene.getThreeScene().add(directionalLight);
  }

  /**
   * 创建数字人
   */
  private createDigitalHuman(): void {
    this.digitalHuman = this.world.createEntity('数字人');
    
    // 创建简单的数字人模型（用胶囊体代替）
    const geometry = new THREE.CapsuleGeometry(0.3, 1.5, 4, 8);
    const material = new THREE.MeshLambertMaterial({ color: 0x8e44ad });
    const mesh = new THREE.Mesh(geometry, material);
    
    this.digitalHuman.addComponent(new Transform(this.digitalHuman, {
      position: new THREE.Vector3(0, 1, 0)
    }));
    this.digitalHuman.addComponent(new MeshRenderer(this.digitalHuman, { mesh }));
  }

  /**
   * 初始化RAG系统
   */
  private initializeRAGSystems(): void {
    // 初始化知识场景编辑器
    this.knowledgeSceneEditor = new KnowledgeSceneEditor(
      this.world,
      this.scene.getThreeScene(),
      this.camera
    );
    
    this.world.addSystem(this.knowledgeSceneEditor);
    
    // 初始化路径编辑器
    this.pathEditor = new DigitalHumanPathEditor(
      this.scene.getThreeScene(),
      {
        pathType: 'digital_human_navigation',
        allowCurves: true,
        showDirection: true,
        enableStops: true,
        segments: 50,
        debug: true
      }
    );
    
    // 初始化导航组件
    this.navigationComponent = new DigitalHumanNavigationComponent(
      this.digitalHuman,
      {
        moveSpeed: 2.0,
        rotationSpeed: 5.0,
        stopThreshold: 0.5,
        smoothRotation: true,
        autoStart: false,
        debug: true
      }
    );
    
    this.digitalHuman.addComponent(this.navigationComponent);
    
    // 设置事件回调
    this.setupRAGEventHandlers();
  }

  /**
   * 设置RAG系统事件处理器
   */
  private setupRAGEventHandlers(): void {
    // 知识点事件
    this.knowledgeSceneEditor.onKnowledgePointAdded = (knowledgePoint) => {
      console.log('知识点已添加:', knowledgePoint.knowledge.title);
      this.updateUI();
    };
    
    this.knowledgeSceneEditor.onKnowledgePointSelected = (knowledgePoint) => {
      if (knowledgePoint) {
        console.log('选中知识点:', knowledgePoint.knowledge.title);
        this.showKnowledgeDetails(knowledgePoint.knowledge);
      }
    };
    
    // 路径事件
    this.pathEditor.onPathChanged = () => {
      console.log('路径已更改');
      this.updateNavigationPath();
    };
    
    this.pathEditor.onPointAdded = (point) => {
      console.log('路径点已添加:', point.type);
    };
    
    // 导航事件
    this.navigationComponent.onStateChanged = (data) => {
      console.log('导航状态变化:', data.state);
      this.updateNavigationUI(data);
    };
    
    this.navigationComponent.onReachedStopPoint = (stopPoint) => {
      console.log('到达停留点:', stopPoint.id);
      this.handleStopPointReached(stopPoint);
    };
    
    this.navigationComponent.onPathCompleted = () => {
      console.log('路径完成');
      this.handlePathCompleted();
    };
  }

  /**
   * 设置示例数据
   */
  private setupExampleData(): void {
    // 创建示例知识点
    this.knowledgePoints = [
      {
        id: '1',
        title: '展品A介绍',
        content: '这是展品A，它展示了古代文明的艺术成就...',
        type: 'text',
        tags: ['艺术', '古代', '文明'],
        category: 'cultural',
        priority: 2,
        relatedTopics: ['历史', '艺术史']
      },
      {
        id: '2',
        title: '展品B技术说明',
        content: '展品B采用了先进的3D打印技术制作...',
        type: 'text',
        tags: ['技术', '3D打印', '现代'],
        category: 'technical',
        priority: 1,
        relatedTopics: ['技术', '制造']
      },
      {
        id: '3',
        title: '展厅导览信息',
        content: '欢迎来到虚拟展厅，这里展示了各种珍贵的展品...',
        type: 'text',
        tags: ['导览', '欢迎', '介绍'],
        category: 'general',
        priority: 3,
        relatedTopics: ['导览', '服务']
      }
    ];
    
    // 添加知识点到场景
    this.addKnowledgePointsToScene();
    
    // 创建示例路径
    this.createExamplePath();
  }

  /**
   * 添加知识点到场景
   */
  private addKnowledgePointsToScene(): void {
    const positions = [
      new THREE.Vector3(-5, 2.5, -3), // 展品A上方
      new THREE.Vector3(0, 2.5, -5),  // 展品B上方
      new THREE.Vector3(0, 2.5, 0)    // 中央位置
    ];
    
    this.knowledgePoints.forEach((knowledge, index) => {
      if (index < positions.length) {
        this.knowledgeSceneEditor.addKnowledgePoint(positions[index], knowledge);
      }
    });
  }

  /**
   * 创建示例路径
   */
  private createExamplePath(): void {
    // 创建导览路径
    const pathPoints = [
      { position: new THREE.Vector3(0, 0.1, 5), type: 'normal' as PathPointType },
      { position: new THREE.Vector3(-5, 0.1, -1), type: 'stop' as PathPointType },
      { position: new THREE.Vector3(-2, 0.1, -5), type: 'waypoint' as PathPointType },
      { position: new THREE.Vector3(2, 0.1, -5), type: 'stop' as PathPointType },
      { position: new THREE.Vector3(5, 0.1, -1), type: 'waypoint' as PathPointType },
      { position: new THREE.Vector3(0, 0.1, 3), type: 'stop' as PathPointType }
    ];
    
    pathPoints.forEach(point => {
      this.pathEditor.addPathPoint(point.position, point.type, {
        waitTime: point.type === 'stop' ? 3000 : 0,
        actions: point.type === 'stop' ? ['介绍展品', '播放动画'] : []
      });
    });
    
    // 设置路径到导航组件
    this.updateNavigationPath();
  }

  /**
   * 更新导航路径
   */
  private updateNavigationPath(): void {
    this.navigationComponent.setPath(this.pathEditor);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 键盘事件
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case '1':
          this.setMode('edit');
          break;
        case '2':
          this.setMode('navigate');
          break;
        case '3':
          this.setMode('interact');
          break;
        case ' ':
          event.preventDefault();
          this.toggleNavigation();
          break;
        case 'r':
          this.resetDemo();
          break;
      }
    });
  }

  /**
   * 设置模式
   */
  private setMode(mode: 'edit' | 'navigate' | 'interact'): void {
    this.currentMode = mode;
    console.log(`切换到${mode}模式`);
    this.updateUI();
  }

  /**
   * 切换导航状态
   */
  private toggleNavigation(): void {
    const state = this.navigationComponent.getState();

    if (state === NavigationState.IDLE || state === NavigationState.STOPPED) {
      this.navigationComponent.startMoving();
    } else if (state === NavigationState.MOVING) {
      this.navigationComponent.pauseMoving();
    } else if (state === NavigationState.PAUSED) {
      this.navigationComponent.resumeMoving();
    }
  }

  /**
   * 重置演示
   */
  private resetDemo(): void {
    this.navigationComponent.reset();
    console.log('演示已重置');
  }

  /**
   * 显示知识详情
   */
  private showKnowledgeDetails(knowledge: KnowledgeData): void {
    const detailsDiv = document.getElementById('knowledge-details');
    if (detailsDiv) {
      detailsDiv.innerHTML = `
        <h3>${knowledge.title}</h3>
        <p><strong>类型:</strong> ${knowledge.type}</p>
        <p><strong>类别:</strong> ${knowledge.category}</p>
        <p><strong>标签:</strong> ${knowledge.tags.join(', ')}</p>
        <p><strong>内容:</strong></p>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
          ${knowledge.content}
        </div>
      `;
    }
  }

  /**
   * 更新导航UI
   */
  private updateNavigationUI(data: any): void {
    const statusDiv = document.getElementById('navigation-status');
    if (statusDiv) {
      statusDiv.innerHTML = `
        <p><strong>状态:</strong> ${data.state}</p>
        <p><strong>进度:</strong> ${(data.progress * 100).toFixed(1)}%</p>
        <p><strong>位置:</strong> (${data.position.x.toFixed(1)}, ${data.position.y.toFixed(1)}, ${data.position.z.toFixed(1)})</p>
        ${data.currentStopPoint ? `<p><strong>当前停留点:</strong> ${data.currentStopPoint.id}</p>` : ''}
      `;
    }
  }

  /**
   * 处理到达停留点
   */
  private handleStopPointReached(stopPoint: any): void {
    // 这里可以触发知识点展示、动画播放等
    console.log(`到达停留点 ${stopPoint.id}，执行动作:`, stopPoint.triggerActions);

    // 模拟知识点交互
    const nearbyKnowledge = this.findNearbyKnowledge(stopPoint.position);
    if (nearbyKnowledge) {
      this.showKnowledgeDetails(nearbyKnowledge);
    }
  }

  /**
   * 查找附近的知识点
   */
  private findNearbyKnowledge(position: THREE.Vector3): KnowledgeData | null {
    const allKnowledgePoints = this.knowledgeSceneEditor.getAllKnowledgePoints();

    for (const kp of allKnowledgePoints) {
      const distance = position.distanceTo(kp.position);
      if (distance < 3.0) { // 3米范围内
        return kp.knowledge;
      }
    }

    return null;
  }

  /**
   * 处理路径完成
   */
  private handlePathCompleted(): void {
    console.log('导览完成！');

    const statusDiv = document.getElementById('navigation-status');
    if (statusDiv) {
      statusDiv.innerHTML += '<p style="color: green;"><strong>导览完成！</strong></p>';
    }
  }

  /**
   * 更新UI
   */
  private updateUI(): void {
    const modeDiv = document.getElementById('current-mode');
    if (modeDiv) {
      modeDiv.textContent = `当前模式: ${this.currentMode}`;
    }

    const knowledgeCountDiv = document.getElementById('knowledge-count');
    if (knowledgeCountDiv) {
      const count = this.knowledgeSceneEditor.getAllKnowledgePoints().length;
      knowledgeCountDiv.textContent = `知识点数量: ${count}`;
    }

    const pathInfoDiv = document.getElementById('path-info');
    if (pathInfoDiv) {
      const pathPoints = this.pathEditor.getPathPoints();
      const stopPoints = this.pathEditor.getStopPoints();
      pathInfoDiv.innerHTML = `
        <p>路径点数量: ${pathPoints.length}</p>
        <p>停留点数量: ${stopPoints.length}</p>
        <p>路径长度: ${this.pathEditor.getLength().toFixed(2)}m</p>
      `;
    }
  }

  /**
   * 设置UI
   */
  private setupUI(): void {
    // 创建控制面板
    const controlPanel = document.createElement('div');
    controlPanel.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-width: 300px;
    `;

    controlPanel.innerHTML = `
      <h3 style="margin: 0 0 10px 0;">RAG数字人交互系统</h3>
      <div id="current-mode">当前模式: ${this.currentMode}</div>
      <div id="knowledge-count">知识点数量: 0</div>
      <div id="path-info">
        <p>路径点数量: 0</p>
        <p>停留点数量: 0</p>
        <p>路径长度: 0m</p>
      </div>
      <div id="navigation-status">
        <p><strong>状态:</strong> 空闲</p>
        <p><strong>进度:</strong> 0%</p>
      </div>
      <hr style="margin: 10px 0;">
      <div style="font-size: 12px;">
        <p><strong>控制说明:</strong></p>
        <p>1 - 编辑模式</p>
        <p>2 - 导航模式</p>
        <p>3 - 交互模式</p>
        <p>空格 - 开始/暂停导航</p>
        <p>R - 重置演示</p>
      </div>
    `;

    document.body.appendChild(controlPanel);

    // 创建知识详情面板
    const detailsPanel = document.createElement('div');
    detailsPanel.id = 'knowledge-details';
    detailsPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(255, 255, 255, 0.95);
      color: black;
      padding: 15px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-width: 350px;
      max-height: 400px;
      overflow-y: auto;
    `;

    detailsPanel.innerHTML = '<p>点击知识点查看详情</p>';
    document.body.appendChild(detailsPanel);

    // 初始化UI
    this.updateUI();
  }

  /**
   * 获取系统信息
   */
  public getSystemInfo(): any {
    return {
      mode: this.currentMode,
      knowledgePoints: this.knowledgeSceneEditor.getAllKnowledgePoints().length,
      pathPoints: this.pathEditor.getPathPoints().length,
      stopPoints: this.pathEditor.getStopPoints().length,
      pathLength: this.pathEditor.getLength(),
      navigationState: this.navigationComponent.getState(),
      navigationProgress: this.navigationComponent.getProgress()
    };
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 清理RAG系统
    this.knowledgeSceneEditor.dispose();
    this.pathEditor.dispose();
    this.navigationComponent.dispose();

    // 清理引擎
    this.engine.dispose();

    // 清理UI
    const controlPanel = document.querySelector('div[style*="position: fixed"]');
    if (controlPanel) {
      controlPanel.remove();
    }

    const detailsPanel = document.getElementById('knowledge-details');
    if (detailsPanel) {
      detailsPanel.remove();
    }
  }
}

// 导出示例类
export default RAGDigitalHumanSystemExample;
