/**
 * 动作序列编排系统使用示例
 * 展示如何使用可视化的动作序列编排器创建、编辑和播放复杂动作序列
 */

import { 
  ActionSequenceComposer,
  ActionClip,
  ActionType,
  ActionPriority,
  BlendMode,
  EasingType
} from '../engine/src/avatar/animation/ActionSequenceComposer';

/**
 * 动作序列编排示例类
 */
export class ActionSequenceComposerExample {
  private composer: ActionSequenceComposer;
  private currentSequenceId?: string;

  constructor() {
    this.initializeExample();
  }

  /**
   * 初始化示例
   */
  private async initializeExample(): Promise<void> {
    console.log('🎬 初始化动作序列编排系统示例...');

    // 1. 创建动作序列编排器
    this.composer = new ActionSequenceComposer({
      defaultFrameRate: 30,
      maxTracks: 10,
      maxDuration: 300, // 5分钟
      autoSave: true,
      autoSaveInterval: 15, // 15秒
      debug: true
    });

    // 2. 添加自定义动作片段
    await this.addCustomActionClips();

    // 3. 演示编排功能
    await this.demonstrateComposerFeatures();

    console.log('✅ 动作序列编排系统示例初始化完成');
  }

  /**
   * 添加自定义动作片段
   */
  private async addCustomActionClips(): Promise<void> {
    console.log('📚 添加自定义动作片段...');

    const customClips: ActionClip[] = [
      {
        id: 'greeting_bow',
        name: '鞠躬问候',
        type: ActionType.GESTURE,
        animationData: null,
        duration: 2.5,
        loop: false,
        priority: ActionPriority.HIGH,
        tags: ['greeting', 'formal', 'bow']
      },
      {
        id: 'thinking_pose',
        name: '思考姿势',
        type: ActionType.IDLE,
        animationData: null,
        duration: 4.0,
        loop: true,
        priority: ActionPriority.NORMAL,
        tags: ['thinking', 'contemplative', 'pose']
      },
      {
        id: 'excited_jump',
        name: '兴奋跳跃',
        type: ActionType.JUMP,
        animationData: null,
        duration: 1.8,
        loop: false,
        priority: ActionPriority.HIGH,
        tags: ['excited', 'energetic', 'celebration']
      },
      {
        id: 'casual_walk',
        name: '休闲行走',
        type: ActionType.WALK,
        animationData: null,
        duration: 3.0,
        loop: true,
        priority: ActionPriority.NORMAL,
        tags: ['casual', 'relaxed', 'locomotion']
      },
      {
        id: 'presentation_gesture',
        name: '演示手势',
        type: ActionType.GESTURE,
        animationData: null,
        duration: 3.5,
        loop: false,
        priority: ActionPriority.HIGH,
        tags: ['presentation', 'professional', 'gesture']
      },
      {
        id: 'victory_dance',
        name: '胜利舞蹈',
        type: ActionType.DANCE,
        animationData: null,
        duration: 6.0,
        loop: true,
        priority: ActionPriority.NORMAL,
        tags: ['victory', 'celebration', 'dance']
      }
    ];

    for (const clip of customClips) {
      this.composer.addActionClip(clip);
    }

    console.log(`✅ 添加了 ${customClips.length} 个自定义动作片段`);
  }

  /**
   * 演示编排功能
   */
  private async demonstrateComposerFeatures(): Promise<void> {
    console.log('🎭 演示动作序列编排功能...');

    // 1. 创建基础序列
    await this.createBasicSequence();

    // 2. 演示轨道管理
    await this.demonstrateTrackManagement();

    // 3. 演示节点编辑
    await this.demonstrateNodeEditing();

    // 4. 演示播放控制
    await this.demonstratePlaybackControl();

    // 5. 演示撤销重做
    await this.demonstrateUndoRedo();

    // 6. 演示导出导入
    await this.demonstrateExportImport();

    // 7. 演示搜索和过滤
    await this.demonstrateSearchAndFilter();

    console.log('✅ 动作序列编排功能演示完成');
  }

  /**
   * 创建基础序列
   */
  private async createBasicSequence(): Promise<void> {
    console.log('1. 创建基础动作序列...');

    // 创建新序列
    this.currentSequenceId = this.composer.createSequence(
      '演示序列',
      '展示各种动作组合的演示序列'
    );

    console.log(`  ✅ 创建序列: ${this.currentSequenceId}`);

    // 添加额外轨道
    const handTrackId = this.composer.addTrack(this.currentSequenceId, '手部动作', 'hand');
    console.log(`  ✅ 添加手部轨道: ${handTrackId}`);

    // 获取默认轨道ID
    const sequence = this.composer.getCurrentSequence()!;
    const bodyTrack = sequence.tracks.find(t => t.type === 'body')!;
    const faceTrack = sequence.tracks.find(t => t.type === 'face')!;

    // 添加基础动作节点
    console.log('  添加基础动作节点...');

    // 身体轨道：待机 -> 行走 -> 跳跃 -> 舞蹈
    this.composer.addSequenceNode(this.currentSequenceId, bodyTrack.id, 'idle_basic', 0, 3);
    this.composer.addSequenceNode(this.currentSequenceId, bodyTrack.id, 'casual_walk', 3, 4);
    this.composer.addSequenceNode(this.currentSequenceId, bodyTrack.id, 'excited_jump', 7, 2);
    this.composer.addSequenceNode(this.currentSequenceId, bodyTrack.id, 'victory_dance', 9, 6);

    // 面部轨道：思考 -> 问候
    this.composer.addSequenceNode(this.currentSequenceId, faceTrack.id, 'thinking_pose', 1, 3);
    this.composer.addSequenceNode(this.currentSequenceId, faceTrack.id, 'greeting_bow', 5, 2.5);

    // 手部轨道：演示手势
    if (handTrackId) {
      this.composer.addSequenceNode(this.currentSequenceId, handTrackId, 'presentation_gesture', 4, 3.5);
      this.composer.addSequenceNode(this.currentSequenceId, handTrackId, 'wave_hello', 8, 3);
    }

    console.log('  ✅ 基础序列创建完成');
  }

  /**
   * 演示轨道管理
   */
  private async demonstrateTrackManagement(): Promise<void> {
    console.log('2. 演示轨道管理...');

    if (!this.currentSequenceId) return;

    // 重命名轨道
    const sequence = this.composer.getCurrentSequence()!;
    const bodyTrack = sequence.tracks.find(t => t.type === 'body')!;
    
    console.log('  重命名身体轨道...');
    this.composer.renameTrack(this.currentSequenceId, bodyTrack.id, '主要身体动作');

    // 设置轨道静音
    console.log('  设置面部轨道静音...');
    const faceTrack = sequence.tracks.find(t => t.type === 'face')!;
    this.composer.setTrackMuted(this.currentSequenceId, faceTrack.id, true);

    await this.wait(1000);

    // 取消静音
    console.log('  取消面部轨道静音...');
    this.composer.setTrackMuted(this.currentSequenceId, faceTrack.id, false);

    // 锁定轨道
    console.log('  锁定手部轨道...');
    const handTrack = sequence.tracks.find(t => t.type === 'hand')!;
    if (handTrack) {
      this.composer.setTrackLocked(this.currentSequenceId, handTrack.id, true);
    }

    console.log('  ✅ 轨道管理演示完成');
  }

  /**
   * 演示节点编辑
   */
  private async demonstrateNodeEditing(): Promise<void> {
    console.log('3. 演示节点编辑...');

    if (!this.currentSequenceId) return;

    const sequence = this.composer.getCurrentSequence()!;
    const bodyTrack = sequence.tracks.find(t => t.type === 'body')!;

    if (bodyTrack.nodes.length > 0) {
      const firstNode = bodyTrack.nodes[0];

      // 移动节点
      console.log('  移动第一个节点...');
      this.composer.moveSequenceNode(this.currentSequenceId, bodyTrack.id, firstNode.id, 0.5);

      await this.wait(500);

      // 调整节点持续时间
      console.log('  调整节点持续时间...');
      this.composer.resizeSequenceNode(this.currentSequenceId, bodyTrack.id, firstNode.id, 2.5);

      await this.wait(500);

      // 恢复原始位置和时长
      console.log('  恢复节点原始设置...');
      this.composer.moveSequenceNode(this.currentSequenceId, bodyTrack.id, firstNode.id, 0);
      this.composer.resizeSequenceNode(this.currentSequenceId, bodyTrack.id, firstNode.id, 3);
    }

    console.log('  ✅ 节点编辑演示完成');
  }

  /**
   * 演示播放控制
   */
  private async demonstratePlaybackControl(): Promise<void> {
    console.log('4. 演示播放控制...');

    if (!this.currentSequenceId) return;

    // 开始播放
    console.log('  开始播放序列...');
    this.composer.play(this.currentSequenceId);

    await this.wait(2000);

    // 暂停播放
    console.log('  暂停播放...');
    this.composer.pause();

    await this.wait(1000);

    // 恢复播放
    console.log('  恢复播放...');
    this.composer.resume();

    await this.wait(1000);

    // 跳转到指定时间
    console.log('  跳转到5秒位置...');
    this.composer.seekTo(5.0);

    await this.wait(1000);

    // 设置播放速度
    console.log('  设置2倍播放速度...');
    this.composer.setPlaybackSpeed(2.0);

    await this.wait(2000);

    // 恢复正常速度
    console.log('  恢复正常播放速度...');
    this.composer.setPlaybackSpeed(1.0);

    await this.wait(1000);

    // 停止播放
    console.log('  停止播放...');
    this.composer.stop();

    console.log('  ✅ 播放控制演示完成');
  }

  /**
   * 演示撤销重做
   */
  private async demonstrateUndoRedo(): Promise<void> {
    console.log('5. 演示撤销重做功能...');

    if (!this.currentSequenceId) return;

    const sequence = this.composer.getCurrentSequence()!;
    const bodyTrack = sequence.tracks.find(t => t.type === 'body')!;

    // 添加一个新节点
    console.log('  添加新节点...');
    const newNodeId = this.composer.addSequenceNode(
      this.currentSequenceId,
      bodyTrack.id,
      'jump_basic',
      15,
      1.5
    );

    await this.wait(500);

    // 撤销操作
    console.log('  执行撤销操作...');
    this.composer.undo();

    await this.wait(500);

    // 重做操作
    console.log('  执行重做操作...');
    this.composer.redo();

    await this.wait(500);

    // 再次撤销
    console.log('  再次撤销...');
    this.composer.undo();

    console.log('  ✅ 撤销重做演示完成');
  }

  /**
   * 演示导出导入
   */
  private async demonstrateExportImport(): Promise<void> {
    console.log('6. 演示导出导入功能...');

    if (!this.currentSequenceId) return;

    // 导出序列
    console.log('  导出当前序列...');
    const exportedData = this.composer.exportSequenceToJSON(this.currentSequenceId);
    
    if (exportedData) {
      console.log(`  ✅ 序列导出成功，数据长度: ${exportedData.length} 字符`);

      // 导入序列
      console.log('  导入序列副本...');
      const importedSequenceId = this.composer.importSequenceFromJSON(exportedData);
      
      if (importedSequenceId) {
        console.log(`  ✅ 序列导入成功: ${importedSequenceId}`);

        // 复制序列
        console.log('  复制序列...');
        const duplicatedSequenceId = this.composer.duplicateSequence(
          this.currentSequenceId,
          '演示序列 (复制版)'
        );
        
        if (duplicatedSequenceId) {
          console.log(`  ✅ 序列复制成功: ${duplicatedSequenceId}`);
        }
      }
    }

    console.log('  ✅ 导出导入演示完成');
  }

  /**
   * 演示搜索和过滤
   */
  private async demonstrateSearchAndFilter(): Promise<void> {
    console.log('7. 演示搜索和过滤功能...');

    // 按类型搜索动作片段
    const gestureClips = this.composer.getActionClips(ActionType.GESTURE);
    console.log(`  找到 ${gestureClips.length} 个手势动作片段`);

    const danceClips = this.composer.getActionClips(ActionType.DANCE);
    console.log(`  找到 ${danceClips.length} 个舞蹈动作片段`);

    // 按标签搜索
    const greetingClips = this.composer.searchActionClips({
      tags: ['greeting']
    });
    console.log(`  找到 ${greetingClips.length} 个问候动作片段`);

    // 按持续时间搜索
    const shortClips = this.composer.searchActionClips({
      duration: [0, 2.0]
    });
    console.log(`  找到 ${shortClips.length} 个短时长动作片段（<2秒）`);

    // 按名称搜索
    const jumpClips = this.composer.searchActionClips({
      name: 'jump'
    });
    console.log(`  找到 ${jumpClips.length} 个包含'jump'的动作片段`);

    // 获取所有序列
    const allSequences = this.composer.getAllSequences();
    console.log(`  当前共有 ${allSequences.length} 个动作序列`);

    console.log('  ✅ 搜索和过滤演示完成');
  }

  /**
   * 等待指定时间
   * @param ms 毫秒
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    console.log('🚀 动作序列编排系统示例正在运行...');

    // 监听编排器事件
    this.composer.on('sequenceCreated', (sequence) => {
      console.log(`📝 序列已创建: ${sequence.name}`);
    });

    this.composer.on('trackAdded', (sequence, track) => {
      console.log(`🛤️ 轨道已添加: ${track.name} -> ${sequence.name}`);
    });

    this.composer.on('nodeAdded', (sequence, track, node) => {
      console.log(`🎬 节点已添加: ${node.name} -> ${track.name}`);
    });

    this.composer.on('playbackStarted', (sequence, startTime) => {
      console.log(`▶️ 开始播放: ${sequence.name} (从 ${startTime}s)`);
    });

    this.composer.on('playbackStopped', () => {
      console.log('⏹️ 播放已停止');
    });

    this.composer.on('playbackTimeUpdated', (currentTime) => {
      // 每秒输出一次播放时间
      if (Math.floor(currentTime) !== Math.floor(currentTime - 1/30)) {
        console.log(`⏱️ 播放时间: ${currentTime.toFixed(1)}s`);
      }
    });

    this.composer.on('undoPerformed', () => {
      console.log('↶ 撤销操作已执行');
    });

    this.composer.on('redoPerformed', () => {
      console.log('↷ 重做操作已执行');
    });

    // 添加键盘控制
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case ' ': // 空格键：播放/暂停
          event.preventDefault();
          const state = this.composer.getPlaybackState();
          if (state.isPlaying && !state.isPaused) {
            this.composer.pause();
          } else if (state.isPlaying && state.isPaused) {
            this.composer.resume();
          } else {
            this.composer.play(this.currentSequenceId);
          }
          break;
        case 'Escape': // ESC：停止播放
          this.composer.stop();
          break;
        case 'z': // Z：撤销
          if (event.ctrlKey) {
            this.composer.undo();
          }
          break;
        case 'y': // Y：重做
          if (event.ctrlKey) {
            this.composer.redo();
          }
          break;
      }
    });

    console.log('💡 提示: 空格键播放/暂停，ESC停止，Ctrl+Z撤销，Ctrl+Y重做');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理动作序列编排系统示例...');

    if (this.composer) {
      this.composer.dispose();
    }

    console.log('✅ 动作序列编排系统示例已清理');
  }
}

// 导出示例类
export default ActionSequenceComposerExample;

// 如果直接运行此文件，则启动示例
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const example = new ActionSequenceComposerExample();
    await example.run();
  });
}
