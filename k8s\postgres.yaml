apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: rag-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: rag-system
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: rag-config
              key: DB_NAME
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: rag-config
              key: DB_USERNAME
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rag-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_INITDB_ARGS
          value: "--encoding=UTF8 --locale=C"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: init-script
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: init-script
        configMap:
          name: postgres-init-script

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: rag-system
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-script
  namespace: rag-system
data:
  init.sql: |
    -- 创建数据库
    CREATE DATABASE IF NOT EXISTS digital_human_rag;
    
    -- 使用数据库
    \c digital_human_rag;
    
    -- 启用UUID扩展
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    -- 数字人表
    CREATE TABLE IF NOT EXISTS digital_humans (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        model_config JSONB,
        voice_config JSONB,
        animation_config JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by UUID,
        status VARCHAR(50) DEFAULT 'active'
    );
    
    -- 知识库表
    CREATE TABLE IF NOT EXISTS knowledge_bases (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(100),
        language VARCHAR(10) DEFAULT 'zh-CN',
        vector_index_name VARCHAR(255),
        document_count INTEGER DEFAULT 0,
        total_chunks INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by UUID,
        status VARCHAR(50) DEFAULT 'active'
    );
    
    -- 知识库文档表
    CREATE TABLE IF NOT EXISTS knowledge_documents (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
        filename VARCHAR(255) NOT NULL,
        original_filename VARCHAR(255),
        file_path VARCHAR(500),
        file_size BIGINT,
        file_type VARCHAR(100),
        content_hash VARCHAR(64),
        metadata JSONB,
        chunk_count INTEGER DEFAULT 0,
        processing_status VARCHAR(50) DEFAULT 'pending',
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP,
        uploaded_by UUID
    );
    
    -- 数字人知识库绑定表
    CREATE TABLE IF NOT EXISTS digital_human_knowledge_bindings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        digital_human_id UUID REFERENCES digital_humans(id) ON DELETE CASCADE,
        knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
        binding_type VARCHAR(50) DEFAULT 'primary',
        priority INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT true,
        binding_config JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by UUID,
        UNIQUE(digital_human_id, knowledge_base_id)
    );
    
    -- 文档块表
    CREATE TABLE IF NOT EXISTS document_chunks (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        document_id UUID REFERENCES knowledge_documents(id) ON DELETE CASCADE,
        chunk_index INTEGER,
        content TEXT NOT NULL,
        content_hash VARCHAR(64),
        start_offset INTEGER,
        end_offset INTEGER,
        metadata JSONB,
        vector_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_knowledge_bindings_digital_human ON digital_human_knowledge_bindings(digital_human_id);
    CREATE INDEX IF NOT EXISTS idx_knowledge_bindings_knowledge_base ON digital_human_knowledge_bindings(knowledge_base_id);
    CREATE INDEX IF NOT EXISTS idx_documents_knowledge_base ON knowledge_documents(knowledge_base_id);
    CREATE INDEX IF NOT EXISTS idx_chunks_document ON document_chunks(document_id);
    CREATE INDEX IF NOT EXISTS idx_documents_status ON knowledge_documents(processing_status);
    CREATE INDEX IF NOT EXISTS idx_knowledge_bases_category ON knowledge_bases(category);
    CREATE INDEX IF NOT EXISTS idx_knowledge_bases_language ON knowledge_bases(language);
