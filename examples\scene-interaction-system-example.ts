/**
 * 场景交互系统使用示例
 * 展示如何使用数字人与场景的高级交互功能，包括拖拽控制、环境适应和智能行为
 */

import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/Transform';
import { DigitalHumanComponent } from '../engine/src/avatar/components/DigitalHumanComponent';
import { 
  SceneInteractionSystem,
  InteractionType,
  InteractionZone,
  InteractionResponse,
  BehaviorType,
  EnvironmentType
} from '../engine/src/avatar/interaction/SceneInteractionSystem';
import * as THREE from 'three';

/**
 * 场景交互系统示例类
 */
export class SceneInteractionSystemExample {
  private world: World;
  private interactionSystem: SceneInteractionSystem;
  private digitalHumanEntities: Entity[] = [];
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;

  constructor() {
    this.initializeExample();
  }

  /**
   * 初始化示例
   */
  private async initializeExample(): Promise<void> {
    console.log('🎮 初始化场景交互系统示例...');

    // 1. 创建Three.js场景
    this.setupThreeJSScene();

    // 2. 创建世界和交互系统
    this.world = new World();
    this.interactionSystem = new SceneInteractionSystem(this.world, {
      enableDragControl: true,
      enableEnvironmentAdaptation: true,
      enableIntelligentBehavior: true,
      enableCollisionDetection: true,
      enablePhysicsSimulation: false,
      interactionCheckFrequency: 60,
      maxInteractionDistance: 5.0,
      debug: true
    });

    // 3. 设置相机和场景引用
    this.interactionSystem.setCamera(this.camera);
    this.interactionSystem.setScene(this.scene);

    // 4. 添加系统到世界
    this.world.addSystem(this.interactionSystem);

    // 5. 创建数字人实体
    await this.createDigitalHumans();

    // 6. 创建交互区域
    this.createInteractionZones();

    // 7. 设置事件监听
    this.setupEventListeners();

    // 8. 演示交互功能
    await this.demonstrateInteractionFeatures();

    console.log('✅ 场景交互系统示例初始化完成');
  }

  /**
   * 设置Three.js场景
   */
  private setupThreeJSScene(): void {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 添加到DOM
    document.body.appendChild(this.renderer.domElement);

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);

    // 添加地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    console.log('✅ Three.js场景设置完成');
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHumans(): Promise<void> {
    console.log('👥 创建数字人实体...');

    const positions = [
      new THREE.Vector3(-3, 0, 0),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(3, 0, 0)
    ];

    for (let i = 0; i < 3; i++) {
      // 创建实体
      const entity = new Entity(this.world);
      entity.name = `DigitalHuman_${i + 1}`;

      // 添加Transform组件
      const transform = new Transform();
      transform.position.copy(positions[i]);
      entity.addComponent(transform);

      // 添加数字人组件
      const digitalHumanComponent = new DigitalHumanComponent(entity, {
        name: `数字人 ${i + 1}`,
        userId: `user_${i + 1}`,
        bodyMorphTargets: {
          height: (Math.random() - 0.5) * 0.2,
          weight: (Math.random() - 0.5) * 0.2,
          muscle: Math.random() * 0.3,
          chest: 0,
          waist: 0,
          hips: 0,
          shoulders: 0,
          custom: new Map()
        }
      });

      // 创建简单的几何体作为数字人表示
      const geometry = new THREE.CapsuleGeometry(0.3, 1.5, 4, 8);
      const material = new THREE.MeshLambertMaterial({ 
        color: new THREE.Color().setHSL(i * 0.3, 0.7, 0.6) 
      });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(transform.position);
      mesh.castShadow = true;
      this.scene.add(mesh);

      // 设置网格引用
      digitalHumanComponent.mesh = mesh;

      entity.addComponent(digitalHumanComponent);

      // 添加到世界
      this.world.addEntity(entity);
      this.digitalHumanEntities.push(entity);
    }

    console.log(`✅ 创建了 ${this.digitalHumanEntities.length} 个数字人实体`);
  }

  /**
   * 创建交互区域
   */
  private createInteractionZones(): void {
    console.log('🎯 创建交互区域...');

    // 创建点击交互区域
    const clickZone: InteractionZone = {
      id: 'click_zone_1',
      name: '点击区域',
      type: 'sphere',
      position: new THREE.Vector3(-5, 1, -3),
      size: new THREE.Vector3(1.5, 1.5, 1.5),
      rotation: new THREE.Euler(0, 0, 0),
      enabled: true,
      interactionTypes: [InteractionType.CLICK],
      triggerConditions: new Map(),
      responses: [
        {
          id: 'click_response_1',
          type: 'animation',
          data: 'wave',
          delay: 0,
          duration: 2000,
          priority: 1
        }
      ]
    };

    // 创建接近交互区域
    const proximityZone: InteractionZone = {
      id: 'proximity_zone_1',
      name: '接近区域',
      type: 'box',
      position: new THREE.Vector3(5, 0.5, -3),
      size: new THREE.Vector3(2, 1, 2),
      rotation: new THREE.Euler(0, 0, 0),
      enabled: true,
      interactionTypes: [InteractionType.PROXIMITY],
      triggerConditions: new Map(),
      responses: [
        {
          id: 'proximity_response_1',
          type: 'sound',
          data: 'greeting_sound',
          delay: 500,
          duration: 1000,
          priority: 2
        }
      ]
    };

    // 添加交互区域
    this.interactionSystem.addInteractionZone(clickZone);
    this.interactionSystem.addInteractionZone(proximityZone);

    // 可视化交互区域
    this.visualizeInteractionZones();

    console.log('✅ 交互区域创建完成');
  }

  /**
   * 可视化交互区域
   */
  private visualizeInteractionZones(): void {
    // 点击区域可视化
    const clickGeometry = new THREE.SphereGeometry(1.5, 16, 16);
    const clickMaterial = new THREE.MeshBasicMaterial({ 
      color: 0xff0000, 
      transparent: true, 
      opacity: 0.3,
      wireframe: true 
    });
    const clickMesh = new THREE.Mesh(clickGeometry, clickMaterial);
    clickMesh.position.set(-5, 1, -3);
    this.scene.add(clickMesh);

    // 接近区域可视化
    const proximityGeometry = new THREE.BoxGeometry(2, 1, 2);
    const proximityMaterial = new THREE.MeshBasicMaterial({ 
      color: 0x00ff00, 
      transparent: true, 
      opacity: 0.3,
      wireframe: true 
    });
    const proximityMesh = new THREE.Mesh(proximityGeometry, proximityMaterial);
    proximityMesh.position.set(5, 0.5, -3);
    this.scene.add(proximityMesh);
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    console.log('📡 设置事件监听...');

    // 拖拽事件
    this.interactionSystem.on('dragStarted', (entityId, position) => {
      console.log(`🖱️ 开始拖拽实体 ${entityId} at ${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}`);
    });

    this.interactionSystem.on('entityDragged', (entityId, position) => {
      // 更新Three.js网格位置
      const entity = this.world.getEntity(entityId);
      if (entity) {
        const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
        if (digitalHuman && digitalHuman.mesh) {
          digitalHuman.mesh.position.copy(position);
        }
      }
    });

    this.interactionSystem.on('dragEnded', (entityId, position) => {
      console.log(`🖱️ 结束拖拽实体 ${entityId} at ${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}`);
    });

    // 交互事件
    this.interactionSystem.on('interactionTriggered', (entityId, type, data) => {
      console.log(`🎯 触发交互: ${type} for 实体 ${entityId}`);
    });

    // 行为事件
    this.interactionSystem.on('behaviorExecuted', (entityId, behavior) => {
      console.log(`🤖 执行行为: ${behavior.type} for 实体 ${entityId}`);
    });

    // 动画请求事件
    this.interactionSystem.on('animationRequested', (entityId, animationName, duration) => {
      console.log(`🎬 动画请求: ${animationName} for 实体 ${entityId} (${duration}ms)`);
      this.playAnimation(entityId, animationName, duration);
    });

    // 移动请求事件
    this.interactionSystem.on('movementRequested', (entityId, movementData) => {
      console.log(`🚶 移动请求: ${movementData.type} for 实体 ${entityId}`);
      this.handleMovementRequest(entityId, movementData);
    });

    // 区域交互事件
    this.interactionSystem.on('zoneInteraction', (entityId, zone) => {
      console.log(`🎯 区域交互: ${zone.name} with 实体 ${entityId}`);
    });

    // 接近交互事件
    this.interactionSystem.on('proximityInteraction', (entityId1, entityId2, distance) => {
      console.log(`👥 接近交互: 实体 ${entityId1} 和 ${entityId2} 距离 ${distance.toFixed(2)}`);
    });

    console.log('✅ 事件监听设置完成');
  }

  /**
   * 演示交互功能
   */
  private async demonstrateInteractionFeatures(): Promise<void> {
    console.log('🎭 演示场景交互功能...');

    // 1. 演示拖拽控制
    await this.demonstrateDragControl();

    // 2. 演示智能行为
    await this.demonstrateIntelligentBehavior();

    // 3. 演示环境适应
    await this.demonstrateEnvironmentAdaptation();

    // 4. 演示交互区域
    await this.demonstrateInteractionZones();

    console.log('✅ 场景交互功能演示完成');
  }

  /**
   * 演示拖拽控制
   */
  private async demonstrateDragControl(): Promise<void> {
    console.log('1. 演示拖拽控制...');

    // 设置第一个数字人的拖拽约束
    const firstEntity = this.digitalHumanEntities[0];
    this.interactionSystem.setDragConstraints(firstEntity.id, { x: true, y: false, z: true });

    // 设置第二个数字人只能在Y轴移动
    const secondEntity = this.digitalHumanEntities[1];
    this.interactionSystem.setDragConstraints(secondEntity.id, { x: false, y: true, z: false });

    // 禁用第三个数字人的拖拽
    const thirdEntity = this.digitalHumanEntities[2];
    this.interactionSystem.setDragEnabled(thirdEntity.id, false);

    console.log('  ✅ 拖拽控制设置完成');
    console.log('  💡 提示: 尝试拖拽不同的数字人观察约束效果');
  }

  /**
   * 演示智能行为
   */
  private async demonstrateIntelligentBehavior(): Promise<void> {
    console.log('2. 演示智能行为...');

    // 触发一些交互来激活智能行为
    for (const entity of this.digitalHumanEntities) {
      this.interactionSystem.triggerInteraction(entity.id, InteractionType.CLICK);
      await this.wait(1000);
    }

    console.log('  ✅ 智能行为演示完成');
  }

  /**
   * 演示环境适应
   */
  private async demonstrateEnvironmentAdaptation(): Promise<void> {
    console.log('3. 演示环境适应...');

    // 模拟环境变化
    console.log('  模拟光照变化...');
    await this.wait(1000);

    console.log('  模拟物理环境变化...');
    await this.wait(1000);

    console.log('  ✅ 环境适应演示完成');
  }

  /**
   * 演示交互区域
   */
  private async demonstrateInteractionZones(): Promise<void> {
    console.log('4. 演示交互区域...');

    // 移动数字人到交互区域
    const entity = this.digitalHumanEntities[0];
    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (transform) {
      // 移动到点击区域
      transform.position.set(-5, 0, -3);
      this.updateMeshPosition(entity);
      
      console.log('  数字人进入点击区域');
      await this.wait(2000);

      // 移动到接近区域
      transform.position.set(5, 0, -3);
      this.updateMeshPosition(entity);
      
      console.log('  数字人进入接近区域');
      await this.wait(2000);
    }

    console.log('  ✅ 交互区域演示完成');
  }

  /**
   * 播放动画
   * @param entityId 实体ID
   * @param animationName 动画名称
   * @param duration 持续时间
   */
  private playAnimation(entityId: string, animationName: string, duration?: number): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHuman || !digitalHuman.mesh) return;

    // 简单的动画效果：改变颜色
    const originalColor = (digitalHuman.mesh.material as THREE.MeshLambertMaterial).color.clone();
    (digitalHuman.mesh.material as THREE.MeshLambertMaterial).color.setHex(0xffff00);

    setTimeout(() => {
      (digitalHuman.mesh!.material as THREE.MeshLambertMaterial).color.copy(originalColor);
    }, duration || 1000);
  }

  /**
   * 处理移动请求
   * @param entityId 实体ID
   * @param movementData 移动数据
   */
  private handleMovementRequest(entityId: string, movementData: any): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return;

    switch (movementData.type) {
      case 'moveTo':
        // 简单的移动到目标位置
        transform.position.copy(movementData.target);
        this.updateMeshPosition(entity);
        break;
      case 'follow':
        // 简单的跟随逻辑
        console.log(`实体 ${entityId} 开始跟随`);
        break;
    }
  }

  /**
   * 更新网格位置
   * @param entity 实体
   */
  private updateMeshPosition(entity: Entity): void {
    const transform = entity.getComponent<Transform>(Transform.TYPE);
    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    
    if (transform && digitalHuman && digitalHuman.mesh) {
      digitalHuman.mesh.position.copy(transform.position);
    }
  }

  /**
   * 等待指定时间
   * @param ms 毫秒
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    console.log('🚀 场景交互系统示例正在运行...');

    // 启动渲染循环
    this.startRenderLoop();

    // 添加窗口调整事件
    window.addEventListener('resize', this.onWindowResize.bind(this));

    console.log('💡 提示: 尝试点击和拖拽数字人，观察交互效果');
  }

  /**
   * 启动渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      requestAnimationFrame(animate);

      // 更新世界
      this.world.update(1/60); // 假设60FPS

      // 渲染场景
      this.renderer.render(this.scene, this.camera);
    };

    animate();
  }

  /**
   * 窗口调整事件
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理场景交互系统示例...');

    // 移除渲染器
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }

    // 清理Three.js资源
    this.renderer.dispose();

    // 清理交互系统
    if (this.interactionSystem) {
      this.interactionSystem.dispose();
    }

    console.log('✅ 场景交互系统示例已清理');
  }
}

// 导出示例类
export default SceneInteractionSystemExample;

// 如果直接运行此文件，则启动示例
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const example = new SceneInteractionSystemExample();
    await example.run();
  });
}
