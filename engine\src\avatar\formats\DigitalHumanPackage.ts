/**
 * 数字人包格式定义
 * 定义数字人文件的标准格式和结构
 */
import * as THREE from 'three';
import { DigitalHumanSource, ClothingSlotType, PersonalityTraits } from '../components/DigitalHumanComponent';
import { StandardBoneType, StandardSkeletonData } from '../bip/BIPToStandardMapping';

/**
 * 数字人包版本
 */
export const DIGITAL_HUMAN_PACKAGE_VERSION = '1.0.0';

/**
 * 数字人包文件扩展名
 */
export const DIGITAL_HUMAN_PACKAGE_EXTENSION = '.dhp';

/**
 * 数字人包MIME类型
 */
export const DIGITAL_HUMAN_PACKAGE_MIME_TYPE = 'application/x-digital-human-package';

/**
 * 数字人包头信息
 */
export interface DigitalHumanPackageHeader {
  /** 包格式版本 */
  version: string;
  /** 包类型 */
  type: 'digital-human-package';
  /** 创建时间 */
  created: string;
  /** 修改时间 */
  modified: string;
  /** 创建者信息 */
  creator: {
    name?: string;
    id?: string;
    version?: string;
  };
  /** 包大小（字节） */
  size: number;
  /** 文件数量 */
  fileCount: number;
  /** 校验和 */
  checksum: string;
}

/**
 * 数字人元数据
 */
export interface DigitalHumanMetadata {
  /** 数字人ID */
  id: string;
  /** 数字人名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 创建来源 */
  source: DigitalHumanSource;
  /** 源文件信息 */
  sourceInfo?: {
    photoUrl?: string;
    fileUrl?: string;
    marketplaceId?: string;
  };
  /** 版本信息 */
  version: string;
  /** 创建者ID */
  creatorId?: string;
  /** 许可类型 */
  licenseType: string;
  /** 标签 */
  tags: string[];
  /** 个性特征 */
  personalityTraits: PersonalityTraits;
  /** 预览图URL */
  thumbnailUrl?: string;
  /** 质量评分 */
  qualityScore?: number;
  /** 自定义属性 */
  customProperties: Record<string, any>;
}

/**
 * 几何数据信息
 */
export interface GeometryInfo {
  /** 文件路径 */
  filePath: string;
  /** 文件格式 */
  format: 'gltf' | 'fbx' | 'obj' | 'ply' | 'custom';
  /** 顶点数量 */
  vertexCount: number;
  /** 面数量 */
  faceCount: number;
  /** 边界框 */
  boundingBox: {
    min: [number, number, number];
    max: [number, number, number];
  };
  /** 是否有UV坐标 */
  hasUVs: boolean;
  /** 是否有法线 */
  hasNormals: boolean;
  /** 是否有顶点颜色 */
  hasVertexColors: boolean;
  /** 混合形状数量 */
  blendShapeCount?: number;
  /** 压缩信息 */
  compression?: {
    algorithm: string;
    ratio: number;
  };
}

/**
 * 纹理信息
 */
export interface TextureInfo {
  /** 纹理类型 */
  type: 'diffuse' | 'normal' | 'roughness' | 'metallic' | 'ao' | 'height' | 'emission';
  /** 文件路径 */
  filePath: string;
  /** 文件格式 */
  format: 'jpg' | 'png' | 'webp' | 'ktx2' | 'dds';
  /** 分辨率 */
  resolution: [number, number];
  /** 文件大小（字节） */
  fileSize: number;
  /** 是否压缩 */
  compressed: boolean;
  /** 压缩格式 */
  compressionFormat?: string;
  /** UV通道 */
  uvChannel: number;
  /** 包装模式 */
  wrapMode: {
    s: 'repeat' | 'clamp' | 'mirror';
    t: 'repeat' | 'clamp' | 'mirror';
  };
  /** 过滤模式 */
  filterMode: {
    min: 'nearest' | 'linear' | 'mipmap';
    mag: 'nearest' | 'linear';
  };
}

/**
 * 骨骼信息
 */
export interface SkeletonInfo {
  /** 骨骼数据文件路径 */
  filePath: string;
  /** 骨骼格式 */
  format: 'standard' | 'bip' | 'mixamo' | 'custom';
  /** 骨骼数量 */
  boneCount: number;
  /** 根骨骼类型 */
  rootBoneType: StandardBoneType;
  /** 支持的骨骼类型列表 */
  supportedBones: StandardBoneType[];
  /** 是否有IK约束 */
  hasIKConstraints: boolean;
  /** 是否有自由度限制 */
  hasDOFLimits: boolean;
  /** 绑定姿势信息 */
  bindPose: {
    filePath: string;
    format: 'json' | 'binary';
  };
}

/**
 * 动画信息
 */
export interface AnimationInfo {
  /** 动画ID */
  id: string;
  /** 动画名称 */
  name: string;
  /** 文件路径 */
  filePath: string;
  /** 动画格式 */
  format: 'gltf' | 'fbx' | 'bvh' | 'bip' | 'custom';
  /** 持续时间（秒） */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 帧数 */
  frameCount: number;
  /** 动画类型 */
  type: 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom';
  /** 是否循环 */
  loop: boolean;
  /** 影响的骨骼 */
  affectedBones: StandardBoneType[];
  /** 动画标签 */
  tags: string[];
  /** 预览信息 */
  preview?: {
    thumbnailPath: string;
    videoPath?: string;
  };
}

/**
 * 服装信息
 */
export interface ClothingInfo {
  /** 服装ID */
  id: string;
  /** 服装名称 */
  name: string;
  /** 插槽类型 */
  slotType: ClothingSlotType;
  /** 几何文件路径 */
  geometryPath: string;
  /** 纹理信息 */
  textures: TextureInfo[];
  /** 适配参数 */
  fittingParams: {
    scale: [number, number, number];
    offset: [number, number, number];
    rotation: [number, number, number];
  };
  /** 物理属性 */
  physics?: {
    enabled: boolean;
    mass: number;
    stiffness: number;
    damping: number;
  };
  /** 碰撞信息 */
  collision?: {
    enabled: boolean;
    meshPath: string;
  };
  /** 材质属性 */
  material: {
    type: 'standard' | 'pbr' | 'custom';
    properties: Record<string, any>;
  };
}

/**
 * 表情信息
 */
export interface ExpressionInfo {
  /** 表情ID */
  id: string;
  /** 表情名称 */
  name: string;
  /** 表情类型 */
  type: 'basic' | 'emotion' | 'viseme' | 'custom';
  /** 混合形状权重 */
  blendShapeWeights: Record<string, number>;
  /** 骨骼变换 */
  boneTransforms?: Record<StandardBoneType, {
    position?: [number, number, number];
    rotation?: [number, number, number, number];
    scale?: [number, number, number];
  }>;
  /** 强度范围 */
  intensityRange: [number, number];
  /** 预览图路径 */
  previewPath?: string;
}

/**
 * 数字人包内容
 */
export interface DigitalHumanPackageContent {
  /** 包头信息 */
  header: DigitalHumanPackageHeader;
  /** 数字人元数据 */
  metadata: DigitalHumanMetadata;
  /** 几何数据 */
  geometry: GeometryInfo;
  /** 纹理列表 */
  textures: TextureInfo[];
  /** 骨骼信息 */
  skeleton?: SkeletonInfo;
  /** 动画列表 */
  animations: AnimationInfo[];
  /** 服装列表 */
  clothing: ClothingInfo[];
  /** 表情列表 */
  expressions: ExpressionInfo[];
  /** 文件清单 */
  manifest: PackageManifest;
}

/**
 * 包文件清单
 */
export interface PackageManifest {
  /** 文件列表 */
  files: PackageFile[];
  /** 目录结构 */
  directories: string[];
  /** 总大小 */
  totalSize: number;
  /** 压缩比 */
  compressionRatio?: number;
}

/**
 * 包文件信息
 */
export interface PackageFile {
  /** 文件路径 */
  path: string;
  /** 文件大小 */
  size: number;
  /** 文件类型 */
  type: 'geometry' | 'texture' | 'animation' | 'skeleton' | 'config' | 'other';
  /** MIME类型 */
  mimeType: string;
  /** 校验和 */
  checksum: string;
  /** 是否压缩 */
  compressed: boolean;
  /** 压缩后大小 */
  compressedSize?: number;
  /** 最后修改时间 */
  lastModified: string;
}

/**
 * 数字人包验证结果
 */
export interface PackageValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误列表 */
  errors: string[];
  /** 警告列表 */
  warnings: string[];
  /** 验证详情 */
  details: {
    headerValid: boolean;
    metadataValid: boolean;
    filesValid: boolean;
    checksumValid: boolean;
    structureValid: boolean;
  };
}

/**
 * 数字人包构建选项
 */
export interface PackageBuildOptions {
  /** 压缩级别 */
  compressionLevel: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  /** 是否包含源文件 */
  includeSource: boolean;
  /** 是否优化纹理 */
  optimizeTextures: boolean;
  /** 纹理质量 */
  textureQuality: 'low' | 'medium' | 'high' | 'lossless';
  /** 是否包含动画 */
  includeAnimations: boolean;
  /** 是否包含表情 */
  includeExpressions: boolean;
  /** 是否包含服装 */
  includeClothing: boolean;
  /** 目标平台 */
  targetPlatform: 'web' | 'mobile' | 'desktop' | 'all';
  /** 自定义元数据 */
  customMetadata?: Record<string, any>;
}

/**
 * 数字人包解析选项
 */
export interface PackageParseOptions {
  /** 是否验证包完整性 */
  validateIntegrity: boolean;
  /** 是否加载所有资源 */
  loadAllResources: boolean;
  /** 资源加载过滤器 */
  resourceFilter?: {
    geometry: boolean;
    textures: boolean;
    animations: boolean;
    clothing: boolean;
    expressions: boolean;
  };
  /** 是否使用缓存 */
  useCache: boolean;
  /** 进度回调 */
  onProgress?: (progress: number, stage: string) => void;
}

/**
 * 数字人包常量
 */
export const PACKAGE_CONSTANTS = {
  /** 最大包大小（字节） */
  MAX_PACKAGE_SIZE: 500 * 1024 * 1024, // 500MB
  /** 最大文件数量 */
  MAX_FILE_COUNT: 1000,
  /** 支持的几何格式 */
  SUPPORTED_GEOMETRY_FORMATS: ['gltf', 'fbx', 'obj', 'ply'],
  /** 支持的纹理格式 */
  SUPPORTED_TEXTURE_FORMATS: ['jpg', 'png', 'webp', 'ktx2', 'dds'],
  /** 支持的动画格式 */
  SUPPORTED_ANIMATION_FORMATS: ['gltf', 'fbx', 'bvh', 'bip'],
  /** 默认目录结构 */
  DEFAULT_DIRECTORIES: [
    'geometry',
    'textures',
    'animations',
    'skeleton',
    'clothing',
    'expressions',
    'thumbnails',
    'config'
  ]
} as const;
