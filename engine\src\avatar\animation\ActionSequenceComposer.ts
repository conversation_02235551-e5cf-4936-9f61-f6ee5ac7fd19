import { EventEmitter } from 'events';
import * as THREE from 'three';

/**
 * 动作类型
 */
export enum ActionType {
  IDLE = 'idle',
  WALK = 'walk',
  RUN = 'run',
  JUMP = 'jump',
  DANCE = 'dance',
  GESTURE = 'gesture',
  EXPRESSION = 'expression',
  CUSTOM = 'custom'
}

/**
 * 动作优先级
 */
export enum ActionPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 混合模式
 */
export enum BlendMode {
  REPLACE = 'replace',
  ADD = 'add',
  MULTIPLY = 'multiply',
  OVERLAY = 'overlay',
  SMOOTH = 'smooth'
}

/**
 * 缓动类型
 */
export enum EasingType {
  LINEAR = 'linear',
  EASE_IN = 'easeIn',
  EASE_OUT = 'easeOut',
  EASE_IN_OUT = 'easeInOut',
  BOUNCE = 'bounce',
  ELASTIC = 'elastic'
}

/**
 * 动作片段
 */
export interface ActionClip {
  /** 片段ID */
  id: string;
  /** 片段名称 */
  name: string;
  /** 动作类型 */
  type: ActionType;
  /** 动画数据 */
  animationData: any;
  /** 持续时间 */
  duration: number;
  /** 是否循环 */
  loop: boolean;
  /** 优先级 */
  priority: ActionPriority;
  /** 标签 */
  tags: string[];
  /** 预览图 */
  thumbnail?: string;
}

/**
 * 序列节点
 */
export interface SequenceNode {
  /** 节点ID */
  id: string;
  /** 节点名称 */
  name: string;
  /** 动作片段ID */
  clipId: string;
  /** 开始时间 */
  startTime: number;
  /** 持续时间 */
  duration: number;
  /** 权重 */
  weight: number;
  /** 混合模式 */
  blendMode: BlendMode;
  /** 缓动类型 */
  easing: EasingType;
  /** 淡入时间 */
  fadeInTime: number;
  /** 淡出时间 */
  fadeOutTime: number;
  /** 是否静音 */
  muted: boolean;
  /** 自定义参数 */
  parameters: Map<string, any>;
}

/**
 * 动作轨道
 */
export interface ActionTrack {
  /** 轨道ID */
  id: string;
  /** 轨道名称 */
  name: string;
  /** 轨道类型 */
  type: 'body' | 'face' | 'hand' | 'custom';
  /** 序列节点 */
  nodes: SequenceNode[];
  /** 是否静音 */
  muted: boolean;
  /** 是否锁定 */
  locked: boolean;
  /** 轨道颜色 */
  color: string;
}

/**
 * 动作序列
 */
export interface ActionSequence {
  /** 序列ID */
  id: string;
  /** 序列名称 */
  name: string;
  /** 描述 */
  description: string;
  /** 总时长 */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 轨道列表 */
  tracks: ActionTrack[];
  /** 是否循环 */
  loop: boolean;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 标签 */
  tags: string[];
  /** 元数据 */
  metadata: Map<string, any>;
}

/**
 * 播放状态
 */
export interface PlaybackState {
  /** 是否播放中 */
  isPlaying: boolean;
  /** 是否暂停 */
  isPaused: boolean;
  /** 当前时间 */
  currentTime: number;
  /** 播放速度 */
  playbackSpeed: number;
  /** 循环次数 */
  loopCount: number;
}

/**
 * 编排器配置
 */
export interface ComposerConfig {
  /** 默认帧率 */
  defaultFrameRate: number;
  /** 最大轨道数 */
  maxTracks: number;
  /** 最大序列时长 */
  maxDuration: number;
  /** 是否启用自动保存 */
  autoSave: boolean;
  /** 自动保存间隔（秒） */
  autoSaveInterval: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 动作序列编排器
 * 实现可视化的动作序列编排器，支持复杂动作序列创建、编辑和播放
 */
export class ActionSequenceComposer extends EventEmitter {
  /** 配置 */
  private config: ComposerConfig;

  /** 动作片段库 */
  private actionClips: Map<string, ActionClip>;

  /** 动作序列库 */
  private sequences: Map<string, ActionSequence>;

  /** 当前编辑的序列 */
  private currentSequence?: ActionSequence;

  /** 播放状态 */
  private playbackState: PlaybackState;

  /** 播放定时器 */
  private playbackTimer?: NodeJS.Timeout;

  /** 自动保存定时器 */
  private autoSaveTimer?: NodeJS.Timeout;

  /** 撤销栈 */
  private undoStack: ActionSequence[];

  /** 重做栈 */
  private redoStack: ActionSequence[];

  /** 最大撤销步数 */
  private maxUndoSteps: number = 50;

  /**
   * 构造函数
   * @param config 编排器配置
   */
  constructor(config: Partial<ComposerConfig> = {}) {
    super();

    this.config = {
      defaultFrameRate: 30,
      maxTracks: 20,
      maxDuration: 600, // 10分钟
      autoSave: true,
      autoSaveInterval: 30, // 30秒
      debug: false,
      ...config
    };

    this.actionClips = new Map();
    this.sequences = new Map();
    this.undoStack = [];
    this.redoStack = [];

    this.playbackState = {
      isPlaying: false,
      isPaused: false,
      currentTime: 0,
      playbackSpeed: 1.0,
      loopCount: 0
    };

    this.initializeDefaultClips();
    this.setupAutoSave();
  }

  /**
   * 初始化默认动作片段
   */
  private initializeDefaultClips(): void {
    const defaultClips: ActionClip[] = [
      {
        id: 'idle_basic',
        name: '基础待机',
        type: ActionType.IDLE,
        animationData: null, // 实际实现需要加载动画数据
        duration: 5.0,
        loop: true,
        priority: ActionPriority.LOW,
        tags: ['idle', 'basic']
      },
      {
        id: 'walk_normal',
        name: '正常行走',
        type: ActionType.WALK,
        animationData: null,
        duration: 2.0,
        loop: true,
        priority: ActionPriority.NORMAL,
        tags: ['walk', 'locomotion']
      },
      {
        id: 'wave_hello',
        name: '挥手问候',
        type: ActionType.GESTURE,
        animationData: null,
        duration: 3.0,
        loop: false,
        priority: ActionPriority.HIGH,
        tags: ['gesture', 'greeting']
      },
      {
        id: 'jump_basic',
        name: '基础跳跃',
        type: ActionType.JUMP,
        animationData: null,
        duration: 1.5,
        loop: false,
        priority: ActionPriority.HIGH,
        tags: ['jump', 'athletic']
      },
      {
        id: 'dance_simple',
        name: '简单舞蹈',
        type: ActionType.DANCE,
        animationData: null,
        duration: 8.0,
        loop: true,
        priority: ActionPriority.NORMAL,
        tags: ['dance', 'entertainment']
      }
    ];

    for (const clip of defaultClips) {
      this.actionClips.set(clip.id, clip);
    }

    if (this.config.debug) {
      console.log(`ActionSequenceComposer: 初始化了 ${defaultClips.length} 个默认动作片段`);
    }
  }

  /**
   * 设置自动保存
   */
  private setupAutoSave(): void {
    if (this.config.autoSave) {
      this.autoSaveTimer = setInterval(() => {
        if (this.currentSequence) {
          this.saveSequence(this.currentSequence.id);
        }
      }, this.config.autoSaveInterval * 1000);
    }
  }

  // ==================== 动作片段管理 ====================

  /**
   * 添加动作片段
   * @param clip 动作片段
   */
  public addActionClip(clip: ActionClip): void {
    this.actionClips.set(clip.id, clip);
    this.emit('clipAdded', clip);

    if (this.config.debug) {
      console.log(`添加动作片段: ${clip.name}`);
    }
  }

  /**
   * 移除动作片段
   * @param clipId 片段ID
   */
  public removeActionClip(clipId: string): boolean {
    const clip = this.actionClips.get(clipId);
    if (!clip) return false;

    this.actionClips.delete(clipId);
    this.emit('clipRemoved', clip);

    if (this.config.debug) {
      console.log(`移除动作片段: ${clip.name}`);
    }

    return true;
  }

  /**
   * 获取动作片段
   * @param clipId 片段ID
   * @returns 动作片段
   */
  public getActionClip(clipId: string): ActionClip | undefined {
    return this.actionClips.get(clipId);
  }

  /**
   * 获取所有动作片段
   * @param type 类型过滤
   * @returns 动作片段列表
   */
  public getActionClips(type?: ActionType): ActionClip[] {
    const clips = Array.from(this.actionClips.values());
    return type ? clips.filter(clip => clip.type === type) : clips;
  }

  /**
   * 搜索动作片段
   * @param query 搜索条件
   * @returns 匹配的片段
   */
  public searchActionClips(query: {
    name?: string;
    type?: ActionType;
    tags?: string[];
    duration?: [number, number];
  }): ActionClip[] {
    const results: ActionClip[] = [];

    for (const clip of this.actionClips.values()) {
      let matches = true;

      if (query.name && !clip.name.toLowerCase().includes(query.name.toLowerCase())) {
        matches = false;
      }

      if (query.type && clip.type !== query.type) {
        matches = false;
      }

      if (query.tags && query.tags.length > 0) {
        const hasAllTags = query.tags.every(tag => clip.tags.includes(tag));
        if (!hasAllTags) {
          matches = false;
        }
      }

      if (query.duration) {
        const [minDuration, maxDuration] = query.duration;
        if (clip.duration < minDuration || clip.duration > maxDuration) {
          matches = false;
        }
      }

      if (matches) {
        results.push(clip);
      }
    }

    return results;
  }

  // ==================== 序列管理 ====================

  /**
   * 创建新序列
   * @param name 序列名称
   * @param description 描述
   * @returns 序列ID
   */
  public createSequence(name: string, description: string = ''): string {
    const sequence: ActionSequence = {
      id: this.generateSequenceId(),
      name,
      description,
      duration: 10.0, // 默认10秒
      frameRate: this.config.defaultFrameRate,
      tracks: [],
      loop: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      tags: [],
      metadata: new Map()
    };

    // 创建默认轨道
    sequence.tracks.push(this.createDefaultTrack('body', '身体动作'));
    sequence.tracks.push(this.createDefaultTrack('face', '面部表情'));

    this.sequences.set(sequence.id, sequence);
    this.currentSequence = sequence;

    this.emit('sequenceCreated', sequence);

    if (this.config.debug) {
      console.log(`创建新序列: ${name} (${sequence.id})`);
    }

    return sequence.id;
  }

  /**
   * 创建默认轨道
   * @param type 轨道类型
   * @param name 轨道名称
   * @returns 轨道
   */
  private createDefaultTrack(type: 'body' | 'face' | 'hand' | 'custom', name: string): ActionTrack {
    return {
      id: this.generateTrackId(),
      name,
      type,
      nodes: [],
      muted: false,
      locked: false,
      color: this.getDefaultTrackColor(type)
    };
  }

  /**
   * 获取默认轨道颜色
   * @param type 轨道类型
   * @returns 颜色
   */
  private getDefaultTrackColor(type: string): string {
    const colors = {
      body: '#4CAF50',
      face: '#2196F3',
      hand: '#FF9800',
      custom: '#9C27B0'
    };
    return colors[type] || '#757575';
  }

  /**
   * 加载序列
   * @param sequenceId 序列ID
   * @returns 是否成功加载
   */
  public loadSequence(sequenceId: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) {
      console.warn(`序列不存在: ${sequenceId}`);
      return false;
    }

    this.currentSequence = sequence;
    this.emit('sequenceLoaded', sequence);

    if (this.config.debug) {
      console.log(`加载序列: ${sequence.name}`);
    }

    return true;
  }

  /**
   * 保存序列
   * @param sequenceId 序列ID
   * @returns 是否成功保存
   */
  public saveSequence(sequenceId: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    sequence.updatedAt = new Date();
    this.emit('sequenceSaved', sequence);

    if (this.config.debug) {
      console.log(`保存序列: ${sequence.name}`);
    }

    return true;
  }

  /**
   * 删除序列
   * @param sequenceId 序列ID
   * @returns 是否成功删除
   */
  public deleteSequence(sequenceId: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    this.sequences.delete(sequenceId);

    if (this.currentSequence?.id === sequenceId) {
      this.currentSequence = undefined;
    }

    this.emit('sequenceDeleted', sequence);

    if (this.config.debug) {
      console.log(`删除序列: ${sequence.name}`);
    }

    return true;
  }

  /**
   * 获取当前序列
   * @returns 当前序列
   */
  public getCurrentSequence(): ActionSequence | undefined {
    return this.currentSequence;
  }

  /**
   * 获取所有序列
   * @returns 序列列表
   */
  public getAllSequences(): ActionSequence[] {
    return Array.from(this.sequences.values());
  }

  // ==================== 轨道管理 ====================

  /**
   * 添加轨道
   * @param sequenceId 序列ID
   * @param name 轨道名称
   * @param type 轨道类型
   * @returns 轨道ID
   */
  public addTrack(sequenceId: string, name: string, type: 'body' | 'face' | 'hand' | 'custom'): string | null {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return null;

    if (sequence.tracks.length >= this.config.maxTracks) {
      console.warn(`轨道数量已达到最大限制: ${this.config.maxTracks}`);
      return null;
    }

    const track = this.createDefaultTrack(type, name);
    sequence.tracks.push(track);
    sequence.updatedAt = new Date();

    this.pushToUndoStack(sequence);
    this.emit('trackAdded', sequence, track);

    if (this.config.debug) {
      console.log(`添加轨道: ${name} 到序列 ${sequence.name}`);
    }

    return track.id;
  }

  /**
   * 移除轨道
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @returns 是否成功移除
   */
  public removeTrack(sequenceId: string, trackId: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const trackIndex = sequence.tracks.findIndex(track => track.id === trackId);
    if (trackIndex === -1) return false;

    const track = sequence.tracks[trackIndex];
    sequence.tracks.splice(trackIndex, 1);
    sequence.updatedAt = new Date();

    this.pushToUndoStack(sequence);
    this.emit('trackRemoved', sequence, track);

    if (this.config.debug) {
      console.log(`移除轨道: ${track.name} 从序列 ${sequence.name}`);
    }

    return true;
  }

  /**
   * 重命名轨道
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param newName 新名称
   * @returns 是否成功重命名
   */
  public renameTrack(sequenceId: string, trackId: string, newName: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track) return false;

    const oldName = track.name;
    track.name = newName;
    sequence.updatedAt = new Date();

    this.pushToUndoStack(sequence);
    this.emit('trackRenamed', sequence, track, oldName);

    if (this.config.debug) {
      console.log(`重命名轨道: ${oldName} -> ${newName}`);
    }

    return true;
  }

  /**
   * 设置轨道静音状态
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param muted 是否静音
   * @returns 是否成功设置
   */
  public setTrackMuted(sequenceId: string, trackId: string, muted: boolean): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track) return false;

    track.muted = muted;
    sequence.updatedAt = new Date();

    this.emit('trackMutedChanged', sequence, track);

    if (this.config.debug) {
      console.log(`设置轨道 ${track.name} 静音状态: ${muted}`);
    }

    return true;
  }

  /**
   * 设置轨道锁定状态
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param locked 是否锁定
   * @returns 是否成功设置
   */
  public setTrackLocked(sequenceId: string, trackId: string, locked: boolean): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track) return false;

    track.locked = locked;
    sequence.updatedAt = new Date();

    this.emit('trackLockedChanged', sequence, track);

    if (this.config.debug) {
      console.log(`设置轨道 ${track.name} 锁定状态: ${locked}`);
    }

    return true;
  }

  // ==================== 节点管理 ====================

  /**
   * 添加序列节点
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param clipId 动作片段ID
   * @param startTime 开始时间
   * @param duration 持续时间
   * @returns 节点ID
   */
  public addSequenceNode(
    sequenceId: string,
    trackId: string,
    clipId: string,
    startTime: number,
    duration?: number
  ): string | null {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return null;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track || track.locked) return null;

    const clip = this.actionClips.get(clipId);
    if (!clip) return null;

    const nodeDuration = duration || clip.duration;

    // 检查时间冲突
    if (this.hasTimeConflict(track, startTime, nodeDuration)) {
      console.warn('时间段冲突，无法添加节点');
      return null;
    }

    const node: SequenceNode = {
      id: this.generateNodeId(),
      name: clip.name,
      clipId,
      startTime,
      duration: nodeDuration,
      weight: 1.0,
      blendMode: BlendMode.REPLACE,
      easing: EasingType.LINEAR,
      fadeInTime: 0.2,
      fadeOutTime: 0.2,
      muted: false,
      parameters: new Map()
    };

    track.nodes.push(node);
    track.nodes.sort((a, b) => a.startTime - b.startTime);
    sequence.updatedAt = new Date();

    // 更新序列总时长
    this.updateSequenceDuration(sequence);

    this.pushToUndoStack(sequence);
    this.emit('nodeAdded', sequence, track, node);

    if (this.config.debug) {
      console.log(`添加节点: ${clip.name} 到轨道 ${track.name}`);
    }

    return node.id;
  }

  /**
   * 移除序列节点
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param nodeId 节点ID
   * @returns 是否成功移除
   */
  public removeSequenceNode(sequenceId: string, trackId: string, nodeId: string): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track || track.locked) return false;

    const nodeIndex = track.nodes.findIndex(node => node.id === nodeId);
    if (nodeIndex === -1) return false;

    const node = track.nodes[nodeIndex];
    track.nodes.splice(nodeIndex, 1);
    sequence.updatedAt = new Date();

    this.updateSequenceDuration(sequence);
    this.pushToUndoStack(sequence);
    this.emit('nodeRemoved', sequence, track, node);

    if (this.config.debug) {
      console.log(`移除节点: ${node.name} 从轨道 ${track.name}`);
    }

    return true;
  }

  /**
   * 移动序列节点
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param nodeId 节点ID
   * @param newStartTime 新开始时间
   * @returns 是否成功移动
   */
  public moveSequenceNode(
    sequenceId: string,
    trackId: string,
    nodeId: string,
    newStartTime: number
  ): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track || track.locked) return false;

    const node = track.nodes.find(n => n.id === nodeId);
    if (!node) return false;

    // 检查新位置是否有冲突
    const otherNodes = track.nodes.filter(n => n.id !== nodeId);
    if (this.hasTimeConflictWithNodes(otherNodes, newStartTime, node.duration)) {
      console.warn('新位置有时间冲突，无法移动节点');
      return false;
    }

    node.startTime = newStartTime;
    track.nodes.sort((a, b) => a.startTime - b.startTime);
    sequence.updatedAt = new Date();

    this.updateSequenceDuration(sequence);
    this.pushToUndoStack(sequence);
    this.emit('nodeMoved', sequence, track, node);

    if (this.config.debug) {
      console.log(`移动节点: ${node.name} 到时间 ${newStartTime}`);
    }

    return true;
  }

  /**
   * 调整节点持续时间
   * @param sequenceId 序列ID
   * @param trackId 轨道ID
   * @param nodeId 节点ID
   * @param newDuration 新持续时间
   * @returns 是否成功调整
   */
  public resizeSequenceNode(
    sequenceId: string,
    trackId: string,
    nodeId: string,
    newDuration: number
  ): boolean {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return false;

    const track = sequence.tracks.find(t => t.id === trackId);
    if (!track || track.locked) return false;

    const node = track.nodes.find(n => n.id === nodeId);
    if (!node) return false;

    // 检查新持续时间是否会造成冲突
    const otherNodes = track.nodes.filter(n => n.id !== nodeId);
    if (this.hasTimeConflictWithNodes(otherNodes, node.startTime, newDuration)) {
      console.warn('新持续时间会造成冲突，无法调整');
      return false;
    }

    node.duration = newDuration;
    sequence.updatedAt = new Date();

    this.updateSequenceDuration(sequence);
    this.pushToUndoStack(sequence);
    this.emit('nodeResized', sequence, track, node);

    if (this.config.debug) {
      console.log(`调整节点 ${node.name} 持续时间为 ${newDuration}`);
    }

    return true;
  }

  /**
   * 检查时间冲突
   * @param track 轨道
   * @param startTime 开始时间
   * @param duration 持续时间
   * @returns 是否有冲突
   */
  private hasTimeConflict(track: ActionTrack, startTime: number, duration: number): boolean {
    return this.hasTimeConflictWithNodes(track.nodes, startTime, duration);
  }

  /**
   * 检查与节点列表的时间冲突
   * @param nodes 节点列表
   * @param startTime 开始时间
   * @param duration 持续时间
   * @returns 是否有冲突
   */
  private hasTimeConflictWithNodes(nodes: SequenceNode[], startTime: number, duration: number): boolean {
    const endTime = startTime + duration;

    for (const node of nodes) {
      const nodeEndTime = node.startTime + node.duration;

      // 检查重叠
      if (startTime < nodeEndTime && endTime > node.startTime) {
        return true;
      }
    }

    return false;
  }

  /**
   * 更新序列总时长
   * @param sequence 序列
   */
  private updateSequenceDuration(sequence: ActionSequence): void {
    let maxEndTime = 0;

    for (const track of sequence.tracks) {
      for (const node of track.nodes) {
        const endTime = node.startTime + node.duration;
        if (endTime > maxEndTime) {
          maxEndTime = endTime;
        }
      }
    }

    sequence.duration = Math.max(maxEndTime, 1.0); // 最小1秒
  }

  // ==================== 撤销/重做系统 ====================

  /**
   * 推入撤销栈
   * @param sequence 序列
   */
  private pushToUndoStack(sequence: ActionSequence): void {
    // 深拷贝序列
    const sequenceCopy = JSON.parse(JSON.stringify(sequence));

    // 恢复Map对象
    sequenceCopy.metadata = new Map(Object.entries(sequenceCopy.metadata || {}));
    for (const track of sequenceCopy.tracks) {
      for (const node of track.nodes) {
        node.parameters = new Map(Object.entries(node.parameters || {}));
      }
    }

    this.undoStack.push(sequenceCopy);

    // 限制撤销栈大小
    if (this.undoStack.length > this.maxUndoSteps) {
      this.undoStack.shift();
    }

    // 清空重做栈
    this.redoStack = [];
  }

  /**
   * 撤销操作
   * @returns 是否成功撤销
   */
  public undo(): boolean {
    if (this.undoStack.length === 0 || !this.currentSequence) return false;

    // 保存当前状态到重做栈
    this.redoStack.push(JSON.parse(JSON.stringify(this.currentSequence)));

    // 恢复上一个状态
    const previousState = this.undoStack.pop()!;
    this.currentSequence = previousState;
    this.sequences.set(this.currentSequence.id, this.currentSequence);

    this.emit('undoPerformed', this.currentSequence);

    if (this.config.debug) {
      console.log(`撤销操作，恢复到序列状态`);
    }

    return true;
  }

  /**
   * 重做操作
   * @returns 是否成功重做
   */
  public redo(): boolean {
    if (this.redoStack.length === 0 || !this.currentSequence) return false;

    // 保存当前状态到撤销栈
    this.undoStack.push(JSON.parse(JSON.stringify(this.currentSequence)));

    // 恢复重做状态
    const redoState = this.redoStack.pop()!;
    this.currentSequence = redoState;
    this.sequences.set(this.currentSequence.id, this.currentSequence);

    this.emit('redoPerformed', this.currentSequence);

    if (this.config.debug) {
      console.log(`重做操作，恢复到序列状态`);
    }

    return true;
  }

  /**
   * 生成序列ID
   * @returns 序列ID
   */
  private generateSequenceId(): string {
    return `seq_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成轨道ID
   * @returns 轨道ID
   */
  private generateTrackId(): string {
    return `track_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成节点ID
   * @returns 节点ID
   */
  private generateNodeId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 播放控制 ====================

  /**
   * 播放序列
   * @param sequenceId 序列ID
   * @param startTime 开始时间
   * @returns 是否成功开始播放
   */
  public play(sequenceId?: string, startTime: number = 0): boolean {
    const sequence = sequenceId ? this.sequences.get(sequenceId) : this.currentSequence;
    if (!sequence) {
      console.warn('没有可播放的序列');
      return false;
    }

    if (this.playbackState.isPlaying) {
      this.stop();
    }

    this.currentSequence = sequence;
    this.playbackState.isPlaying = true;
    this.playbackState.isPaused = false;
    this.playbackState.currentTime = startTime;

    this.startPlaybackTimer();
    this.emit('playbackStarted', sequence, startTime);

    if (this.config.debug) {
      console.log(`开始播放序列: ${sequence.name}`);
    }

    return true;
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (!this.playbackState.isPlaying || this.playbackState.isPaused) return;

    this.playbackState.isPaused = true;
    this.stopPlaybackTimer();
    this.emit('playbackPaused', this.playbackState.currentTime);

    if (this.config.debug) {
      console.log('暂停播放');
    }
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    if (!this.playbackState.isPlaying || !this.playbackState.isPaused) return;

    this.playbackState.isPaused = false;
    this.startPlaybackTimer();
    this.emit('playbackResumed', this.playbackState.currentTime);

    if (this.config.debug) {
      console.log('恢复播放');
    }
  }

  /**
   * 停止播放
   */
  public stop(): void {
    if (!this.playbackState.isPlaying) return;

    this.playbackState.isPlaying = false;
    this.playbackState.isPaused = false;
    this.playbackState.currentTime = 0;
    this.stopPlaybackTimer();
    this.emit('playbackStopped');

    if (this.config.debug) {
      console.log('停止播放');
    }
  }

  /**
   * 跳转到指定时间
   * @param time 时间
   */
  public seekTo(time: number): void {
    if (!this.currentSequence) return;

    this.playbackState.currentTime = Math.max(0, Math.min(time, this.currentSequence.duration));
    this.emit('playbackSeeked', this.playbackState.currentTime);

    if (this.config.debug) {
      console.log(`跳转到时间: ${this.playbackState.currentTime}`);
    }
  }

  /**
   * 设置播放速度
   * @param speed 播放速度
   */
  public setPlaybackSpeed(speed: number): void {
    this.playbackState.playbackSpeed = Math.max(0.1, Math.min(speed, 5.0));
    this.emit('playbackSpeedChanged', this.playbackState.playbackSpeed);

    if (this.config.debug) {
      console.log(`设置播放速度: ${this.playbackState.playbackSpeed}`);
    }
  }

  /**
   * 获取播放状态
   * @returns 播放状态
   */
  public getPlaybackState(): PlaybackState {
    return { ...this.playbackState };
  }

  /**
   * 开始播放定时器
   */
  private startPlaybackTimer(): void {
    this.stopPlaybackTimer();

    const frameInterval = 1000 / (this.currentSequence?.frameRate || this.config.defaultFrameRate);

    this.playbackTimer = setInterval(() => {
      if (!this.playbackState.isPlaying || this.playbackState.isPaused || !this.currentSequence) {
        return;
      }

      const deltaTime = (frameInterval / 1000) * this.playbackState.playbackSpeed;
      this.playbackState.currentTime += deltaTime;

      // 检查是否播放完成
      if (this.playbackState.currentTime >= this.currentSequence.duration) {
        if (this.currentSequence.loop) {
          this.playbackState.currentTime = 0;
          this.playbackState.loopCount++;
          this.emit('playbackLooped', this.playbackState.loopCount);
        } else {
          this.stop();
          this.emit('playbackCompleted');
          return;
        }
      }

      // 更新当前播放的动作
      this.updateCurrentActions();
      this.emit('playbackTimeUpdated', this.playbackState.currentTime);

    }, frameInterval);
  }

  /**
   * 停止播放定时器
   */
  private stopPlaybackTimer(): void {
    if (this.playbackTimer) {
      clearInterval(this.playbackTimer);
      this.playbackTimer = undefined;
    }
  }

  /**
   * 更新当前播放的动作
   */
  private updateCurrentActions(): void {
    if (!this.currentSequence) return;

    const currentTime = this.playbackState.currentTime;
    const activeNodes: Array<{ track: ActionTrack; node: SequenceNode }> = [];

    // 找到当前时间活跃的节点
    for (const track of this.currentSequence.tracks) {
      if (track.muted) continue;

      for (const node of track.nodes) {
        if (node.muted) continue;

        const nodeStartTime = node.startTime;
        const nodeEndTime = node.startTime + node.duration;

        if (currentTime >= nodeStartTime && currentTime < nodeEndTime) {
          activeNodes.push({ track, node });
        }
      }
    }

    // 触发动作更新事件
    this.emit('activeNodesUpdated', activeNodes, currentTime);
  }

  // ==================== 导出和导入 ====================

  /**
   * 导出序列为JSON
   * @param sequenceId 序列ID
   * @returns JSON字符串
   */
  public exportSequenceToJSON(sequenceId: string): string | null {
    const sequence = this.sequences.get(sequenceId);
    if (!sequence) return null;

    // 转换Map为普通对象以便序列化
    const exportData = {
      ...sequence,
      metadata: Object.fromEntries(sequence.metadata),
      tracks: sequence.tracks.map(track => ({
        ...track,
        nodes: track.nodes.map(node => ({
          ...node,
          parameters: Object.fromEntries(node.parameters)
        }))
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 从JSON导入序列
   * @param jsonData JSON数据
   * @returns 序列ID
   */
  public importSequenceFromJSON(jsonData: string): string | null {
    try {
      const data = JSON.parse(jsonData);

      // 生成新的ID
      const newId = this.generateSequenceId();

      const sequence: ActionSequence = {
        ...data,
        id: newId,
        metadata: new Map(Object.entries(data.metadata || {})),
        tracks: data.tracks.map((track: any) => ({
          ...track,
          id: this.generateTrackId(),
          nodes: track.nodes.map((node: any) => ({
            ...node,
            id: this.generateNodeId(),
            parameters: new Map(Object.entries(node.parameters || {}))
          }))
        })),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.sequences.set(sequence.id, sequence);
      this.emit('sequenceImported', sequence);

      if (this.config.debug) {
        console.log(`导入序列: ${sequence.name}`);
      }

      return sequence.id;
    } catch (error) {
      console.error('导入序列失败:', error);
      return null;
    }
  }

  /**
   * 导出所有序列
   * @returns JSON字符串
   */
  public exportAllSequences(): string {
    const allSequences = Array.from(this.sequences.values());
    const exportData = allSequences.map(sequence => ({
      ...sequence,
      metadata: Object.fromEntries(sequence.metadata),
      tracks: sequence.tracks.map(track => ({
        ...track,
        nodes: track.nodes.map(node => ({
          ...node,
          parameters: Object.fromEntries(node.parameters)
        }))
      }))
    }));

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 复制序列
   * @param sequenceId 源序列ID
   * @param newName 新序列名称
   * @returns 新序列ID
   */
  public duplicateSequence(sequenceId: string, newName?: string): string | null {
    const sourceSequence = this.sequences.get(sequenceId);
    if (!sourceSequence) return null;

    const jsonData = this.exportSequenceToJSON(sequenceId);
    if (!jsonData) return null;

    const newSequenceId = this.importSequenceFromJSON(jsonData);
    if (!newSequenceId) return null;

    const newSequence = this.sequences.get(newSequenceId)!;
    newSequence.name = newName || `${sourceSequence.name} (副本)`;

    this.emit('sequenceDuplicated', sourceSequence, newSequence);

    if (this.config.debug) {
      console.log(`复制序列: ${sourceSequence.name} -> ${newSequence.name}`);
    }

    return newSequenceId;
  }

  // ==================== 清理和销毁 ====================

  /**
   * 清理资源
   */
  public dispose(): void {
    // 停止播放
    this.stop();

    // 清理定时器
    this.stopPlaybackTimer();
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }

    // 清理数据
    this.actionClips.clear();
    this.sequences.clear();
    this.undoStack = [];
    this.redoStack = [];
    this.currentSequence = undefined;

    // 移除所有事件监听器
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('ActionSequenceComposer: 资源已清理');
    }
  }
}
