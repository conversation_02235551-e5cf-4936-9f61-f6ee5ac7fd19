import { Injectable } from '@nestjs/common';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import * as cheerio from 'cheerio';
import { marked } from 'marked';

export interface ParsedDocument {
  content: string;
  metadata: {
    title?: string;
    author?: string;
    pageCount?: number;
    wordCount?: number;
    language?: string;
  };
}

@Injectable()
export class DocumentParserService {
  /**
   * 解析文档内容
   */
  async parseDocument(
    buffer: Buffer,
    mimeType: string,
    filename: string,
  ): Promise<ParsedDocument> {
    switch (mimeType) {
      case 'text/plain':
        return this.parseTextFile(buffer);
      case 'application/pdf':
        return this.parsePDF(buffer);
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.parseDocx(buffer);
      case 'application/msword':
        return this.parseDoc(buffer);
      case 'text/html':
        return this.parseHTML(buffer);
      case 'text/markdown':
        return this.parseMarkdown(buffer);
      default:
        throw new Error(`不支持的文件类型: ${mimeType}`);
    }
  }

  /**
   * 解析纯文本文件
   */
  private async parseTextFile(buffer: Buffer): Promise<ParsedDocument> {
    const content = buffer.toString('utf-8');
    
    return {
      content: content.trim(),
      metadata: {
        wordCount: this.countWords(content),
        language: this.detectLanguage(content),
      },
    };
  }

  /**
   * 解析PDF文件
   */
  private async parsePDF(buffer: Buffer): Promise<ParsedDocument> {
    try {
      const data = await pdfParse(buffer);
      
      return {
        content: data.text.trim(),
        metadata: {
          title: data.info?.Title,
          author: data.info?.Author,
          pageCount: data.numpages,
          wordCount: this.countWords(data.text),
          language: this.detectLanguage(data.text),
        },
      };
    } catch (error) {
      throw new Error(`PDF解析失败: ${error.message}`);
    }
  }

  /**
   * 解析DOCX文件
   */
  private async parseDocx(buffer: Buffer): Promise<ParsedDocument> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      const content = result.value.trim();
      
      return {
        content,
        metadata: {
          wordCount: this.countWords(content),
          language: this.detectLanguage(content),
        },
      };
    } catch (error) {
      throw new Error(`DOCX解析失败: ${error.message}`);
    }
  }

  /**
   * 解析DOC文件
   */
  private async parseDoc(buffer: Buffer): Promise<ParsedDocument> {
    // DOC格式比较复杂，这里简化处理
    // 在生产环境中可能需要使用专门的库如 node-word-extractor
    try {
      const content = buffer.toString('utf-8');
      const cleanContent = this.cleanDocContent(content);
      
      return {
        content: cleanContent,
        metadata: {
          wordCount: this.countWords(cleanContent),
          language: this.detectLanguage(cleanContent),
        },
      };
    } catch (error) {
      throw new Error(`DOC解析失败: ${error.message}`);
    }
  }

  /**
   * 解析HTML文件
   */
  private async parseHTML(buffer: Buffer): Promise<ParsedDocument> {
    const html = buffer.toString('utf-8');
    const $ = cheerio.load(html);
    
    // 移除脚本和样式标签
    $('script, style').remove();
    
    // 提取文本内容
    const content = $('body').text() || $.text();
    const title = $('title').text() || $('h1').first().text();
    
    return {
      content: content.trim(),
      metadata: {
        title,
        wordCount: this.countWords(content),
        language: this.detectLanguage(content),
      },
    };
  }

  /**
   * 解析Markdown文件
   */
  private async parseMarkdown(buffer: Buffer): Promise<ParsedDocument> {
    const markdown = buffer.toString('utf-8');
    
    // 将Markdown转换为HTML，然后提取纯文本
    const html = marked(markdown);
    const $ = cheerio.load(html);
    const content = $.text().trim();
    
    // 提取标题（第一个#标题）
    const titleMatch = markdown.match(/^#\s+(.+)$/m);
    const title = titleMatch ? titleMatch[1] : undefined;
    
    return {
      content,
      metadata: {
        title,
        wordCount: this.countWords(content),
        language: this.detectLanguage(content),
      },
    };
  }

  /**
   * 清理DOC文件内容
   */
  private cleanDocContent(content: string): string {
    // 移除控制字符和特殊字符
    return content
      .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // 移除控制字符
      .replace(/[^\x20-\x7E\u4e00-\u9fa5]/g, ' ') // 保留ASCII和中文字符
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();
  }

  /**
   * 统计单词数量
   */
  private countWords(text: string): number {
    // 中文按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    
    return chineseChars + englishWords;
  }

  /**
   * 检测语言
   */
  private detectLanguage(text: string): string {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const totalChars = text.length;
    
    // 如果中文字符占比超过30%，认为是中文
    if (chineseChars / totalChars > 0.3) {
      return 'zh-CN';
    }
    
    // 简单的英文检测
    const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
    if (englishChars / totalChars > 0.5) {
      return 'en';
    }
    
    return 'unknown';
  }

  /**
   * 验证解析结果
   */
  validateParsedContent(parsed: ParsedDocument): boolean {
    // 检查内容是否为空
    if (!parsed.content || parsed.content.trim().length === 0) {
      return false;
    }
    
    // 检查内容长度是否合理
    if (parsed.content.length < 10) {
      return false;
    }
    
    // 检查是否包含过多的特殊字符
    const specialCharRatio = (parsed.content.match(/[^\w\s\u4e00-\u9fa5]/g) || []).length / parsed.content.length;
    if (specialCharRatio > 0.5) {
      return false;
    }
    
    return true;
  }

  /**
   * 提取文档摘要
   */
  extractSummary(content: string, maxLength: number = 200): string {
    // 按句子分割
    const sentences = content.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
    
    let summary = '';
    for (const sentence of sentences) {
      if (summary.length + sentence.length > maxLength) {
        break;
      }
      summary += sentence.trim() + '。';
    }
    
    return summary || content.substring(0, maxLength) + '...';
  }
}
