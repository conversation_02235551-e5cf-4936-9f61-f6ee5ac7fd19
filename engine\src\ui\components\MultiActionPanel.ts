/**
 * 多动作管理面板
 * 用于管理数字人的多个动作和动画
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { MultiActionFusionManager, ActionLibraryItem, BatchImportResult } from '../../avatar';

/**
 * 面板配置
 */
export interface MultiActionPanelConfig {
  /** 容器元素 */
  container: HTMLElement;
  /** 是否显示调试信息 */
  showDebugInfo?: boolean;
  /** 最大显示动作数量 */
  maxDisplayActions?: number;
  /** 是否允许拖拽排序 */
  allowDragSort?: boolean;
}

/**
 * 动作项状态
 */
export interface ActionItemState {
  /** 是否选中 */
  selected: boolean;
  /** 是否正在播放 */
  playing: boolean;
  /** 是否有冲突 */
  hasConflict: boolean;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 多动作管理面板
 */
export class MultiActionPanel extends EventEmitter {
  /** 配置 */
  private config: MultiActionPanelConfig;

  /** 容器元素 */
  private container: HTMLElement;

  /** 融合管理器 */
  private fusionManager: MultiActionFusionManager | null = null;

  /** 动作项状态映射 */
  private actionStates: Map<string, ActionItemState> = new Map();

  /** 当前选中的动作 */
  private selectedActions: Set<string> = new Set();

  /** UI元素 */
  private elements: {
    header?: HTMLElement;
    toolbar?: HTMLElement;
    actionList?: HTMLElement;
    footer?: HTMLElement;
    uploadArea?: HTMLElement;
    progressBar?: HTMLElement;
  } = {};

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: MultiActionPanelConfig) {
    super();

    this.config = {
      showDebugInfo: false,
      maxDisplayActions: 50,
      allowDragSort: true,
      ...config
    };

    this.container = config.container;
    this.initializeUI();
    this.setupEventListeners();
  }

  /**
   * 设置融合管理器
   * @param manager 融合管理器
   */
  public setFusionManager(manager: MultiActionFusionManager): void {
    this.fusionManager = manager;
    this.setupFusionManagerEvents();
    this.refreshActionList();
  }

  /**
   * 初始化UI
   */
  private initializeUI(): void {
    this.container.className = 'multi-action-panel';
    this.container.innerHTML = `
      <div class="panel-header">
        <h3>动作管理</h3>
        <div class="action-count">0 个动作</div>
      </div>
      
      <div class="panel-toolbar">
        <button class="btn btn-primary" id="import-bip-btn">
          <i class="icon-upload"></i>
          导入BIP文件
        </button>
        <button class="btn btn-secondary" id="play-selected-btn" disabled>
          <i class="icon-play"></i>
          播放选中
        </button>
        <button class="btn btn-secondary" id="stop-all-btn">
          <i class="icon-stop"></i>
          停止所有
        </button>
        <button class="btn btn-danger" id="delete-selected-btn" disabled>
          <i class="icon-delete"></i>
          删除选中
        </button>
      </div>

      <div class="upload-area" id="upload-area" style="display: none;">
        <div class="upload-content">
          <i class="icon-upload-cloud"></i>
          <p>拖拽BIP文件到此处或点击选择文件</p>
          <input type="file" id="file-input" multiple accept=".bip" style="display: none;">
          <button class="btn btn-outline" id="select-files-btn">选择文件</button>
        </div>
        <div class="upload-progress" id="upload-progress" style="display: none;">
          <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
          </div>
          <div class="progress-text" id="progress-text">准备上传...</div>
        </div>
      </div>

      <div class="action-list" id="action-list">
        <div class="empty-state">
          <i class="icon-animation"></i>
          <p>暂无动作</p>
          <p class="text-muted">点击"导入BIP文件"开始添加动作</p>
        </div>
      </div>

      <div class="panel-footer">
        <div class="status-info">
          <span class="status-text">就绪</span>
        </div>
        <div class="debug-info" style="display: ${this.config.showDebugInfo ? 'block' : 'none'};">
          <small class="text-muted">调试信息</small>
        </div>
      </div>
    `;

    // 获取UI元素引用
    this.elements.header = this.container.querySelector('.panel-header') as HTMLElement;
    this.elements.toolbar = this.container.querySelector('.panel-toolbar') as HTMLElement;
    this.elements.actionList = this.container.querySelector('.action-list') as HTMLElement;
    this.elements.footer = this.container.querySelector('.panel-footer') as HTMLElement;
    this.elements.uploadArea = this.container.querySelector('.upload-area') as HTMLElement;
    this.elements.progressBar = this.container.querySelector('.upload-progress') as HTMLElement;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 导入BIP文件按钮
    const importBtn = this.container.querySelector('#import-bip-btn') as HTMLButtonElement;
    importBtn.addEventListener('click', () => this.showUploadArea());

    // 播放选中按钮
    const playBtn = this.container.querySelector('#play-selected-btn') as HTMLButtonElement;
    playBtn.addEventListener('click', () => this.playSelectedActions());

    // 停止所有按钮
    const stopBtn = this.container.querySelector('#stop-all-btn') as HTMLButtonElement;
    stopBtn.addEventListener('click', () => this.stopAllActions());

    // 删除选中按钮
    const deleteBtn = this.container.querySelector('#delete-selected-btn') as HTMLButtonElement;
    deleteBtn.addEventListener('click', () => this.deleteSelectedActions());

    // 文件选择
    const fileInput = this.container.querySelector('#file-input') as HTMLInputElement;
    const selectFilesBtn = this.container.querySelector('#select-files-btn') as HTMLButtonElement;
    
    selectFilesBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', (e) => this.handleFileSelection(e));

    // 拖拽上传
    const uploadArea = this.elements.uploadArea!;
    uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
    uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
    uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
  }

  /**
   * 设置融合管理器事件
   */
  private setupFusionManagerEvents(): void {
    if (!this.fusionManager) return;

    // 监听批量导入开始
    this.fusionManager.on('batchImportStarted', (files: File[]) => {
      this.showProgress(`开始导入 ${files.length} 个文件...`, 0);
    });

    // 监听批量导入完成
    this.fusionManager.on('batchImportCompleted', (result: BatchImportResult) => {
      this.hideProgress();
      this.refreshActionList();
      this.updateStatus(`导入完成: ${result.successCount}/${result.totalFiles} 个文件成功`);
      
      if (result.conflicts.length > 0) {
        this.showConflictDialog(result.conflicts);
      }
    });

    // 监听冲突解决
    this.fusionManager.on('conflictResolved', (conflict: any, resolution: any) => {
      this.updateStatus(`冲突已解决: ${resolution.reason}`);
    });

    // 监听动作转换
    this.fusionManager.on('actionTransitionCompleted', (from: string, to: string) => {
      this.updateActionPlayingState(from, false);
      this.updateActionPlayingState(to, true);
    });
  }

  /**
   * 显示上传区域
   */
  private showUploadArea(): void {
    this.elements.uploadArea!.style.display = 'block';
    this.elements.actionList!.style.display = 'none';
  }

  /**
   * 隐藏上传区域
   */
  private hideUploadArea(): void {
    this.elements.uploadArea!.style.display = 'none';
    this.elements.actionList!.style.display = 'block';
  }

  /**
   * 处理文件选择
   * @param event 文件选择事件
   */
  private handleFileSelection(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.importBIPFiles(Array.from(input.files));
    }
  }

  /**
   * 处理拖拽悬停
   * @param event 拖拽事件
   */
  private handleDragOver(event: DragEvent): void {
    event.preventDefault();
    this.elements.uploadArea!.classList.add('drag-over');
  }

  /**
   * 处理文件拖拽放下
   * @param event 拖拽事件
   */
  private handleFileDrop(event: DragEvent): void {
    event.preventDefault();
    this.elements.uploadArea!.classList.remove('drag-over');

    const files = Array.from(event.dataTransfer?.files || []);
    const bipFiles = files.filter(file => file.name.toLowerCase().endsWith('.bip'));

    if (bipFiles.length > 0) {
      this.importBIPFiles(bipFiles);
    } else {
      this.updateStatus('请选择有效的BIP文件');
    }
  }

  /**
   * 处理拖拽离开
   * @param event 拖拽事件
   */
  private handleDragLeave(event: DragEvent): void {
    this.elements.uploadArea!.classList.remove('drag-over');
  }

  /**
   * 导入BIP文件
   * @param files BIP文件列表
   */
  private async importBIPFiles(files: File[]): Promise<void> {
    if (!this.fusionManager) {
      this.updateStatus('错误: 未设置融合管理器');
      return;
    }

    this.hideUploadArea();
    this.showProgress('正在导入文件...', 0);

    try {
      await this.fusionManager.importMultipleBIPFiles(files);
    } catch (error) {
      this.hideProgress();
      this.updateStatus(`导入失败: ${error.message}`);
    }
  }

  /**
   * 刷新动作列表
   */
  private refreshActionList(): void {
    if (!this.fusionManager) return;

    const actions = this.fusionManager.getActionLibraryItems();
    this.renderActionList(actions);
    this.updateActionCount(actions.length);
  }

  /**
   * 渲染动作列表
   * @param actions 动作列表
   */
  private renderActionList(actions: ActionLibraryItem[]): void {
    const actionList = this.elements.actionList!;

    if (actions.length === 0) {
      actionList.innerHTML = `
        <div class="empty-state">
          <i class="icon-animation"></i>
          <p>暂无动作</p>
          <p class="text-muted">点击"导入BIP文件"开始添加动作</p>
        </div>
      `;
      return;
    }

    const html = actions.map(action => this.renderActionItem(action)).join('');
    actionList.innerHTML = html;

    // 添加动作项事件监听器
    this.setupActionItemEvents();
  }

  /**
   * 渲染动作项
   * @param action 动作项
   * @returns HTML字符串
   */
  private renderActionItem(action: ActionLibraryItem): string {
    const state = this.actionStates.get(action.name) || {
      selected: false,
      playing: false,
      hasConflict: action.hasConflict,
      enabled: action.status === 'ready'
    };

    const statusClass = action.status === 'ready' ? 'ready' : 'processing';
    const conflictClass = state.hasConflict ? 'has-conflict' : '';
    const selectedClass = state.selected ? 'selected' : '';
    const playingClass = state.playing ? 'playing' : '';

    return `
      <div class="action-item ${statusClass} ${conflictClass} ${selectedClass} ${playingClass}" 
           data-action="${action.name}">
        <div class="action-checkbox">
          <input type="checkbox" ${state.selected ? 'checked' : ''}>
        </div>
        <div class="action-info">
          <div class="action-name">${action.name}</div>
          <div class="action-details">
            <span class="duration">${action.duration.toFixed(2)}s</span>
            <span class="source">${action.sourceFile}</span>
            ${state.hasConflict ? '<span class="conflict-badge">冲突</span>' : ''}
          </div>
        </div>
        <div class="action-controls">
          <button class="btn-icon play-btn" title="播放">
            <i class="icon-play"></i>
          </button>
          <button class="btn-icon stop-btn" title="停止" style="display: none;">
            <i class="icon-stop"></i>
          </button>
          <button class="btn-icon delete-btn" title="删除">
            <i class="icon-delete"></i>
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 设置动作项事件
   */
  private setupActionItemEvents(): void {
    const actionItems = this.elements.actionList!.querySelectorAll('.action-item');

    actionItems.forEach(item => {
      const actionName = item.getAttribute('data-action')!;
      
      // 复选框事件
      const checkbox = item.querySelector('input[type="checkbox"]') as HTMLInputElement;
      checkbox.addEventListener('change', () => {
        this.toggleActionSelection(actionName, checkbox.checked);
      });

      // 播放按钮事件
      const playBtn = item.querySelector('.play-btn') as HTMLButtonElement;
      playBtn.addEventListener('click', () => {
        this.playAction(actionName);
      });

      // 停止按钮事件
      const stopBtn = item.querySelector('.stop-btn') as HTMLButtonElement;
      stopBtn.addEventListener('click', () => {
        this.stopAction(actionName);
      });

      // 删除按钮事件
      const deleteBtn = item.querySelector('.delete-btn') as HTMLButtonElement;
      deleteBtn.addEventListener('click', () => {
        this.deleteAction(actionName);
      });
    });
  }

  /**
   * 切换动作选择状态
   * @param actionName 动作名称
   * @param selected 是否选中
   */
  private toggleActionSelection(actionName: string, selected: boolean): void {
    const state = this.actionStates.get(actionName) || {
      selected: false,
      playing: false,
      hasConflict: false,
      enabled: true
    };

    state.selected = selected;
    this.actionStates.set(actionName, state);

    if (selected) {
      this.selectedActions.add(actionName);
    } else {
      this.selectedActions.delete(actionName);
    }

    this.updateToolbarButtons();
    this.emit('actionSelectionChanged', actionName, selected);
  }

  /**
   * 播放动作
   * @param actionName 动作名称
   */
  private async playAction(actionName: string): Promise<void> {
    if (!this.fusionManager) return;

    try {
      await this.fusionManager.playAction(actionName, {
        fadeTime: 0.3,
        loop: true
      });

      this.updateActionPlayingState(actionName, true);
      this.emit('actionPlayed', actionName);

    } catch (error) {
      this.updateStatus(`播放失败: ${error.message}`);
    }
  }

  /**
   * 停止动作
   * @param actionName 动作名称
   */
  private stopAction(actionName: string): void {
    // TODO: 实现停止动作逻辑
    this.updateActionPlayingState(actionName, false);
    this.emit('actionStopped', actionName);
  }

  /**
   * 删除动作
   * @param actionName 动作名称
   */
  private deleteAction(actionName: string): void {
    if (confirm(`确定要删除动作 "${actionName}" 吗？`)) {
      // TODO: 实现删除动作逻辑
      this.actionStates.delete(actionName);
      this.selectedActions.delete(actionName);
      this.refreshActionList();
      this.emit('actionDeleted', actionName);
    }
  }

  /**
   * 播放选中的动作
   */
  private playSelectedActions(): void {
    for (const actionName of this.selectedActions) {
      this.playAction(actionName);
    }
  }

  /**
   * 停止所有动作
   */
  private stopAllActions(): void {
    for (const [actionName, state] of this.actionStates) {
      if (state.playing) {
        this.stopAction(actionName);
      }
    }
  }

  /**
   * 删除选中的动作
   */
  private deleteSelectedActions(): void {
    if (this.selectedActions.size === 0) return;

    const actionNames = Array.from(this.selectedActions);
    if (confirm(`确定要删除选中的 ${actionNames.length} 个动作吗？`)) {
      for (const actionName of actionNames) {
        this.deleteAction(actionName);
      }
    }
  }

  /**
   * 更新动作播放状态
   * @param actionName 动作名称
   * @param playing 是否播放中
   */
  private updateActionPlayingState(actionName: string, playing: boolean): void {
    const state = this.actionStates.get(actionName);
    if (state) {
      state.playing = playing;
      this.actionStates.set(actionName, state);
    }

    // 更新UI
    const actionItem = this.container.querySelector(`[data-action="${actionName}"]`);
    if (actionItem) {
      const playBtn = actionItem.querySelector('.play-btn') as HTMLElement;
      const stopBtn = actionItem.querySelector('.stop-btn') as HTMLElement;

      if (playing) {
        actionItem.classList.add('playing');
        playBtn.style.display = 'none';
        stopBtn.style.display = 'inline-block';
      } else {
        actionItem.classList.remove('playing');
        playBtn.style.display = 'inline-block';
        stopBtn.style.display = 'none';
      }
    }
  }

  /**
   * 更新工具栏按钮状态
   */
  private updateToolbarButtons(): void {
    const playBtn = this.container.querySelector('#play-selected-btn') as HTMLButtonElement;
    const deleteBtn = this.container.querySelector('#delete-selected-btn') as HTMLButtonElement;

    const hasSelection = this.selectedActions.size > 0;
    playBtn.disabled = !hasSelection;
    deleteBtn.disabled = !hasSelection;
  }

  /**
   * 更新动作数量
   * @param count 动作数量
   */
  private updateActionCount(count: number): void {
    const countElement = this.container.querySelector('.action-count') as HTMLElement;
    countElement.textContent = `${count} 个动作`;
  }

  /**
   * 显示进度
   * @param message 消息
   * @param progress 进度百分比
   */
  private showProgress(message: string, progress: number): void {
    const progressBar = this.elements.progressBar!;
    const progressFill = progressBar.querySelector('#progress-fill') as HTMLElement;
    const progressText = progressBar.querySelector('#progress-text') as HTMLElement;

    progressBar.style.display = 'block';
    progressFill.style.width = `${progress}%`;
    progressText.textContent = message;
  }

  /**
   * 隐藏进度
   */
  private hideProgress(): void {
    this.elements.progressBar!.style.display = 'none';
  }

  /**
   * 更新状态
   * @param message 状态消息
   */
  private updateStatus(message: string): void {
    const statusText = this.container.querySelector('.status-text') as HTMLElement;
    statusText.textContent = message;

    // 3秒后恢复默认状态
    setTimeout(() => {
      statusText.textContent = '就绪';
    }, 3000);
  }

  /**
   * 显示冲突对话框
   * @param conflicts 冲突列表
   */
  private showConflictDialog(conflicts: any[]): void {
    // TODO: 实现冲突解决对话框
    console.log('检测到冲突:', conflicts);
  }

  /**
   * 销毁面板
   */
  public dispose(): void {
    this.actionStates.clear();
    this.selectedActions.clear();
    this.removeAllListeners();
  }
}
