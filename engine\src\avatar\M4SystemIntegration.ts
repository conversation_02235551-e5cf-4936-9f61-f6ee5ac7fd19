/**
 * M4系统集成主入口
 * 整合所有数字人制作系统的功能模块，提供统一的系统接口
 * 实现完整的数字人制作、编辑、管理和分享生态系统
 */

import { Logger } from '../../core/Logger';
import { EventEmitter } from 'events';

// 核心系统
import { DigitalHumanIntegrationSystem } from './systems/DigitalHumanIntegrationSystem';
import { PerformanceOptimizer } from './optimization/PerformanceOptimizer';
import { UserExperienceOptimizer } from './ux/UserExperienceOptimizer';
import { SystemTestSuite } from './testing/SystemTestSuite';

// 服务模块
import { DigitalHumanMarketplaceService } from './marketplace/DigitalHumanMarketplaceService';
import { MinIOStorageService } from '../storage/MinIOStorageService';

// 类型定义
import { 
  IntegrationConfig,
  SystemStatistics,
  SystemHealthCheck,
  OptimizationSuggestion,
  BatchOperationResult,
  IntegrationResult,
  DigitalHumanCreationData,
  AutoOptimizationConfig,
  QualitySettings
} from './types/IntegrationTypes';

/**
 * M4系统集成配置
 */
interface M4SystemConfig {
  integration: Partial<IntegrationConfig>;
  optimization: Partial<AutoOptimizationConfig>;
  marketplace: {
    apiBaseUrl: string;
    apiKey: string;
  };
  storage: {
    endpoint: string;
    accessKey: string;
    secretKey: string;
  };
  testing: {
    enableAutoTesting: boolean;
    testInterval: number; // minutes
  };
}

/**
 * 系统状态
 */
interface M4SystemStatus {
  isInitialized: boolean;
  isRunning: boolean;
  startTime: number;
  lastHealthCheck: number;
  systemHealth: 'healthy' | 'warning' | 'error';
  activeModules: string[];
  statistics: SystemStatistics;
}

/**
 * M4数字人制作系统集成管理器
 * 
 * 功能特性：
 * - 完整的数字人制作工作流
 * - 多种创建方式（照片、文件上传、市场下载、BIP骨骼）
 * - 智能性能优化和监控
 * - 用户体验优化
 * - 数字人市场生态
 * - 全面的测试和质量保证
 * - 系统健康监控和自动恢复
 */
export class M4SystemIntegration extends EventEmitter {
  private logger: Logger;
  private config: M4SystemConfig;
  private status: M4SystemStatus;

  // 核心系统模块
  private integrationSystem: DigitalHumanIntegrationSystem;
  private performanceOptimizer: PerformanceOptimizer;
  private uxOptimizer: UserExperienceOptimizer;
  private testSuite: SystemTestSuite;

  // 服务模块
  private marketplaceService: DigitalHumanMarketplaceService;
  private storageService: MinIOStorageService;

  // 定时器
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private autoTestTimer: NodeJS.Timeout | null = null;
  private statisticsTimer: NodeJS.Timeout | null = null;

  constructor(config: M4SystemConfig) {
    super();
    this.logger = new Logger('M4SystemIntegration');
    this.config = config;
    
    this.status = {
      isInitialized: false,
      isRunning: false,
      startTime: 0,
      lastHealthCheck: 0,
      systemHealth: 'healthy',
      activeModules: [],
      statistics: {
        totalDigitalHumans: 0,
        activeDigitalHumans: 0,
        totalMemoryUsage: 0,
        averageRenderTime: 0,
        totalStorageUsed: 0,
        networkTraffic: { uploaded: 0, downloaded: 0 },
        userActivity: { activeUsers: 0, totalSessions: 0, averageSessionDuration: 0 },
        systemUptime: 0
      }
    };
  }

  /**
   * 初始化M4系统
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('开始初始化M4数字人制作系统...');
      const startTime = Date.now();

      // 1. 初始化存储服务
      await this.initializeStorageService();
      this.status.activeModules.push('storage');

      // 2. 初始化市场服务
      await this.initializeMarketplaceService();
      this.status.activeModules.push('marketplace');

      // 3. 初始化核心集成系统
      await this.initializeIntegrationSystem();
      this.status.activeModules.push('integration');

      // 4. 初始化性能优化器
      await this.initializePerformanceOptimizer();
      this.status.activeModules.push('performance');

      // 5. 初始化用户体验优化器
      await this.initializeUXOptimizer();
      this.status.activeModules.push('ux');

      // 6. 初始化测试套件
      await this.initializeTestSuite();
      this.status.activeModules.push('testing');

      // 7. 设置事件监听
      this.setupEventListeners();

      // 8. 启动定时任务
      this.startTimers();

      const duration = Date.now() - startTime;
      this.status.isInitialized = true;
      this.status.startTime = Date.now();

      this.logger.info(`M4系统初始化完成，耗时: ${duration}ms`);
      this.emit('systemInitialized', { duration, modules: this.status.activeModules });

    } catch (error) {
      this.logger.error('M4系统初始化失败:', error);
      this.status.systemHealth = 'error';
      throw error;
    }
  }

  /**
   * 启动M4系统
   */
  public async start(): Promise<void> {
    if (!this.status.isInitialized) {
      throw new Error('系统未初始化，请先调用 initialize()');
    }

    try {
      this.logger.info('启动M4数字人制作系统...');

      // 启动所有子系统
      if (this.integrationSystem) {
        // 集成系统已在初始化时启动
      }

      if (this.performanceOptimizer) {
        this.performanceOptimizer.setEnabled(true);
      }

      // 执行初始健康检查
      await this.performHealthCheck();

      // 如果启用自动测试，运行初始测试
      if (this.config.testing.enableAutoTesting) {
        await this.runSystemTests();
      }

      this.status.isRunning = true;
      this.logger.info('M4系统启动成功');
      this.emit('systemStarted');

    } catch (error) {
      this.logger.error('M4系统启动失败:', error);
      this.status.systemHealth = 'error';
      throw error;
    }
  }

  /**
   * 停止M4系统
   */
  public async stop(): Promise<void> {
    try {
      this.logger.info('停止M4数字人制作系统...');

      // 停止定时器
      this.stopTimers();

      // 停止性能优化器
      if (this.performanceOptimizer) {
        this.performanceOptimizer.setEnabled(false);
      }

      this.status.isRunning = false;
      this.logger.info('M4系统已停止');
      this.emit('systemStopped');

    } catch (error) {
      this.logger.error('M4系统停止失败:', error);
      throw error;
    }
  }

  /**
   * 创建数字人（统一入口）
   */
  public async createDigitalHuman(creationData: DigitalHumanCreationData): Promise<IntegrationResult> {
    if (!this.integrationSystem) {
      throw new Error('集成系统未初始化');
    }

    return await this.integrationSystem.createDigitalHuman(creationData);
  }

  /**
   * 批量创建数字人
   */
  public async batchCreateDigitalHumans(
    creationDataList: DigitalHumanCreationData[]
  ): Promise<BatchOperationResult<IntegrationResult>> {
    if (!this.integrationSystem) {
      throw new Error('集成系统未初始化');
    }

    return await this.integrationSystem.batchCreateDigitalHumans(creationDataList);
  }

  /**
   * 获取系统状态
   */
  public getSystemStatus(): M4SystemStatus {
    return { ...this.status };
  }

  /**
   * 获取系统统计信息
   */
  public getSystemStatistics(): SystemStatistics {
    if (this.integrationSystem) {
      const integrationStats = this.integrationSystem.getSystemStatus();
      this.status.statistics = {
        ...this.status.statistics,
        activeDigitalHumans: integrationStats.activeDigitalHumans.size,
        totalMemoryUsage: integrationStats.performanceMetrics.memoryUsage,
        averageRenderTime: 1000 / integrationStats.performanceMetrics.renderFPS,
        systemUptime: Date.now() - this.status.startTime
      };
    }

    return { ...this.status.statistics };
  }

  /**
   * 执行系统健康检查
   */
  public async performHealthCheck(): Promise<SystemHealthCheck> {
    try {
      this.logger.info('执行系统健康检查...');

      let healthCheck: SystemHealthCheck;

      if (this.integrationSystem) {
        healthCheck = await this.integrationSystem.getSystemHealthCheck();
      } else {
        healthCheck = {
          overall: 'error',
          components: {
            storage: 'error',
            ai: 'error',
            bip: 'error',
            marketplace: 'error',
            versioning: 'error'
          },
          issues: [{
            component: 'system',
            severity: 'critical',
            message: '集成系统未初始化',
            timestamp: Date.now()
          }]
        };
      }

      this.status.systemHealth = healthCheck.overall;
      this.status.lastHealthCheck = Date.now();

      this.emit('healthCheckCompleted', healthCheck);
      return healthCheck;

    } catch (error) {
      this.logger.error('健康检查失败:', error);
      this.status.systemHealth = 'error';
      throw error;
    }
  }

  /**
   * 获取优化建议
   */
  public getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // 从集成系统获取建议
    if (this.integrationSystem) {
      suggestions.push(...this.integrationSystem.getOptimizationSuggestions());
    }

    // 从用户体验优化器获取建议
    if (this.uxOptimizer) {
      const uxSuggestions = this.uxOptimizer.getOptimizationSuggestions();
      suggestions.push(...uxSuggestions.map(suggestion => ({
        type: 'quality' as const,
        priority: 'medium' as const,
        description: suggestion,
        action: suggestion,
        estimatedImpact: { quality: 0.1 }
      })));
    }

    return suggestions;
  }

  /**
   * 应用优化建议
   */
  public async applyOptimizationSuggestion(suggestion: OptimizationSuggestion): Promise<boolean> {
    if (this.integrationSystem) {
      return await this.integrationSystem.applyOptimizationSuggestion(suggestion);
    }
    return false;
  }

  /**
   * 运行系统测试
   */
  public async runSystemTests(): Promise<any> {
    if (!this.testSuite) {
      throw new Error('测试套件未初始化');
    }

    this.logger.info('开始运行系统测试...');
    const results = await this.testSuite.runFullTestSuite();
    
    this.emit('systemTestsCompleted', results);
    return results;
  }

  /**
   * 设置质量级别
   */
  public setQualityLevel(level: 'low' | 'medium' | 'high' | 'ultra'): void {
    if (this.performanceOptimizer) {
      this.performanceOptimizer.setQualityLevel(level);
    }
  }

  /**
   * 获取当前质量设置
   */
  public getCurrentQuality(): QualitySettings | null {
    if (this.performanceOptimizer) {
      return this.performanceOptimizer.getCurrentQuality();
    }
    return null;
  }

  // 私有初始化方法
  private async initializeStorageService(): Promise<void> {
    this.storageService = new MinIOStorageService();
    await this.storageService.initialize();
    this.logger.info('存储服务初始化完成');
  }

  private async initializeMarketplaceService(): Promise<void> {
    this.marketplaceService = new DigitalHumanMarketplaceService(
      this.config.marketplace.apiBaseUrl,
      this.config.marketplace.apiKey
    );
    this.logger.info('市场服务初始化完成');
  }

  private async initializeIntegrationSystem(): Promise<void> {
    this.integrationSystem = new DigitalHumanIntegrationSystem(this.config.integration);
    this.logger.info('集成系统初始化完成');
  }

  private async initializePerformanceOptimizer(): Promise<void> {
    this.performanceOptimizer = new PerformanceOptimizer(this.config.optimization);
    this.logger.info('性能优化器初始化完成');
  }

  private async initializeUXOptimizer(): Promise<void> {
    this.uxOptimizer = new UserExperienceOptimizer();
    this.logger.info('用户体验优化器初始化完成');
  }

  private async initializeTestSuite(): Promise<void> {
    this.testSuite = new SystemTestSuite(
      this.integrationSystem,
      this.performanceOptimizer,
      this.marketplaceService
    );
    this.logger.info('测试套件初始化完成');
  }

  private setupEventListeners(): void {
    // 监听集成系统事件
    if (this.integrationSystem) {
      this.integrationSystem.on('digitalHumanCreated', (data) => {
        this.emit('digitalHumanCreated', data);
      });

      this.integrationSystem.on('healthStatusChanged', (status) => {
        this.status.systemHealth = status;
        this.emit('healthStatusChanged', status);
      });
    }

    // 监听性能优化器事件
    if (this.performanceOptimizer) {
      this.performanceOptimizer.on('optimizationApplied', (data) => {
        this.emit('optimizationApplied', data);
      });
    }

    // 监听用户体验优化器事件
    if (this.uxOptimizer) {
      this.uxOptimizer.on('preferencesUpdated', (data) => {
        this.emit('preferencesUpdated', data);
      });
    }
  }

  private startTimers(): void {
    // 健康检查定时器（每5分钟）
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, 5 * 60 * 1000);

    // 自动测试定时器
    if (this.config.testing.enableAutoTesting) {
      this.autoTestTimer = setInterval(async () => {
        await this.runSystemTests();
      }, this.config.testing.testInterval * 60 * 1000);
    }

    // 统计信息更新定时器（每30秒）
    this.statisticsTimer = setInterval(() => {
      this.getSystemStatistics();
    }, 30 * 1000);
  }

  private stopTimers(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    if (this.autoTestTimer) {
      clearInterval(this.autoTestTimer);
      this.autoTestTimer = null;
    }

    if (this.statisticsTimer) {
      clearInterval(this.statisticsTimer);
      this.statisticsTimer = null;
    }
  }

  /**
   * 系统更新（每帧调用）
   */
  public update(deltaTime: number): void {
    if (!this.status.isRunning) return;

    // 更新集成系统
    if (this.integrationSystem) {
      this.integrationSystem.update(deltaTime);
    }

    // 更新统计信息
    this.status.statistics.systemUptime = Date.now() - this.status.startTime;
  }

  /**
   * 销毁M4系统
   */
  public dispose(): void {
    this.logger.info('销毁M4数字人制作系统...');

    // 停止定时器
    this.stopTimers();

    // 销毁所有子系统
    if (this.integrationSystem) {
      this.integrationSystem.dispose();
    }

    if (this.performanceOptimizer) {
      this.performanceOptimizer.dispose();
    }

    if (this.uxOptimizer) {
      this.uxOptimizer.dispose();
    }

    // 移除所有事件监听器
    this.removeAllListeners();

    this.status.isRunning = false;
    this.status.isInitialized = false;

    this.logger.info('M4系统已销毁');
  }
}
