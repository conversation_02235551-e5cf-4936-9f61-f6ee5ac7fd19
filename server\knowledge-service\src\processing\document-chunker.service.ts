import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

export interface ChunkOptions {
  chunkSize: number;
  overlap: number;
  preserveSentences: boolean;
  minChunkSize?: number;
  maxChunkSize?: number;
}

export interface DocumentChunk {
  id: string;
  content: string;
  index: number;
  startOffset: number;
  endOffset: number;
  metadata?: any;
}

@Injectable()
export class DocumentChunkerService {
  /**
   * 将文档分块
   */
  async chunkDocument(
    content: string,
    options: ChunkOptions,
  ): Promise<DocumentChunk[]> {
    if (!content || content.trim().length === 0) {
      throw new Error('文档内容为空');
    }

    const chunks: DocumentChunk[] = [];
    
    if (options.preserveSentences) {
      return this.chunkBySentences(content, options);
    } else {
      return this.chunkBySize(content, options);
    }
  }

  /**
   * 按句子分块
   */
  private chunkBySentences(
    content: string,
    options: ChunkOptions,
  ): DocumentChunk[] {
    const sentences = this.splitIntoSentences(content);
    const chunks: DocumentChunk[] = [];
    
    let currentChunk = '';
    let currentOffset = 0;
    let chunkIndex = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      const sentenceWithSpace = (currentChunk ? ' ' : '') + sentence;

      // 检查添加当前句子是否会超过块大小
      if (currentChunk.length + sentenceWithSpace.length > options.chunkSize && currentChunk.length > 0) {
        // 创建当前块
        const chunk = this.createChunk(
          currentChunk.trim(),
          chunkIndex,
          currentOffset,
          currentOffset + currentChunk.length,
        );
        chunks.push(chunk);
        chunkIndex++;

        // 处理重叠
        if (options.overlap > 0 && chunks.length > 0) {
          const overlapText = this.getOverlapText(currentChunk, options.overlap);
          currentChunk = overlapText + sentenceWithSpace;
          currentOffset = this.findOverlapOffset(content, overlapText, currentOffset);
        } else {
          currentChunk = sentence;
          currentOffset = content.indexOf(sentence, currentOffset);
        }
      } else {
        currentChunk += sentenceWithSpace;
      }
    }

    // 添加最后一个块
    if (currentChunk.trim().length > 0) {
      const chunk = this.createChunk(
        currentChunk.trim(),
        chunkIndex,
        currentOffset,
        currentOffset + currentChunk.length,
      );
      chunks.push(chunk);
    }

    return this.validateAndFilterChunks(chunks, options);
  }

  /**
   * 按固定大小分块
   */
  private chunkBySize(
    content: string,
    options: ChunkOptions,
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    let chunkIndex = 0;
    let currentOffset = 0;

    while (currentOffset < content.length) {
      let endOffset = Math.min(currentOffset + options.chunkSize, content.length);
      
      // 尝试在单词边界处分割
      if (endOffset < content.length) {
        const nextSpace = content.indexOf(' ', endOffset);
        const prevSpace = content.lastIndexOf(' ', endOffset);
        
        // 选择更接近目标大小的边界
        if (nextSpace !== -1 && (nextSpace - currentOffset) <= options.maxChunkSize) {
          endOffset = nextSpace;
        } else if (prevSpace !== -1 && (prevSpace - currentOffset) >= options.minChunkSize) {
          endOffset = prevSpace;
        }
      }

      const chunkContent = content.substring(currentOffset, endOffset).trim();
      
      if (chunkContent.length > 0) {
        const chunk = this.createChunk(
          chunkContent,
          chunkIndex,
          currentOffset,
          endOffset,
        );
        chunks.push(chunk);
        chunkIndex++;
      }

      // 计算下一个块的起始位置（考虑重叠）
      currentOffset = endOffset - options.overlap;
      if (currentOffset <= chunks[chunks.length - 1]?.startOffset) {
        currentOffset = endOffset;
      }
    }

    return this.validateAndFilterChunks(chunks, options);
  }

  /**
   * 将文本分割成句子
   */
  private splitIntoSentences(text: string): string[] {
    // 中英文句子分割正则表达式
    const sentenceRegex = /[^。！？.!?]*[。！？.!?]+/g;
    const sentences = text.match(sentenceRegex) || [];
    
    // 如果没有匹配到句子，按段落分割
    if (sentences.length === 0) {
      return text.split(/\n\s*\n/).filter(s => s.trim().length > 0);
    }
    
    return sentences.map(s => s.trim()).filter(s => s.length > 0);
  }

  /**
   * 创建文档块
   */
  private createChunk(
    content: string,
    index: number,
    startOffset: number,
    endOffset: number,
  ): DocumentChunk {
    const id = this.generateChunkId(content, index);
    
    return {
      id,
      content,
      index,
      startOffset,
      endOffset,
      metadata: {
        length: content.length,
        wordCount: this.countWords(content),
        hash: this.calculateContentHash(content),
      },
    };
  }

  /**
   * 生成块ID
   */
  private generateChunkId(content: string, index: number): string {
    const hash = crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
    return `chunk_${index}_${hash}`;
  }

  /**
   * 计算内容哈希
   */
  private calculateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * 获取重叠文本
   */
  private getOverlapText(text: string, overlapSize: number): string {
    if (overlapSize <= 0 || text.length <= overlapSize) {
      return '';
    }
    
    const overlapText = text.substring(text.length - overlapSize);
    
    // 尝试在单词边界处开始重叠
    const spaceIndex = overlapText.indexOf(' ');
    if (spaceIndex !== -1) {
      return overlapText.substring(spaceIndex + 1);
    }
    
    return overlapText;
  }

  /**
   * 查找重叠文本的偏移量
   */
  private findOverlapOffset(
    fullText: string,
    overlapText: string,
    currentOffset: number,
  ): number {
    if (!overlapText) {
      return currentOffset;
    }
    
    const index = fullText.indexOf(overlapText, Math.max(0, currentOffset - overlapText.length));
    return index !== -1 ? index : currentOffset;
  }

  /**
   * 验证和过滤块
   */
  private validateAndFilterChunks(
    chunks: DocumentChunk[],
    options: ChunkOptions,
  ): DocumentChunk[] {
    return chunks.filter(chunk => {
      // 检查最小大小
      if (options.minChunkSize && chunk.content.length < options.minChunkSize) {
        return false;
      }
      
      // 检查最大大小
      if (options.maxChunkSize && chunk.content.length > options.maxChunkSize) {
        return false;
      }
      
      // 检查内容质量
      return this.isValidChunk(chunk);
    });
  }

  /**
   * 检查块是否有效
   */
  private isValidChunk(chunk: DocumentChunk): boolean {
    const content = chunk.content.trim();
    
    // 检查是否为空
    if (content.length === 0) {
      return false;
    }
    
    // 检查是否只包含特殊字符
    const meaningfulChars = content.replace(/[\s\W]/g, '');
    if (meaningfulChars.length < 3) {
      return false;
    }
    
    // 检查重复字符比例
    const uniqueChars = new Set(content.toLowerCase()).size;
    if (uniqueChars / content.length < 0.1) {
      return false;
    }
    
    return true;
  }

  /**
   * 统计单词数量
   */
  private countWords(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }

  /**
   * 合并小块
   */
  mergeSmallChunks(
    chunks: DocumentChunk[],
    minSize: number,
  ): DocumentChunk[] {
    const mergedChunks: DocumentChunk[] = [];
    let currentChunk: DocumentChunk | null = null;

    for (const chunk of chunks) {
      if (!currentChunk) {
        currentChunk = { ...chunk };
      } else if (currentChunk.content.length < minSize) {
        // 合并到当前块
        currentChunk.content += ' ' + chunk.content;
        currentChunk.endOffset = chunk.endOffset;
        currentChunk.metadata = {
          ...currentChunk.metadata,
          length: currentChunk.content.length,
          wordCount: this.countWords(currentChunk.content),
          hash: this.calculateContentHash(currentChunk.content),
        };
      } else {
        // 当前块足够大，保存并开始新块
        mergedChunks.push(currentChunk);
        currentChunk = { ...chunk };
      }
    }

    if (currentChunk) {
      mergedChunks.push(currentChunk);
    }

    return mergedChunks;
  }
}
