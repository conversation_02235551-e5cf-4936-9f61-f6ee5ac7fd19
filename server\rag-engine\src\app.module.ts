import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RAGModule } from './rag/rag.module';
import { CacheModule } from './cache/cache.service';
import productionConfig from '../knowledge-service/src/config/production.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [productionConfig],
      envFilePath: ['.env.local', '.env'],
    }),
    CacheModule,
    RAGModule,
  ],
})
export class AppModule {}
