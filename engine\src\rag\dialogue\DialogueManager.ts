/**
 * 对话管理系统
 * 实现意图识别、对话理解、情感分析、动作映射等功能
 */

import { RAGRetrievalEngine, RetrievalResult, ContextInfo, ConversationTurn } from '../retrieval/RAGRetrievalEngine';

/**
 * 意图类型枚举
 */
export enum IntentType {
  GREETING = 'greeting',
  QUESTION = 'question',
  NAVIGATION = 'navigation',
  EXPLANATION = 'explanation',
  RECOMMENDATION = 'recommendation',
  COMPARISON = 'comparison',
  GOODBYE = 'goodbye',
  UNKNOWN = 'unknown'
}

/**
 * 情感类型枚举
 */
export enum EmotionType {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  NEUTRAL = 'neutral',
  EXCITED = 'excited',
  CONFUSED = 'confused',
  FRUSTRATED = 'frustrated',
  SATISFIED = 'satisfied'
}

/**
 * 动作类型枚举
 */
export enum ActionType {
  SPEAK = 'speak',
  GESTURE = 'gesture',
  MOVE = 'move',
  POINT = 'point',
  NOD = 'nod',
  SHAKE_HEAD = 'shake_head',
  SMILE = 'smile',
  THINK = 'think',
  LOOK_AT = 'look_at',
  TURN_TO = 'turn_to'
}

/**
 * 意图识别结果接口
 */
export interface IntentRecognitionResult {
  intent: IntentType;
  confidence: number;
  entities: any[];
  parameters: Record<string, any>;
}

/**
 * 情感分析结果接口
 */
export interface EmotionAnalysisResult {
  emotion: EmotionType;
  intensity: number;
  confidence: number;
  indicators: string[];
}

/**
 * 动作映射结果接口
 */
export interface ActionMappingResult {
  actions: DigitalHumanAction[];
  priority: number;
  duration: number;
}

/**
 * 数字人动作接口
 */
export interface DigitalHumanAction {
  type: ActionType;
  parameters: Record<string, any>;
  duration: number;
  delay: number;
  priority: number;
}

/**
 * 对话响应接口
 */
export interface DialogueResponse {
  text: string;
  intent: IntentRecognitionResult;
  emotion: EmotionAnalysisResult;
  actions: ActionMappingResult;
  sources: any[];
  confidence: number;
  followUpQuestions: string[];
  metadata: {
    processingTime: number;
    retrievalResults: RetrievalResult;
  };
}

/**
 * 对话状态接口
 */
export interface DialogueState {
  sessionId: string;
  currentTopic: string;
  userProfile: any;
  conversationHistory: ConversationTurn[];
  context: ContextInfo;
  lastIntent: IntentType;
  lastEmotion: EmotionType;
}

/**
 * 意图识别器
 */
export class IntentRecognizer {
  /**
   * 识别意图
   */
  public recognizeIntent(text: string, context?: ContextInfo): IntentRecognitionResult {
    const lowerText = text.toLowerCase();
    
    // 简单的基于规则的意图识别
    if (this.isGreeting(lowerText)) {
      return {
        intent: IntentType.GREETING,
        confidence: 0.9,
        entities: [],
        parameters: {}
      };
    } else if (this.isQuestion(lowerText)) {
      return {
        intent: IntentType.QUESTION,
        confidence: 0.8,
        entities: this.extractQuestionEntities(text),
        parameters: { questionType: this.getQuestionType(lowerText) }
      };
    } else if (this.isNavigation(lowerText)) {
      return {
        intent: IntentType.NAVIGATION,
        confidence: 0.85,
        entities: this.extractLocationEntities(text),
        parameters: { direction: this.getNavigationDirection(lowerText) }
      };
    } else if (this.isExplanation(lowerText)) {
      return {
        intent: IntentType.EXPLANATION,
        confidence: 0.8,
        entities: this.extractExplanationEntities(text),
        parameters: {}
      };
    } else if (this.isRecommendation(lowerText)) {
      return {
        intent: IntentType.RECOMMENDATION,
        confidence: 0.75,
        entities: [],
        parameters: { category: this.getRecommendationCategory(lowerText) }
      };
    } else if (this.isComparison(lowerText)) {
      return {
        intent: IntentType.COMPARISON,
        confidence: 0.8,
        entities: this.extractComparisonEntities(text),
        parameters: {}
      };
    } else if (this.isGoodbye(lowerText)) {
      return {
        intent: IntentType.GOODBYE,
        confidence: 0.9,
        entities: [],
        parameters: {}
      };
    } else {
      return {
        intent: IntentType.UNKNOWN,
        confidence: 0.3,
        entities: [],
        parameters: {}
      };
    }
  }

  private isGreeting(text: string): boolean {
    const greetingPatterns = [
      '你好', '您好', 'hello', 'hi', '早上好', '下午好', '晚上好',
      '欢迎', 'welcome', '开始', 'start'
    ];
    return greetingPatterns.some(pattern => text.includes(pattern));
  }

  private isQuestion(text: string): boolean {
    const questionPatterns = [
      '什么', '如何', '怎么', '为什么', '哪里', '何时', '谁',
      'what', 'how', 'why', 'where', 'when', 'who', '?', '？'
    ];
    return questionPatterns.some(pattern => text.includes(pattern));
  }

  private isNavigation(text: string): boolean {
    const navigationPatterns = [
      '导航', '去', '到', '路线', '方向', '位置',
      'navigate', 'go', 'direction', 'location', 'way'
    ];
    return navigationPatterns.some(pattern => text.includes(pattern));
  }

  private isExplanation(text: string): boolean {
    const explanationPatterns = [
      '介绍', '解释', '说明', '告诉我', '讲解',
      'explain', 'tell me', 'describe', 'introduce'
    ];
    return explanationPatterns.some(pattern => text.includes(pattern));
  }

  private isRecommendation(text: string): boolean {
    const recommendationPatterns = [
      '推荐', '建议', '推荐一下', '有什么好的',
      'recommend', 'suggest', 'advice'
    ];
    return recommendationPatterns.some(pattern => text.includes(pattern));
  }

  private isComparison(text: string): boolean {
    const comparisonPatterns = [
      '比较', '对比', '区别', '不同', '相同',
      'compare', 'difference', 'similar', 'versus'
    ];
    return comparisonPatterns.some(pattern => text.includes(pattern));
  }

  private isGoodbye(text: string): boolean {
    const goodbyePatterns = [
      '再见', '拜拜', '结束', '退出', '谢谢',
      'goodbye', 'bye', 'exit', 'quit', 'thank you'
    ];
    return goodbyePatterns.some(pattern => text.includes(pattern));
  }

  private extractQuestionEntities(text: string): any[] {
    // 简单的实体提取
    const entities = [];
    
    // 提取展品名称
    const exhibitPattern = /展品[A-Z]|展品\d+/g;
    let match;
    while ((match = exhibitPattern.exec(text)) !== null) {
      entities.push({
        type: 'EXHIBIT',
        value: match[0],
        start: match.index,
        end: match.index + match[0].length
      });
    }
    
    return entities;
  }

  private extractLocationEntities(text: string): any[] {
    const entities = [];
    const locationPattern = /(展厅|大厅|入口|出口|二楼|三楼)/g;
    let match;
    while ((match = locationPattern.exec(text)) !== null) {
      entities.push({
        type: 'LOCATION',
        value: match[0],
        start: match.index,
        end: match.index + match[0].length
      });
    }
    return entities;
  }

  private extractExplanationEntities(text: string): any[] {
    return this.extractQuestionEntities(text);
  }

  private extractComparisonEntities(text: string): any[] {
    const entities = [];
    const items = text.split(/和|与|跟|比较|对比/);
    
    items.forEach((item, index) => {
      const trimmed = item.trim();
      if (trimmed.length > 0) {
        entities.push({
          type: 'COMPARISON_ITEM',
          value: trimmed,
          index
        });
      }
    });
    
    return entities;
  }

  private getQuestionType(text: string): string {
    if (text.includes('什么') || text.includes('what')) return 'what';
    if (text.includes('如何') || text.includes('怎么') || text.includes('how')) return 'how';
    if (text.includes('为什么') || text.includes('why')) return 'why';
    if (text.includes('哪里') || text.includes('where')) return 'where';
    if (text.includes('何时') || text.includes('when')) return 'when';
    if (text.includes('谁') || text.includes('who')) return 'who';
    return 'general';
  }

  private getNavigationDirection(text: string): string {
    if (text.includes('左') || text.includes('left')) return 'left';
    if (text.includes('右') || text.includes('right')) return 'right';
    if (text.includes('前') || text.includes('forward')) return 'forward';
    if (text.includes('后') || text.includes('back')) return 'back';
    if (text.includes('上') || text.includes('up')) return 'up';
    if (text.includes('下') || text.includes('down')) return 'down';
    return 'unknown';
  }

  private getRecommendationCategory(text: string): string {
    if (text.includes('展品') || text.includes('exhibit')) return 'exhibit';
    if (text.includes('路线') || text.includes('route')) return 'route';
    if (text.includes('活动') || text.includes('activity')) return 'activity';
    return 'general';
  }
}

/**
 * 情感分析器
 */
export class EmotionAnalyzer {
  /**
   * 分析情感
   */
  public analyzeEmotion(text: string, context?: ContextInfo): EmotionAnalysisResult {
    const lowerText = text.toLowerCase();
    
    // 积极情感词汇
    const positiveWords = ['好', '棒', '喜欢', '满意', '开心', '高兴', '谢谢', 'good', 'great', 'like', 'happy', 'thanks'];
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    
    // 消极情感词汇
    const negativeWords = ['不好', '糟糕', '讨厌', '失望', '生气', '烦', 'bad', 'terrible', 'hate', 'disappointed', 'angry'];
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    // 兴奋情感词汇
    const excitedWords = ['太棒了', 'amazing', '惊人', '不可思议', 'incredible', 'awesome'];
    const excitedCount = excitedWords.filter(word => lowerText.includes(word)).length;
    
    // 困惑情感词汇
    const confusedWords = ['不懂', '不明白', '困惑', 'confused', 'don\'t understand'];
    const confusedCount = confusedWords.filter(word => lowerText.includes(word)).length;
    
    // 沮丧情感词汇
    const frustratedWords = ['烦躁', '沮丧', '无聊', 'frustrated', 'boring', 'annoying'];
    const frustratedCount = frustratedWords.filter(word => lowerText.includes(word)).length;

    // 确定主要情感
    let emotion: EmotionType;
    let intensity: number;
    let confidence: number;
    let indicators: string[] = [];

    if (excitedCount > 0) {
      emotion = EmotionType.EXCITED;
      intensity = Math.min(1.0, excitedCount * 0.8);
      confidence = 0.9;
      indicators = excitedWords.filter(word => lowerText.includes(word));
    } else if (confusedCount > 0) {
      emotion = EmotionType.CONFUSED;
      intensity = Math.min(1.0, confusedCount * 0.7);
      confidence = 0.8;
      indicators = confusedWords.filter(word => lowerText.includes(word));
    } else if (frustratedCount > 0) {
      emotion = EmotionType.FRUSTRATED;
      intensity = Math.min(1.0, frustratedCount * 0.8);
      confidence = 0.85;
      indicators = frustratedWords.filter(word => lowerText.includes(word));
    } else if (positiveCount > negativeCount) {
      emotion = EmotionType.POSITIVE;
      intensity = Math.min(1.0, positiveCount * 0.6);
      confidence = 0.7;
      indicators = positiveWords.filter(word => lowerText.includes(word));
    } else if (negativeCount > positiveCount) {
      emotion = EmotionType.NEGATIVE;
      intensity = Math.min(1.0, negativeCount * 0.7);
      confidence = 0.75;
      indicators = negativeWords.filter(word => lowerText.includes(word));
    } else {
      emotion = EmotionType.NEUTRAL;
      intensity = 0.5;
      confidence = 0.6;
      indicators = [];
    }

    return {
      emotion,
      intensity,
      confidence,
      indicators
    };
  }
}

/**
 * 动作映射器
 */
export class ActionMapper {
  /**
   * 映射动作
   */
  public mapActions(
    intent: IntentRecognitionResult,
    emotion: EmotionAnalysisResult,
    responseText: string
  ): ActionMappingResult {
    const actions: DigitalHumanAction[] = [];

    // 基于意图的基础动作
    const baseActions = this.getBaseActionsForIntent(intent);
    actions.push(...baseActions);

    // 基于情感的修饰动作
    const emotionActions = this.getActionsForEmotion(emotion);
    actions.push(...emotionActions);

    // 基于响应内容的手势动作
    const gestureActions = this.getGestureActionsForText(responseText);
    actions.push(...gestureActions);

    // 计算总体优先级和持续时间
    const priority = this.calculatePriority(actions);
    const duration = this.calculateTotalDuration(actions);

    return {
      actions,
      priority,
      duration
    };
  }

  /**
   * 获取意图对应的基础动作
   */
  private getBaseActionsForIntent(intent: IntentRecognitionResult): DigitalHumanAction[] {
    const actions: DigitalHumanAction[] = [];

    switch (intent.intent) {
      case IntentType.GREETING:
        actions.push({
          type: ActionType.SMILE,
          parameters: { intensity: 0.8 },
          duration: 2000,
          delay: 0,
          priority: 1
        });
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'wave', hand: 'right' },
          duration: 1500,
          delay: 500,
          priority: 2
        });
        break;

      case IntentType.QUESTION:
        actions.push({
          type: ActionType.THINK,
          parameters: { intensity: 0.6 },
          duration: 1000,
          delay: 0,
          priority: 1
        });
        actions.push({
          type: ActionType.NOD,
          parameters: { count: 1 },
          duration: 800,
          delay: 1200,
          priority: 2
        });
        break;

      case IntentType.NAVIGATION:
        actions.push({
          type: ActionType.POINT,
          parameters: { direction: intent.parameters.direction || 'forward' },
          duration: 2000,
          delay: 500,
          priority: 1
        });
        actions.push({
          type: ActionType.TURN_TO,
          parameters: { direction: intent.parameters.direction || 'forward' },
          duration: 1000,
          delay: 0,
          priority: 2
        });
        break;

      case IntentType.EXPLANATION:
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'explain', hands: 'both' },
          duration: 3000,
          delay: 1000,
          priority: 1
        });
        break;

      case IntentType.RECOMMENDATION:
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'present', hand: 'right' },
          duration: 2000,
          delay: 500,
          priority: 1
        });
        actions.push({
          type: ActionType.SMILE,
          parameters: { intensity: 0.6 },
          duration: 1500,
          delay: 0,
          priority: 2
        });
        break;

      case IntentType.GOODBYE:
        actions.push({
          type: ActionType.SMILE,
          parameters: { intensity: 0.7 },
          duration: 2000,
          delay: 0,
          priority: 1
        });
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'goodbye', hand: 'right' },
          duration: 2000,
          delay: 1000,
          priority: 2
        });
        break;
    }

    return actions;
  }

  /**
   * 获取情感对应的动作
   */
  private getActionsForEmotion(emotion: EmotionAnalysisResult): DigitalHumanAction[] {
    const actions: DigitalHumanAction[] = [];

    switch (emotion.emotion) {
      case EmotionType.POSITIVE:
        actions.push({
          type: ActionType.SMILE,
          parameters: { intensity: emotion.intensity },
          duration: 1500,
          delay: 0,
          priority: 3
        });
        break;

      case EmotionType.EXCITED:
        actions.push({
          type: ActionType.SMILE,
          parameters: { intensity: 0.9 },
          duration: 2000,
          delay: 0,
          priority: 3
        });
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'excited', hands: 'both' },
          duration: 1500,
          delay: 500,
          priority: 4
        });
        break;

      case EmotionType.CONFUSED:
        actions.push({
          type: ActionType.THINK,
          parameters: { intensity: 0.8 },
          duration: 2000,
          delay: 0,
          priority: 3
        });
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'confused', hand: 'right' },
          duration: 1000,
          delay: 1000,
          priority: 4
        });
        break;

      case EmotionType.FRUSTRATED:
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'calm', hands: 'both' },
          duration: 2000,
          delay: 0,
          priority: 3
        });
        break;

      case EmotionType.NEGATIVE:
        actions.push({
          type: ActionType.GESTURE,
          parameters: { type: 'apologetic', hands: 'both' },
          duration: 1500,
          delay: 0,
          priority: 3
        });
        break;
    }

    return actions;
  }

  /**
   * 获取文本对应的手势动作
   */
  private getGestureActionsForText(text: string): DigitalHumanAction[] {
    const actions: DigitalHumanAction[] = [];

    // 检测数字和计数
    const numberPattern = /\d+/g;
    const numbers = text.match(numberPattern);
    if (numbers) {
      actions.push({
        type: ActionType.GESTURE,
        parameters: { type: 'count', count: parseInt(numbers[0]) },
        duration: 1000,
        delay: 500,
        priority: 5
      });
    }

    // 检测方向词汇
    if (text.includes('左') || text.includes('left')) {
      actions.push({
        type: ActionType.POINT,
        parameters: { direction: 'left' },
        duration: 1500,
        delay: 0,
        priority: 5
      });
    } else if (text.includes('右') || text.includes('right')) {
      actions.push({
        type: ActionType.POINT,
        parameters: { direction: 'right' },
        duration: 1500,
        delay: 0,
        priority: 5
      });
    }

    // 检测大小形容词
    if (text.includes('大') || text.includes('big') || text.includes('large')) {
      actions.push({
        type: ActionType.GESTURE,
        parameters: { type: 'big', hands: 'both' },
        duration: 1500,
        delay: 0,
        priority: 5
      });
    } else if (text.includes('小') || text.includes('small') || text.includes('little')) {
      actions.push({
        type: ActionType.GESTURE,
        parameters: { type: 'small', hands: 'both' },
        duration: 1000,
        delay: 0,
        priority: 5
      });
    }

    return actions;
  }

  /**
   * 计算动作优先级
   */
  private calculatePriority(actions: DigitalHumanAction[]): number {
    if (actions.length === 0) return 0;
    return Math.max(...actions.map(action => action.priority));
  }

  /**
   * 计算总持续时间
   */
  private calculateTotalDuration(actions: DigitalHumanAction[]): number {
    if (actions.length === 0) return 0;
    return Math.max(...actions.map(action => action.delay + action.duration));
  }
}

/**
 * 对话管理器
 */
export class DialogueManager {
  private ragEngine: RAGRetrievalEngine;
  private intentRecognizer: IntentRecognizer;
  private emotionAnalyzer: EmotionAnalyzer;
  private actionMapper: ActionMapper;
  private dialogueStates: Map<string, DialogueState> = new Map();

  constructor(ragEngine: RAGRetrievalEngine) {
    this.ragEngine = ragEngine;
    this.intentRecognizer = new IntentRecognizer();
    this.emotionAnalyzer = new EmotionAnalyzer();
    this.actionMapper = new ActionMapper();
  }

  /**
   * 处理用户输入
   */
  public async processUserInput(
    sessionId: string,
    userInput: string,
    context?: Partial<ContextInfo>
  ): Promise<DialogueResponse> {
    const startTime = Date.now();

    // 获取或创建对话状态
    const dialogueState = this.getOrCreateDialogueState(sessionId, context);

    // 意图识别
    const intent = this.intentRecognizer.recognizeIntent(userInput, dialogueState.context);

    // 情感分析
    const emotion = this.emotionAnalyzer.analyzeEmotion(userInput, dialogueState.context);

    // RAG检索
    const retrievalResult = await this.ragEngine.retrieve(
      userInput,
      dialogueState.context,
      {
        topK: 5,
        threshold: 0.7
      }
    );

    // 生成响应文本
    const responseText = this.generateResponse(intent, emotion, retrievalResult);

    // 动作映射
    const actionMapping = this.actionMapper.mapActions(intent, emotion, responseText);

    // 生成后续问题
    const followUpQuestions = this.generateFollowUpQuestions(intent, retrievalResult);

    // 更新对话状态
    this.updateDialogueState(sessionId, userInput, responseText, intent, emotion);

    const processingTime = Date.now() - startTime;

    return {
      text: responseText,
      intent,
      emotion,
      actions: actionMapping,
      sources: retrievalResult.searchResults,
      confidence: this.calculateResponseConfidence(intent, emotion, retrievalResult),
      followUpQuestions,
      metadata: {
        processingTime,
        retrievalResults: retrievalResult
      }
    };
  }

  /**
   * 获取或创建对话状态
   */
  private getOrCreateDialogueState(
    sessionId: string,
    context?: Partial<ContextInfo>
  ): DialogueState {
    if (!this.dialogueStates.has(sessionId)) {
      const newState: DialogueState = {
        sessionId,
        currentTopic: '',
        userProfile: context?.userProfile || null,
        conversationHistory: [],
        context: this.ragEngine.getContextManager().buildContext(
          sessionId,
          context?.currentLocation,
          context?.userProfile
        ),
        lastIntent: IntentType.UNKNOWN,
        lastEmotion: EmotionType.NEUTRAL
      };
      this.dialogueStates.set(sessionId, newState);
    }

    return this.dialogueStates.get(sessionId)!;
  }

  /**
   * 生成响应文本
   */
  private generateResponse(
    intent: IntentRecognitionResult,
    emotion: EmotionAnalysisResult,
    retrievalResult: RetrievalResult
  ): string {
    // 基于意图的基础响应模板
    let response = this.getBaseResponseForIntent(intent);

    // 如果有检索结果，整合知识内容
    if (retrievalResult.searchResults.length > 0) {
      const topResult = retrievalResult.searchResults[0];
      response = this.integrateKnowledgeIntoResponse(response, topResult, intent);
    }

    // 基于情感调整响应语调
    response = this.adjustResponseForEmotion(response, emotion);

    return response;
  }

  /**
   * 获取意图对应的基础响应
   */
  private getBaseResponseForIntent(intent: IntentRecognitionResult): string {
    switch (intent.intent) {
      case IntentType.GREETING:
        return '您好！欢迎来到虚拟展厅。我是您的数字导览员，很高兴为您服务。';
      case IntentType.QUESTION:
        return '让我为您查找相关信息...';
      case IntentType.NAVIGATION:
        return '我来为您指路。';
      case IntentType.EXPLANATION:
        return '我来为您详细介绍一下。';
      case IntentType.RECOMMENDATION:
        return '根据您的兴趣，我推荐您看看...';
      case IntentType.COMPARISON:
        return '让我为您比较一下这些内容...';
      case IntentType.GOODBYE:
        return '感谢您的参观！希望您今天的体验愉快。再见！';
      default:
        return '我理解您的问题，让我为您查找相关信息。';
    }
  }

  /**
   * 将知识内容整合到响应中
   */
  private integrateKnowledgeIntoResponse(
    baseResponse: string,
    searchResult: any,
    intent: IntentRecognitionResult
  ): string {
    const knowledge = searchResult.chunk.content;

    if (intent.intent === IntentType.QUESTION) {
      return `${baseResponse}\n\n${knowledge}`;
    } else if (intent.intent === IntentType.EXPLANATION) {
      return `${baseResponse}\n\n${knowledge}`;
    } else {
      return `${baseResponse}\n\n根据我的了解：${knowledge}`;
    }
  }

  /**
   * 基于情感调整响应
   */
  private adjustResponseForEmotion(response: string, emotion: EmotionAnalysisResult): string {
    switch (emotion.emotion) {
      case EmotionType.CONFUSED:
        return `我理解您可能有些困惑，让我用更简单的方式来解释：\n\n${response}`;
      case EmotionType.FRUSTRATED:
        return `我感觉到您可能有些不满意，让我重新为您解答：\n\n${response}`;
      case EmotionType.EXCITED:
        return `我很高兴看到您对此感兴趣！${response}`;
      case EmotionType.POSITIVE:
        return `很高兴为您服务！${response}`;
      default:
        return response;
    }
  }

  /**
   * 生成后续问题
   */
  private generateFollowUpQuestions(
    intent: IntentRecognitionResult,
    retrievalResult: RetrievalResult
  ): string[] {
    const questions: string[] = [];

    switch (intent.intent) {
      case IntentType.GREETING:
        questions.push('您想了解哪个展品？');
        questions.push('需要我为您推荐参观路线吗？');
        break;
      case IntentType.QUESTION:
        questions.push('您还想了解更多相关信息吗？');
        questions.push('有其他问题需要我帮助您吗？');
        break;
      case IntentType.EXPLANATION:
        questions.push('您想了解更多细节吗？');
        questions.push('有什么特别感兴趣的方面吗？');
        break;
      case IntentType.NAVIGATION:
        questions.push('到达后需要我介绍这里的展品吗？');
        questions.push('您想知道下一个推荐的参观点吗？');
        break;
    }

    return questions;
  }

  /**
   * 更新对话状态
   */
  private updateDialogueState(
    sessionId: string,
    userInput: string,
    response: string,
    intent: IntentRecognitionResult,
    emotion: EmotionAnalysisResult
  ): void {
    const state = this.dialogueStates.get(sessionId);
    if (!state) return;

    // 添加对话轮次
    const turn: ConversationTurn = {
      id: `turn_${Date.now()}`,
      userQuery: userInput,
      systemResponse: response,
      timestamp: new Date(),
      context: { intent: intent.intent, emotion: emotion.emotion }
    };

    state.conversationHistory.push(turn);
    state.lastIntent = intent.intent;
    state.lastEmotion = emotion.emotion;

    // 更新RAG引擎的对话历史
    this.ragEngine.addConversationTurn(sessionId, turn);
  }

  /**
   * 计算响应置信度
   */
  private calculateResponseConfidence(
    intent: IntentRecognitionResult,
    emotion: EmotionAnalysisResult,
    retrievalResult: RetrievalResult
  ): number {
    const intentConfidence = intent.confidence;
    const emotionConfidence = emotion.confidence;
    const retrievalConfidence = retrievalResult.retrievalMetadata.confidence;

    return (intentConfidence + emotionConfidence + retrievalConfidence) / 3;
  }

  /**
   * 获取对话状态
   */
  public getDialogueState(sessionId: string): DialogueState | undefined {
    return this.dialogueStates.get(sessionId);
  }

  /**
   * 清除会话
   */
  public clearSession(sessionId: string): void {
    this.dialogueStates.delete(sessionId);
    this.ragEngine.getContextManager().clearSession(sessionId);
  }

  /**
   * 获取所有活跃会话
   */
  public getActiveSessions(): string[] {
    return Array.from(this.dialogueStates.keys());
  }
}
