/**
 * BIP到标准骨骼映射器
 * 将BIP骨骼结构映射到DL引擎的标准骨骼结构
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPBone, BIPSkeletonData, BIPBoneType } from './BIPSkeletonParser';

/**
 * 标准骨骼类型
 */
export enum StandardBoneType {
  ROOT = 'root',
  HIPS = 'hips',
  SPINE = 'spine',
  CHEST = 'chest',
  NECK = 'neck',
  HEAD = 'head',
  
  LEFT_SHOULDER = 'leftShoulder',
  LEFT_UPPER_ARM = 'leftUpperArm',
  LEFT_LOWER_ARM = 'leftLowerArm',
  LEFT_HAND = 'leftHand',
  
  RIGHT_SHOULDER = 'rightShoulder',
  RIGHT_UPPER_ARM = 'rightUpperArm',
  RIGHT_LOWER_ARM = 'rightLowerArm',
  RIGHT_HAND = 'rightHand',
  
  LEFT_UPPER_LEG = 'leftUpperLeg',
  LEFT_LOWER_LEG = 'leftLowerLeg',
  LEFT_FOOT = 'leftFoot',
  LEFT_TOES = 'leftToes',
  
  RIGHT_UPPER_LEG = 'rightUpperLeg',
  RIGHT_LOWER_LEG = 'rightLowerLeg',
  RIGHT_FOOT = 'rightFoot',
  RIGHT_TOES = 'rightToes',
  
  // 手指
  LEFT_THUMB = 'leftThumb',
  LEFT_INDEX = 'leftIndex',
  LEFT_MIDDLE = 'leftMiddle',
  LEFT_RING = 'leftRing',
  LEFT_PINKY = 'leftPinky',
  
  RIGHT_THUMB = 'rightThumb',
  RIGHT_INDEX = 'rightIndex',
  RIGHT_MIDDLE = 'rightMiddle',
  RIGHT_RING = 'rightRing',
  RIGHT_PINKY = 'rightPinky'
}

/**
 * 标准骨骼
 */
export interface StandardBone {
  /** 骨骼类型 */
  type: StandardBoneType;
  /** 骨骼名称 */
  name: string;
  /** 父骨骼类型 */
  parentType?: StandardBoneType;
  /** 本地位置 */
  position: THREE.Vector3;
  /** 本地旋转 */
  rotation: THREE.Quaternion;
  /** 本地缩放 */
  scale: THREE.Vector3;
  /** 世界变换矩阵 */
  worldMatrix: THREE.Matrix4;
  /** 绑定姿势矩阵 */
  bindMatrix: THREE.Matrix4;
  /** 子骨骼类型列表 */
  children: StandardBoneType[];
  /** 原始BIP骨骼ID */
  originalBoneId?: number;
}

/**
 * 标准骨骼数据
 */
export interface StandardSkeletonData {
  /** 骨骼映射 */
  bones: Map<StandardBoneType, StandardBone>;
  /** 根骨骼类型 */
  rootBoneType: StandardBoneType;
  /** 层级结构 */
  hierarchy: StandardBoneHierarchy;
  /** 原始BIP数据引用 */
  originalBIPData: BIPSkeletonData;
}

/**
 * 标准骨骼层级结构
 */
export interface StandardBoneHierarchy {
  /** 根节点 */
  root: StandardBoneNode;
  /** 深度映射 */
  depthMap: Map<StandardBoneType, number>;
  /** 最大深度 */
  maxDepth: number;
}

/**
 * 标准骨骼节点
 */
export interface StandardBoneNode {
  /** 骨骼类型 */
  boneType: StandardBoneType;
  /** 子节点 */
  children: StandardBoneNode[];
  /** 深度 */
  depth: number;
}

/**
 * 骨骼映射规则
 */
export interface BoneMappingRule {
  /** BIP骨骼名称模式 */
  bipNamePattern: string | RegExp;
  /** 标准骨骼类型 */
  standardType: StandardBoneType;
  /** 优先级 */
  priority: number;
  /** 是否必需 */
  required: boolean;
  /** 变换调整 */
  transformAdjustment?: {
    positionOffset?: THREE.Vector3;
    rotationOffset?: THREE.Euler;
    scaleMultiplier?: THREE.Vector3;
  };
}

/**
 * 映射配置
 */
export interface MappingConfig {
  /** 是否严格模式（要求所有必需骨骼都存在） */
  strictMode?: boolean;
  /** 是否自动修复缺失骨骼 */
  autoFixMissing?: boolean;
  /** 是否保留原始变换 */
  preserveOriginalTransforms?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * BIP到标准骨骼映射器
 */
export class BIPToStandardMapping extends EventEmitter {
  /** 配置 */
  private config: MappingConfig;

  /** 骨骼映射规则 */
  private mappingRules: BoneMappingRule[];

  /** 标准骨骼层级定义 */
  private static readonly STANDARD_HIERARCHY: Record<StandardBoneType, StandardBoneType[]> = {
    [StandardBoneType.ROOT]: [StandardBoneType.HIPS],
    [StandardBoneType.HIPS]: [StandardBoneType.SPINE, StandardBoneType.LEFT_UPPER_LEG, StandardBoneType.RIGHT_UPPER_LEG],
    [StandardBoneType.SPINE]: [StandardBoneType.CHEST],
    [StandardBoneType.CHEST]: [StandardBoneType.NECK, StandardBoneType.LEFT_SHOULDER, StandardBoneType.RIGHT_SHOULDER],
    [StandardBoneType.NECK]: [StandardBoneType.HEAD],
    [StandardBoneType.HEAD]: [],
    
    [StandardBoneType.LEFT_SHOULDER]: [StandardBoneType.LEFT_UPPER_ARM],
    [StandardBoneType.LEFT_UPPER_ARM]: [StandardBoneType.LEFT_LOWER_ARM],
    [StandardBoneType.LEFT_LOWER_ARM]: [StandardBoneType.LEFT_HAND],
    [StandardBoneType.LEFT_HAND]: [StandardBoneType.LEFT_THUMB, StandardBoneType.LEFT_INDEX, StandardBoneType.LEFT_MIDDLE, StandardBoneType.LEFT_RING, StandardBoneType.LEFT_PINKY],
    
    [StandardBoneType.RIGHT_SHOULDER]: [StandardBoneType.RIGHT_UPPER_ARM],
    [StandardBoneType.RIGHT_UPPER_ARM]: [StandardBoneType.RIGHT_LOWER_ARM],
    [StandardBoneType.RIGHT_LOWER_ARM]: [StandardBoneType.RIGHT_HAND],
    [StandardBoneType.RIGHT_HAND]: [StandardBoneType.RIGHT_THUMB, StandardBoneType.RIGHT_INDEX, StandardBoneType.RIGHT_MIDDLE, StandardBoneType.RIGHT_RING, StandardBoneType.RIGHT_PINKY],
    
    [StandardBoneType.LEFT_UPPER_LEG]: [StandardBoneType.LEFT_LOWER_LEG],
    [StandardBoneType.LEFT_LOWER_LEG]: [StandardBoneType.LEFT_FOOT],
    [StandardBoneType.LEFT_FOOT]: [StandardBoneType.LEFT_TOES],
    [StandardBoneType.LEFT_TOES]: [],
    
    [StandardBoneType.RIGHT_UPPER_LEG]: [StandardBoneType.RIGHT_LOWER_LEG],
    [StandardBoneType.RIGHT_LOWER_LEG]: [StandardBoneType.RIGHT_FOOT],
    [StandardBoneType.RIGHT_FOOT]: [StandardBoneType.RIGHT_TOES],
    [StandardBoneType.RIGHT_TOES]: [],
    
    [StandardBoneType.LEFT_THUMB]: [],
    [StandardBoneType.LEFT_INDEX]: [],
    [StandardBoneType.LEFT_MIDDLE]: [],
    [StandardBoneType.LEFT_RING]: [],
    [StandardBoneType.LEFT_PINKY]: [],
    
    [StandardBoneType.RIGHT_THUMB]: [],
    [StandardBoneType.RIGHT_INDEX]: [],
    [StandardBoneType.RIGHT_MIDDLE]: [],
    [StandardBoneType.RIGHT_RING]: [],
    [StandardBoneType.RIGHT_PINKY]: []
  };

  /**
   * 构造函数
   * @param config 映射配置
   */
  constructor(config: MappingConfig = {}) {
    super();

    this.config = {
      strictMode: false,
      autoFixMissing: true,
      preserveOriginalTransforms: true,
      debug: false,
      ...config
    };

    // 初始化映射规则
    this.initializeMappingRules();
  }

  /**
   * 初始化映射规则
   */
  private initializeMappingRules(): void {
    this.mappingRules = [
      // 躯干
      { bipNamePattern: /^Bip01$|^Bip01 Pelvis$/, standardType: StandardBoneType.HIPS, priority: 10, required: true },
      { bipNamePattern: /^Bip01 Spine$/, standardType: StandardBoneType.SPINE, priority: 9, required: true },
      { bipNamePattern: /^Bip01 Spine1$|^Bip01 Spine2$|^Bip01 Spine3$/, standardType: StandardBoneType.CHEST, priority: 8, required: true },
      { bipNamePattern: /^Bip01 Neck$/, standardType: StandardBoneType.NECK, priority: 7, required: true },
      { bipNamePattern: /^Bip01 Head$/, standardType: StandardBoneType.HEAD, priority: 6, required: true },

      // 左臂
      { bipNamePattern: /^Bip01 L Clavicle$/, standardType: StandardBoneType.LEFT_SHOULDER, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L UpperArm$/, standardType: StandardBoneType.LEFT_UPPER_ARM, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L Forearm$/, standardType: StandardBoneType.LEFT_LOWER_ARM, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L Hand$/, standardType: StandardBoneType.LEFT_HAND, priority: 5, required: true },

      // 右臂
      { bipNamePattern: /^Bip01 R Clavicle$/, standardType: StandardBoneType.RIGHT_SHOULDER, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R UpperArm$/, standardType: StandardBoneType.RIGHT_UPPER_ARM, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R Forearm$/, standardType: StandardBoneType.RIGHT_LOWER_ARM, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R Hand$/, standardType: StandardBoneType.RIGHT_HAND, priority: 5, required: true },

      // 左腿
      { bipNamePattern: /^Bip01 L Thigh$/, standardType: StandardBoneType.LEFT_UPPER_LEG, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L Calf$/, standardType: StandardBoneType.LEFT_LOWER_LEG, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L Foot$/, standardType: StandardBoneType.LEFT_FOOT, priority: 5, required: true },
      { bipNamePattern: /^Bip01 L Toe0$/, standardType: StandardBoneType.LEFT_TOES, priority: 4, required: false },

      // 右腿
      { bipNamePattern: /^Bip01 R Thigh$/, standardType: StandardBoneType.RIGHT_UPPER_LEG, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R Calf$/, standardType: StandardBoneType.RIGHT_LOWER_LEG, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R Foot$/, standardType: StandardBoneType.RIGHT_FOOT, priority: 5, required: true },
      { bipNamePattern: /^Bip01 R Toe0$/, standardType: StandardBoneType.RIGHT_TOES, priority: 4, required: false },

      // 左手指
      { bipNamePattern: /^Bip01 L Finger0/, standardType: StandardBoneType.LEFT_THUMB, priority: 3, required: false },
      { bipNamePattern: /^Bip01 L Finger1/, standardType: StandardBoneType.LEFT_INDEX, priority: 3, required: false },
      { bipNamePattern: /^Bip01 L Finger2/, standardType: StandardBoneType.LEFT_MIDDLE, priority: 3, required: false },
      { bipNamePattern: /^Bip01 L Finger3/, standardType: StandardBoneType.LEFT_RING, priority: 3, required: false },
      { bipNamePattern: /^Bip01 L Finger4/, standardType: StandardBoneType.LEFT_PINKY, priority: 3, required: false },

      // 右手指
      { bipNamePattern: /^Bip01 R Finger0/, standardType: StandardBoneType.RIGHT_THUMB, priority: 3, required: false },
      { bipNamePattern: /^Bip01 R Finger1/, standardType: StandardBoneType.RIGHT_INDEX, priority: 3, required: false },
      { bipNamePattern: /^Bip01 R Finger2/, standardType: StandardBoneType.RIGHT_MIDDLE, priority: 3, required: false },
      { bipNamePattern: /^Bip01 R Finger3/, standardType: StandardBoneType.RIGHT_RING, priority: 3, required: false },
      { bipNamePattern: /^Bip01 R Finger4/, standardType: StandardBoneType.RIGHT_PINKY, priority: 3, required: false }
    ];
  }

  /**
   * 映射BIP骨骼到标准骨骼
   * @param bipData BIP骨骼数据
   * @returns Promise<StandardSkeletonData>
   */
  public async mapToStandardSkeleton(bipData: BIPSkeletonData): Promise<StandardSkeletonData> {
    try {
      if (this.config.debug) {
        console.log('[BIPToStandardMapping] 开始映射BIP骨骼到标准骨骼');
      }

      this.emit('mappingStarted', bipData);

      // 创建骨骼映射
      const boneMapping = this.createBoneMapping(bipData);

      // 构建标准骨骼
      const standardBones = this.buildStandardBones(bipData, boneMapping);

      // 修复缺失骨骼
      if (this.config.autoFixMissing) {
        this.fixMissingBones(standardBones);
      }

      // 验证骨骼结构
      if (this.config.strictMode) {
        this.validateStandardSkeleton(standardBones);
      }

      // 生成层级结构
      const hierarchy = this.generateStandardHierarchy(standardBones);

      const standardData: StandardSkeletonData = {
        bones: standardBones,
        rootBoneType: StandardBoneType.ROOT,
        hierarchy,
        originalBIPData: bipData
      };

      this.emit('mappingCompleted', standardData);

      if (this.config.debug) {
        console.log(`[BIPToStandardMapping] 映射完成，生成 ${standardBones.size} 个标准骨骼`);
      }

      return standardData;
    } catch (error) {
      this.emit('mappingError', error);
      throw error;
    }
  }

  /**
   * 创建骨骼映射
   * @param bipData BIP骨骼数据
   * @returns 骨骼映射表
   */
  private createBoneMapping(bipData: BIPSkeletonData): Map<StandardBoneType, BIPBone> {
    const mapping = new Map<StandardBoneType, BIPBone>();
    const usedBones = new Set<number>();

    // 按优先级排序映射规则
    const sortedRules = [...this.mappingRules].sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      let matchedBone: BIPBone | undefined;

      // 查找匹配的BIP骨骼
      for (const bone of bipData.bones) {
        if (usedBones.has(bone.id)) continue;

        let isMatch = false;
        if (typeof rule.bipNamePattern === 'string') {
          isMatch = bone.name === rule.bipNamePattern;
        } else {
          isMatch = rule.bipNamePattern.test(bone.name);
        }

        if (isMatch) {
          matchedBone = bone;
          break;
        }
      }

      if (matchedBone) {
        mapping.set(rule.standardType, matchedBone);
        usedBones.add(matchedBone.id);

        if (this.config.debug) {
          console.log(`[BIPToStandardMapping] 映射: ${matchedBone.name} -> ${rule.standardType}`);
        }
      } else if (rule.required && this.config.strictMode) {
        throw new Error(`必需的骨骼未找到: ${rule.standardType}`);
      }
    }

    return mapping;
  }

  /**
   * 构建标准骨骼
   * @param bipData BIP骨骼数据
   * @param boneMapping 骨骼映射
   * @returns 标准骨骼映射
   */
  private buildStandardBones(
    bipData: BIPSkeletonData,
    boneMapping: Map<StandardBoneType, BIPBone>
  ): Map<StandardBoneType, StandardBone> {
    const standardBones = new Map<StandardBoneType, StandardBone>();

    // 创建标准骨骼
    for (const [standardType, bipBone] of boneMapping) {
      const standardBone: StandardBone = {
        type: standardType,
        name: this.getStandardBoneName(standardType),
        position: this.config.preserveOriginalTransforms ? bipBone.position.clone() : new THREE.Vector3(),
        rotation: this.config.preserveOriginalTransforms ? bipBone.rotation.clone() : new THREE.Quaternion(),
        scale: this.config.preserveOriginalTransforms ? bipBone.scale.clone() : new THREE.Vector3(1, 1, 1),
        worldMatrix: new THREE.Matrix4(),
        bindMatrix: new THREE.Matrix4(),
        children: [],
        originalBoneId: bipBone.id
      };

      // 应用变换调整
      const rule = this.mappingRules.find(r => r.standardType === standardType);
      if (rule?.transformAdjustment) {
        this.applyTransformAdjustment(standardBone, rule.transformAdjustment);
      }

      standardBones.set(standardType, standardBone);
    }

    // 构建父子关系
    this.buildStandardHierarchyRelations(standardBones);

    // 计算世界变换
    this.calculateStandardWorldTransforms(standardBones);

    return standardBones;
  }

  /**
   * 获取标准骨骼名称
   * @param boneType 骨骼类型
   * @returns 骨骼名称
   */
  private getStandardBoneName(boneType: StandardBoneType): string {
    const nameMap: Record<StandardBoneType, string> = {
      [StandardBoneType.ROOT]: 'Root',
      [StandardBoneType.HIPS]: 'Hips',
      [StandardBoneType.SPINE]: 'Spine',
      [StandardBoneType.CHEST]: 'Chest',
      [StandardBoneType.NECK]: 'Neck',
      [StandardBoneType.HEAD]: 'Head',

      [StandardBoneType.LEFT_SHOULDER]: 'LeftShoulder',
      [StandardBoneType.LEFT_UPPER_ARM]: 'LeftUpperArm',
      [StandardBoneType.LEFT_LOWER_ARM]: 'LeftLowerArm',
      [StandardBoneType.LEFT_HAND]: 'LeftHand',

      [StandardBoneType.RIGHT_SHOULDER]: 'RightShoulder',
      [StandardBoneType.RIGHT_UPPER_ARM]: 'RightUpperArm',
      [StandardBoneType.RIGHT_LOWER_ARM]: 'RightLowerArm',
      [StandardBoneType.RIGHT_HAND]: 'RightHand',

      [StandardBoneType.LEFT_UPPER_LEG]: 'LeftUpperLeg',
      [StandardBoneType.LEFT_LOWER_LEG]: 'LeftLowerLeg',
      [StandardBoneType.LEFT_FOOT]: 'LeftFoot',
      [StandardBoneType.LEFT_TOES]: 'LeftToes',

      [StandardBoneType.RIGHT_UPPER_LEG]: 'RightUpperLeg',
      [StandardBoneType.RIGHT_LOWER_LEG]: 'RightLowerLeg',
      [StandardBoneType.RIGHT_FOOT]: 'RightFoot',
      [StandardBoneType.RIGHT_TOES]: 'RightToes',

      [StandardBoneType.LEFT_THUMB]: 'LeftThumb',
      [StandardBoneType.LEFT_INDEX]: 'LeftIndex',
      [StandardBoneType.LEFT_MIDDLE]: 'LeftMiddle',
      [StandardBoneType.LEFT_RING]: 'LeftRing',
      [StandardBoneType.LEFT_PINKY]: 'LeftPinky',

      [StandardBoneType.RIGHT_THUMB]: 'RightThumb',
      [StandardBoneType.RIGHT_INDEX]: 'RightIndex',
      [StandardBoneType.RIGHT_MIDDLE]: 'RightMiddle',
      [StandardBoneType.RIGHT_RING]: 'RightRing',
      [StandardBoneType.RIGHT_PINKY]: 'RightPinky'
    };

    return nameMap[boneType] || boneType;
  }

  /**
   * 应用变换调整
   * @param bone 标准骨骼
   * @param adjustment 变换调整
   */
  private applyTransformAdjustment(
    bone: StandardBone,
    adjustment: NonNullable<BoneMappingRule['transformAdjustment']>
  ): void {
    if (adjustment.positionOffset) {
      bone.position.add(adjustment.positionOffset);
    }

    if (adjustment.rotationOffset) {
      const offsetQuat = new THREE.Quaternion().setFromEuler(adjustment.rotationOffset);
      bone.rotation.multiply(offsetQuat);
    }

    if (adjustment.scaleMultiplier) {
      bone.scale.multiply(adjustment.scaleMultiplier);
    }
  }

  /**
   * 构建标准层级关系
   * @param standardBones 标准骨骼映射
   */
  private buildStandardHierarchyRelations(standardBones: Map<StandardBoneType, StandardBone>): void {
    for (const [boneType, bone] of standardBones) {
      // 查找父骨骼
      for (const [parentType, childTypes] of Object.entries(BIPToStandardMapping.STANDARD_HIERARCHY)) {
        if (childTypes.includes(boneType)) {
          bone.parentType = parentType as StandardBoneType;

          // 添加到父骨骼的子列表
          const parentBone = standardBones.get(parentType as StandardBoneType);
          if (parentBone && !parentBone.children.includes(boneType)) {
            parentBone.children.push(boneType);
          }
          break;
        }
      }
    }
  }

  /**
   * 计算标准世界变换
   * @param standardBones 标准骨骼映射
   */
  private calculateStandardWorldTransforms(standardBones: Map<StandardBoneType, StandardBone>): void {
    // 递归计算世界变换
    const calculateTransform = (boneType: StandardBoneType, parentWorldMatrix?: THREE.Matrix4) => {
      const bone = standardBones.get(boneType);
      if (!bone) return;

      // 构建本地变换矩阵
      const localMatrix = new THREE.Matrix4();
      localMatrix.compose(bone.position, bone.rotation, bone.scale);

      // 计算世界变换矩阵
      if (parentWorldMatrix) {
        bone.worldMatrix.multiplyMatrices(parentWorldMatrix, localMatrix);
      } else {
        bone.worldMatrix.copy(localMatrix);
      }

      // 计算绑定姿势矩阵
      bone.bindMatrix.copy(bone.worldMatrix).invert();

      // 递归处理子骨骼
      for (const childType of bone.children) {
        calculateTransform(childType, bone.worldMatrix);
      }
    };

    // 从根骨骼开始计算
    const rootBone = standardBones.get(StandardBoneType.ROOT);
    if (rootBone) {
      calculateTransform(StandardBoneType.ROOT);
    } else {
      // 如果没有根骨骼，从臀部开始
      calculateTransform(StandardBoneType.HIPS);
    }
  }

  /**
   * 修复缺失骨骼
   * @param standardBones 标准骨骼映射
   */
  private fixMissingBones(standardBones: Map<StandardBoneType, StandardBone>): void {
    // 检查必需的骨骼
    const requiredBones = this.mappingRules
      .filter(rule => rule.required)
      .map(rule => rule.standardType);

    for (const requiredType of requiredBones) {
      if (!standardBones.has(requiredType)) {
        // 创建缺失的骨骼
        const missingBone = this.createMissingBone(requiredType, standardBones);
        standardBones.set(requiredType, missingBone);

        if (this.config.debug) {
          console.log(`[BIPToStandardMapping] 创建缺失骨骼: ${requiredType}`);
        }
      }
    }

    // 确保根骨骼存在
    if (!standardBones.has(StandardBoneType.ROOT)) {
      const rootBone: StandardBone = {
        type: StandardBoneType.ROOT,
        name: 'Root',
        position: new THREE.Vector3(),
        rotation: new THREE.Quaternion(),
        scale: new THREE.Vector3(1, 1, 1),
        worldMatrix: new THREE.Matrix4(),
        bindMatrix: new THREE.Matrix4(),
        children: [StandardBoneType.HIPS]
      };
      standardBones.set(StandardBoneType.ROOT, rootBone);
    }
  }

  /**
   * 创建缺失骨骼
   * @param boneType 骨骼类型
   * @param existingBones 现有骨骼
   * @returns 创建的骨骼
   */
  private createMissingBone(
    boneType: StandardBoneType,
    existingBones: Map<StandardBoneType, StandardBone>
  ): StandardBone {
    // 尝试从相邻骨骼推断位置
    const position = this.inferBonePosition(boneType, existingBones);

    return {
      type: boneType,
      name: this.getStandardBoneName(boneType),
      position,
      rotation: new THREE.Quaternion(),
      scale: new THREE.Vector3(1, 1, 1),
      worldMatrix: new THREE.Matrix4(),
      bindMatrix: new THREE.Matrix4(),
      children: BIPToStandardMapping.STANDARD_HIERARCHY[boneType] || []
    };
  }

  /**
   * 推断骨骼位置
   * @param boneType 骨骼类型
   * @param existingBones 现有骨骼
   * @returns 推断的位置
   */
  private inferBonePosition(
    boneType: StandardBoneType,
    existingBones: Map<StandardBoneType, StandardBone>
  ): THREE.Vector3 {
    // TODO: 实现更智能的位置推断算法
    // 这里返回默认位置
    return new THREE.Vector3();
  }

  /**
   * 验证标准骨骼结构
   * @param standardBones 标准骨骼映射
   */
  private validateStandardSkeleton(standardBones: Map<StandardBoneType, StandardBone>): void {
    const requiredBones = this.mappingRules
      .filter(rule => rule.required)
      .map(rule => rule.standardType);

    for (const requiredType of requiredBones) {
      if (!standardBones.has(requiredType)) {
        throw new Error(`缺少必需的标准骨骼: ${requiredType}`);
      }
    }

    if (this.config.debug) {
      console.log('[BIPToStandardMapping] 标准骨骼结构验证通过');
    }
  }

  /**
   * 生成标准层级结构
   * @param standardBones 标准骨骼映射
   * @returns 层级结构
   */
  private generateStandardHierarchy(standardBones: Map<StandardBoneType, StandardBone>): StandardBoneHierarchy {
    const depthMap = new Map<StandardBoneType, number>();
    let maxDepth = 0;

    // 递归构建层级节点
    const buildNode = (boneType: StandardBoneType, depth: number): StandardBoneNode => {
      const bone = standardBones.get(boneType);
      if (!bone) {
        throw new Error(`标准骨骼不存在: ${boneType}`);
      }

      depthMap.set(boneType, depth);
      maxDepth = Math.max(maxDepth, depth);

      const node: StandardBoneNode = {
        boneType,
        children: [],
        depth
      };

      // 递归构建子节点
      for (const childType of bone.children) {
        if (standardBones.has(childType)) {
          node.children.push(buildNode(childType, depth + 1));
        }
      }

      return node;
    };

    // 从根骨骼开始构建
    let rootType = StandardBoneType.ROOT;
    if (!standardBones.has(rootType)) {
      rootType = StandardBoneType.HIPS;
    }

    const root = buildNode(rootType, 0);

    return {
      root,
      depthMap,
      maxDepth
    };
  }

  /**
   * 销毁映射器
   */
  public dispose(): void {
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[BIPToStandardMapping] 映射器已销毁');
    }
  }
}
