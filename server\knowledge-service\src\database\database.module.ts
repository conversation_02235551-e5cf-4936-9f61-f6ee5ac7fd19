import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  DigitalHuman,
  KnowledgeBase,
  KnowledgeDocument,
  DocumentChunk,
  DigitalHumanKnowledgeBinding,
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('production.metadataDatabase.host'),
        port: configService.get('production.metadataDatabase.port'),
        username: configService.get('production.metadataDatabase.username'),
        password: configService.get('production.metadataDatabase.password'),
        database: configService.get('production.metadataDatabase.database'),
        ssl: configService.get('production.metadataDatabase.ssl'),
        entities: [
          DigitalHuman,
          KnowledgeBase,
          KnowledgeDocument,
          DocumentChunk,
          DigitalHumanKnowledgeBinding,
        ],
        synchronize: process.env.NODE_ENV !== 'production',
        logging: process.env.NODE_ENV === 'development',
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        extra: {
          max: 20, // 最大连接数
          min: 5,  // 最小连接数
          acquire: 30000, // 获取连接超时时间
          idle: 10000,    // 空闲连接超时时间
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      DigitalHuman,
      KnowledgeBase,
      KnowledgeDocument,
      DocumentChunk,
      DigitalHumanKnowledgeBinding,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
