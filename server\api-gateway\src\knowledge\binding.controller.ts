import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BindingService } from './binding.service';

@ApiTags('数字人管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/digital-humans')
export class BindingController {
  constructor(private readonly bindingService: BindingService) {}

  @Post()
  @ApiOperation({ summary: '创建数字人' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: '数字人名称' },
        description: { type: 'string', description: '数字人描述' },
        modelConfig: { type: 'object', description: '模型配置' },
        voiceConfig: { type: 'object', description: '语音配置' },
        animationConfig: { type: 'object', description: '动画配置' },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '数字人创建成功',
  })
  async createDigitalHuman(@Body() body: any): Promise<any> {
    try {
      return await this.bindingService.createDigitalHuman(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '创建数字人失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({ summary: '获取数字人列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiResponse({
    status: 200,
    description: '数字人列表',
  })
  async getDigitalHumans(@Query() query: any): Promise<any> {
    try {
      return await this.bindingService.getDigitalHumans(query);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取数字人列表失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取数字人详情' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '数字人详情',
  })
  async getDigitalHuman(@Param('id') id: string): Promise<any> {
    try {
      return await this.bindingService.getDigitalHuman(id);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取数字人详情失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @ApiOperation({ summary: '更新数字人' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        modelConfig: { type: 'object' },
        voiceConfig: { type: 'object' },
        animationConfig: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '数字人更新成功',
  })
  async updateDigitalHuman(
    @Param('id') id: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.bindingService.updateDigitalHuman(id, body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '更新数字人失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除数字人' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '数字人删除成功',
  })
  async deleteDigitalHuman(@Param('id') id: string): Promise<any> {
    try {
      return await this.bindingService.deleteDigitalHuman(id);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '删除数字人失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/knowledge-bases')
  @ApiOperation({ summary: '绑定知识库到数字人' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        knowledgeBaseIds: {
          type: 'array',
          items: { type: 'string' },
          description: '知识库ID列表',
        },
        bindingConfig: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['primary', 'secondary'],
              description: '绑定类型',
            },
            priority: { type: 'number', description: '优先级' },
            searchWeight: { type: 'number', description: '搜索权重' },
            enabledFeatures: {
              type: 'array',
              items: { type: 'string' },
              description: '启用的功能',
            },
          },
        },
      },
      required: ['knowledgeBaseIds'],
    },
  })
  @ApiResponse({
    status: 201,
    description: '绑定成功',
  })
  async bindKnowledgeBases(
    @Param('id') digitalHumanId: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.bindingService.bindKnowledgeBases(digitalHumanId, body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '绑定失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/knowledge-bases')
  @ApiOperation({ summary: '获取数字人的知识库绑定' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '知识库绑定列表',
  })
  async getDigitalHumanKnowledgeBases(
    @Param('id') digitalHumanId: string,
  ): Promise<any> {
    try {
      return await this.bindingService.getDigitalHumanKnowledgeBases(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '获取绑定列表失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':dhId/knowledge-bases/:kbId')
  @ApiOperation({ summary: '解绑知识库' })
  @ApiParam({ name: 'dhId', description: '数字人ID' })
  @ApiParam({ name: 'kbId', description: '知识库ID' })
  @ApiResponse({
    status: 200,
    description: '解绑成功',
  })
  async unbindKnowledgeBase(
    @Param('dhId') digitalHumanId: string,
    @Param('kbId') knowledgeBaseId: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.bindingService.unbindKnowledgeBase(
        digitalHumanId,
        knowledgeBaseId,
        body,
      );
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '解绑失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':dhId/knowledge-bases/:kbId')
  @ApiOperation({ summary: '更新绑定配置' })
  @ApiParam({ name: 'dhId', description: '数字人ID' })
  @ApiParam({ name: 'kbId', description: '知识库ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        priority: { type: 'number' },
        isActive: { type: 'boolean' },
        config: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
  })
  async updateBindingConfig(
    @Param('dhId') digitalHumanId: string,
    @Param('kbId') knowledgeBaseId: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.bindingService.updateBindingConfig(
        digitalHumanId,
        knowledgeBaseId,
        body,
      );
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '更新失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id/knowledge-bases/reorder')
  @ApiOperation({ summary: '重新排序绑定优先级' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        bindingOrders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              bindingId: { type: 'string' },
              priority: { type: 'number' },
            },
          },
        },
      },
      required: ['bindingOrders'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '重新排序成功',
  })
  async reorderBindings(
    @Param('id') digitalHumanId: string,
    @Body() body: any,
  ): Promise<any> {
    try {
      return await this.bindingService.reorderBindings(digitalHumanId, body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || '重新排序失败',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
