/**
 * 数字人制作系统集成示例
 * 展示如何使用DL引擎的数字人制作功能
 */
import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/scene/Transform';
import {
  DigitalHumanSystem,
  DigitalHumanComponent,
  DigitalHumanSource,
  ClothingSlotType,
  AIProcessingManager,
  FaceDetectionService,
  Face3DReconstructionService,
  TextureGenerationService,
  BIPSkeletonParser,
  BIPToStandardMapping,
  DigitalHumanPackageManager,
  MinIOStorageService,
  DigitalHumanFileType
} from '../engine/src/avatar';

/**
 * 数字人制作系统示例
 */
export class DigitalHumanCreationExample {
  private world: World;
  private digitalHumanSystem: DigitalHumanSystem;
  private aiProcessingManager: AIProcessingManager;
  private storageService: MinIOStorageService;
  private packageManager: DigitalHumanPackageManager;

  constructor() {
    // 初始化世界
    this.world = new World();

    // 初始化数字人系统
    this.digitalHumanSystem = new DigitalHumanSystem(this.world, {
      debug: true,
      maxConcurrentGenerations: 2,
      autoSaveInterval: 30000
    });

    // 初始化AI处理管理器
    this.aiProcessingManager = new AIProcessingManager({
      faceDetection: { debug: true },
      face3DReconstruction: { debug: true },
      textureGeneration: { debug: true },
      enableCache: true,
      debug: true
    });

    // 初始化存储服务
    this.storageService = new MinIOStorageService({
      endpoint: 'localhost',
      port: 9000,
      useSSL: false,
      accessKey: 'minioadmin',
      secretKey: 'minioadmin',
      defaultBucket: 'digital-humans'
    });

    // 初始化包管理器
    this.packageManager = new DigitalHumanPackageManager({
      debug: true,
      enableCompression: true
    });

    // 添加系统到世界
    this.world.addSystem(this.digitalHumanSystem);
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    console.log('🚀 初始化数字人制作系统...');

    try {
      // 初始化各个服务
      await Promise.all([
        this.aiProcessingManager.initialize(),
        this.storageService.initialize()
      ]);

      console.log('✅ 数字人制作系统初始化完成');
    } catch (error) {
      console.error('❌ 系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 从照片创建数字人
   */
  public async createDigitalHumanFromPhoto(
    userId: string,
    photoFile: File,
    options: {
      name: string;
      tags?: string[];
      licenseType?: string;
    }
  ): Promise<Entity> {
    console.log('📸 开始从照片创建数字人...');

    try {
      // 1. 上传源照片到存储
      console.log('📤 上传源照片...');
      const digitalHumanId = `dh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const photoInfo = await this.storageService.uploadDigitalHumanFile(
        userId,
        digitalHumanId,
        DigitalHumanFileType.SOURCE_PHOTO,
        photoFile,
        `source_${photoFile.name}`
      );

      console.log('✅ 源照片上传完成:', photoInfo.url);

      // 2. AI处理生成3D模型和纹理
      console.log('🤖 开始AI处理...');
      const aiResult = await this.aiProcessingManager.generateDigitalHuman({
        id: `ai_req_${digitalHumanId}`,
        photoFile,
        options: {
          resolution: 1024,
          quality: 'high',
          generateExtraMaps: true
        },
        onProgress: (progress, stage) => {
          console.log(`🔄 AI处理进度: ${Math.round(progress * 100)}% - ${stage}`);
        }
      });

      console.log('✅ AI处理完成，质量评分:', aiResult.qualityScore);

      // 3. 创建数字人实体
      console.log('👤 创建数字人实体...');
      const entity = new Entity(this.world);
      entity.name = options.name;

      // 添加Transform组件
      const transform = new Transform();
      entity.addComponent(transform);

      // 添加数字人组件
      const digitalHumanComponent = new DigitalHumanComponent(entity, {
        source: DigitalHumanSource.PHOTO,
        sourcePhotoUrl: photoInfo.url,
        name: options.name,
        userId,
        tags: options.tags || [],
        licenseType: options.licenseType || 'private',
        faceGeometry: {
          mesh: new THREE.Mesh(), // 这里应该从AI结果创建实际的网格
          vertices: aiResult.faceMesh.vertices,
          normals: aiResult.faceMesh.normals,
          uvs: aiResult.faceMesh.uvs,
          landmarks: [],
          boundingBox: new THREE.Box3()
        }
      });

      entity.addComponent(digitalHumanComponent);

      // 4. 保存生成的资源
      console.log('💾 保存生成的资源...');
      
      // 保存模型文件
      const modelBuffer = this.serializeMesh(aiResult.faceMesh);
      await this.storageService.uploadDigitalHumanFile(
        userId,
        digitalHumanId,
        DigitalHumanFileType.MODEL,
        modelBuffer,
        'model.gltf'
      );

      // 保存纹理文件
      const textureBuffer = await this.serializeTexture(aiResult.faceTexture.diffuseMap);
      await this.storageService.uploadDigitalHumanFile(
        userId,
        digitalHumanId,
        DigitalHumanFileType.TEXTURE,
        textureBuffer,
        'diffuse.jpg'
      );

      // 5. 添加到世界
      this.world.addEntity(entity);

      console.log('🎉 数字人创建完成!');
      return entity;

    } catch (error) {
      console.error('❌ 数字人创建失败:', error);
      throw error;
    }
  }

  /**
   * 上传BIP骨骼文件并绑定到数字人
   */
  public async uploadAndBindBIPSkeleton(
    userId: string,
    digitalHumanId: string,
    bipFile: File
  ): Promise<void> {
    console.log('🦴 开始上传和绑定BIP骨骼...');

    try {
      // 1. 上传BIP文件
      console.log('📤 上传BIP文件...');
      const bipInfo = await this.storageService.uploadDigitalHumanFile(
        userId,
        digitalHumanId,
        DigitalHumanFileType.BIP_SKELETON,
        bipFile,
        bipFile.name
      );

      // 2. 解析BIP文件
      console.log('🔍 解析BIP骨骼结构...');
      const bipParser = new BIPSkeletonParser({ debug: true });
      const bipData = await bipParser.parseBIPFile(await bipFile.arrayBuffer());

      // 3. 映射到标准骨骼
      console.log('🔄 映射到标准骨骼结构...');
      const bipMapper = new BIPToStandardMapping({ debug: true });
      const standardSkeleton = await bipMapper.mapToStandardSkeleton(bipData);

      // 4. 保存标准骨骼数据
      const skeletonBuffer = Buffer.from(JSON.stringify(standardSkeleton));
      await this.storageService.uploadDigitalHumanFile(
        userId,
        digitalHumanId,
        DigitalHumanFileType.CONFIG,
        skeletonBuffer,
        'skeleton.json'
      );

      console.log('✅ BIP骨骼绑定完成');

    } catch (error) {
      console.error('❌ BIP骨骼绑定失败:', error);
      throw error;
    }
  }

  /**
   * 为数字人换装
   */
  public async changeClothing(
    entityId: string,
    slotType: ClothingSlotType,
    clothingFile: File
  ): Promise<void> {
    console.log('👕 开始为数字人换装...');

    try {
      // 获取数字人实体
      const entity = this.digitalHumanSystem.getDigitalHuman(entityId);
      if (!entity) {
        throw new Error('数字人不存在');
      }

      const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (!digitalHumanComponent) {
        throw new Error('数字人组件不存在');
      }

      // 上传服装文件
      const clothingId = `clothing_${Date.now()}`;
      const clothingInfo = await this.storageService.uploadDigitalHumanFile(
        digitalHumanComponent.userId!,
        entityId,
        DigitalHumanFileType.CLOTHING,
        clothingFile,
        `${clothingId}_${clothingFile.name}`
      );

      // 更新数字人服装
      digitalHumanComponent.setClothingItem(slotType, clothingId, clothingInfo.url!);

      console.log('✅ 换装完成');

    } catch (error) {
      console.error('❌ 换装失败:', error);
      throw error;
    }
  }

  /**
   * 导出数字人包
   */
  public async exportDigitalHumanPackage(
    userId: string,
    digitalHumanId: string
  ): Promise<ArrayBuffer> {
    console.log('📦 开始导出数字人包...');

    try {
      // 1. 获取数字人实体
      const entity = this.digitalHumanSystem.getDigitalHuman(digitalHumanId);
      if (!entity) {
        throw new Error('数字人不存在');
      }

      const digitalHumanComponent = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (!digitalHumanComponent) {
        throw new Error('数字人组件不存在');
      }

      // 2. 收集所有相关文件
      console.log('📋 收集数字人文件...');
      const files = await this.storageService.listDigitalHumanFiles(userId, digitalHumanId);
      
      const resources = new Map<string, ArrayBuffer>();
      for (const file of files) {
        const stream = await this.storageService.downloadFile(undefined, file.fullPath);
        const buffer = await this.streamToArrayBuffer(stream);
        resources.set(file.name, buffer);
      }

      // 3. 创建包元数据
      const metadata = {
        id: digitalHumanId,
        name: digitalHumanComponent.name,
        description: `数字人包 - ${digitalHumanComponent.name}`,
        source: digitalHumanComponent.source,
        version: digitalHumanComponent.version,
        creatorId: digitalHumanComponent.creatorId,
        licenseType: digitalHumanComponent.licenseType,
        tags: digitalHumanComponent.tags,
        personalityTraits: digitalHumanComponent.personalityTraits,
        customProperties: {}
      };

      // 4. 创建数字人包
      console.log('🔨 构建数字人包...');
      const packageBuffer = await this.packageManager.createPackage(
        metadata,
        resources,
        {
          compressionLevel: 6,
          includeSource: true,
          optimizeTextures: true,
          textureQuality: 'high',
          includeAnimations: true,
          includeExpressions: true,
          includeClothing: true,
          targetPlatform: 'all'
        }
      );

      console.log('✅ 数字人包导出完成');
      return packageBuffer;

    } catch (error) {
      console.error('❌ 数字人包导出失败:', error);
      throw error;
    }
  }

  /**
   * 导入数字人包
   */
  public async importDigitalHumanPackage(
    userId: string,
    packageFile: File
  ): Promise<Entity> {
    console.log('📥 开始导入数字人包...');

    try {
      // 1. 解析数字人包
      console.log('🔍 解析数字人包...');
      const packageBuffer = await packageFile.arrayBuffer();
      const packageContent = await this.packageManager.parsePackage(packageBuffer, {
        validateIntegrity: true,
        loadAllResources: true
      });

      // 2. 创建新的数字人ID
      const newDigitalHumanId = `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 3. 上传包中的文件到存储
      console.log('📤 上传包文件到存储...');
      // TODO: 实现从包中提取文件并上传到存储的逻辑

      // 4. 创建数字人实体
      console.log('👤 创建导入的数字人实体...');
      const entity = new Entity(this.world);
      entity.name = packageContent.metadata.name;

      const transform = new Transform();
      entity.addComponent(transform);

      const digitalHumanComponent = new DigitalHumanComponent(entity, {
        source: DigitalHumanSource.UPLOAD,
        name: packageContent.metadata.name,
        userId,
        tags: packageContent.metadata.tags,
        licenseType: packageContent.metadata.licenseType,
        personalityTraits: packageContent.metadata.personalityTraits,
        version: packageContent.metadata.version
      });

      entity.addComponent(digitalHumanComponent);
      this.world.addEntity(entity);

      console.log('✅ 数字人包导入完成');
      return entity;

    } catch (error) {
      console.error('❌ 数字人包导入失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  public async getStorageStats(userId: string): Promise<any> {
    return await this.storageService.getStorageStats(userId);
  }

  /**
   * 辅助方法：序列化网格
   */
  private serializeMesh(mesh: any): ArrayBuffer {
    // TODO: 实现实际的网格序列化
    const mockData = JSON.stringify({ vertices: Array.from(mesh.vertices) });
    return new TextEncoder().encode(mockData).buffer;
  }

  /**
   * 辅助方法：序列化纹理
   */
  private async serializeTexture(texture: THREE.Texture): Promise<ArrayBuffer> {
    // TODO: 实现实际的纹理序列化
    const mockData = new Uint8Array(1024 * 1024 * 4); // 模拟1024x1024 RGBA纹理
    return mockData.buffer;
  }

  /**
   * 辅助方法：流转换为ArrayBuffer
   */
  private async streamToArrayBuffer(stream: ReadableStream): Promise<ArrayBuffer> {
    const reader = stream.getReader();
    const chunks: Uint8Array[] = [];
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }
    } finally {
      reader.releaseLock();
    }

    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    return result.buffer;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.digitalHumanSystem.dispose();
    this.aiProcessingManager.dispose();
    this.storageService.dispose();
    this.packageManager.dispose();
    console.log('🧹 数字人制作系统已清理');
  }
}

// 使用示例
async function runExample() {
  const example = new DigitalHumanCreationExample();
  
  try {
    // 初始化系统
    await example.initialize();
    
    // 模拟用户上传照片创建数字人
    // const photoFile = new File([new ArrayBuffer(1024)], 'photo.jpg', { type: 'image/jpeg' });
    // const digitalHuman = await example.createDigitalHumanFromPhoto('user123', photoFile, {
    //   name: '我的数字人',
    //   tags: ['测试', '示例'],
    //   licenseType: 'private'
    // });
    
    console.log('🎉 数字人制作系统示例运行完成!');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  } finally {
    example.dispose();
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runExample();
}
