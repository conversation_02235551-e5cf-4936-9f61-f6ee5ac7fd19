#!/bin/bash

# RAG数字人交互系统备份脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
BACKUP_DIR="/backup/rag-system"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="rag_backup_$TIMESTAMP"
RETENTION_DAYS=30

# 数据库配置
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="digital_human_rag"
DB_USER="postgres"
DB_PASSWORD="${POSTGRES_PASSWORD}"

# MinIO配置
MINIO_ENDPOINT="localhost:9000"
MINIO_ACCESS_KEY="${MINIO_ACCESS_KEY}"
MINIO_SECRET_KEY="${MINIO_SECRET_KEY}"
MINIO_BUCKET="knowledge-bases"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD="${REDIS_PASSWORD}"

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    
    log_success "备份目录创建完成: $BACKUP_DIR/$BACKUP_NAME"
}

# 备份PostgreSQL数据库
backup_postgres() {
    log_info "备份PostgreSQL数据库..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 备份数据库结构和数据
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --clean --if-exists --create \
        > "$BACKUP_DIR/$BACKUP_NAME/postgres_dump.sql"
    
    # 压缩备份文件
    gzip "$BACKUP_DIR/$BACKUP_NAME/postgres_dump.sql"
    
    log_success "PostgreSQL备份完成"
}

# 备份Redis数据
backup_redis() {
    log_info "备份Redis数据..."
    
    # 触发Redis保存
    if [ -n "$REDIS_PASSWORD" ]; then
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" BGSAVE
    else
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" BGSAVE
    fi
    
    # 等待保存完成
    sleep 5
    
    # 复制RDB文件
    docker cp rag-redis:/data/dump.rdb "$BACKUP_DIR/$BACKUP_NAME/redis_dump.rdb"
    
    # 压缩备份文件
    gzip "$BACKUP_DIR/$BACKUP_NAME/redis_dump.rdb"
    
    log_success "Redis备份完成"
}

# 备份MinIO数据
backup_minio() {
    log_info "备份MinIO数据..."
    
    # 使用mc客户端备份
    if command -v mc &> /dev/null; then
        # 配置mc客户端
        mc alias set backup-minio "http://$MINIO_ENDPOINT" "$MINIO_ACCESS_KEY" "$MINIO_SECRET_KEY"
        
        # 镜像备份
        mc mirror backup-minio/"$MINIO_BUCKET" "$BACKUP_DIR/$BACKUP_NAME/minio_data"
        
        # 压缩备份
        tar -czf "$BACKUP_DIR/$BACKUP_NAME/minio_backup.tar.gz" -C "$BACKUP_DIR/$BACKUP_NAME" minio_data
        rm -rf "$BACKUP_DIR/$BACKUP_NAME/minio_data"
        
        log_success "MinIO备份完成"
    else
        log_warning "mc客户端未安装，跳过MinIO备份"
    fi
}

# 备份Chroma向量数据库
backup_chroma() {
    log_info "备份Chroma向量数据库..."
    
    # 复制Chroma数据目录
    docker cp rag-chroma:/chroma/chroma "$BACKUP_DIR/$BACKUP_NAME/chroma_data"
    
    # 压缩备份
    tar -czf "$BACKUP_DIR/$BACKUP_NAME/chroma_backup.tar.gz" -C "$BACKUP_DIR/$BACKUP_NAME" chroma_data
    rm -rf "$BACKUP_DIR/$BACKUP_NAME/chroma_data"
    
    log_success "Chroma备份完成"
}

# 备份配置文件
backup_configs() {
    log_info "备份配置文件..."
    
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/configs"
    
    # 备份环境配置
    cp .env.production "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    cp .env.staging "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    cp .env.development "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    
    # 备份Docker配置
    cp docker-compose.production.yml "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    cp docker-compose.staging.yml "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    
    # 备份Nginx配置
    cp -r nginx/ "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    
    # 备份监控配置
    cp -r monitoring/ "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    
    # 备份K8s配置
    cp -r k8s/ "$BACKUP_DIR/$BACKUP_NAME/configs/" 2>/dev/null || true
    
    log_success "配置文件备份完成"
}

# 创建备份元数据
create_metadata() {
    log_info "创建备份元数据..."
    
    cat > "$BACKUP_DIR/$BACKUP_NAME/metadata.json" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "timestamp": "$TIMESTAMP",
    "date": "$(date -Iseconds)",
    "version": "1.0.0",
    "components": {
        "postgres": true,
        "redis": true,
        "minio": true,
        "chroma": true,
        "configs": true
    },
    "system_info": {
        "hostname": "$(hostname)",
        "os": "$(uname -s)",
        "kernel": "$(uname -r)",
        "architecture": "$(uname -m)"
    },
    "docker_info": {
        "version": "$(docker --version 2>/dev/null || echo 'N/A')",
        "compose_version": "$(docker-compose --version 2>/dev/null || echo 'N/A')"
    }
}
EOF
    
    log_success "备份元数据创建完成"
}

# 压缩备份
compress_backup() {
    log_info "压缩备份文件..."
    
    cd "$BACKUP_DIR"
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    rm -rf "$BACKUP_NAME"
    
    # 计算校验和
    sha256sum "${BACKUP_NAME}.tar.gz" > "${BACKUP_NAME}.tar.gz.sha256"
    
    log_success "备份压缩完成: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    find "$BACKUP_DIR" -name "rag_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_DIR" -name "rag_backup_*.tar.gz.sha256" -mtime +$RETENTION_DAYS -delete
    
    log_success "旧备份清理完成"
}

# 验证备份
verify_backup() {
    log_info "验证备份完整性..."
    
    cd "$BACKUP_DIR"
    
    if sha256sum -c "${BACKUP_NAME}.tar.gz.sha256"; then
        log_success "备份完整性验证通过"
    else
        log_error "备份完整性验证失败"
        return 1
    fi
    
    # 检查备份文件大小
    backup_size=$(du -h "${BACKUP_NAME}.tar.gz" | cut -f1)
    log_info "备份文件大小: $backup_size"
    
    return 0
}

# 发送备份通知
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$BACKUP_NOTIFICATION_WEBHOOK" ]; then
        curl -X POST "$BACKUP_NOTIFICATION_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"RAG系统备份通知: $status - $message\"}" \
            2>/dev/null || true
    fi
    
    if [ -n "$BACKUP_NOTIFICATION_EMAIL" ]; then
        echo "$message" | mail -s "RAG系统备份通知: $status" "$BACKUP_NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
}

# 主函数
main() {
    log_info "开始RAG系统备份..."
    
    start_time=$(date +%s)
    
    # 检查备份目录
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
    fi
    
    # 执行备份
    create_backup_dir
    
    # 备份各个组件
    backup_postgres
    backup_redis
    backup_minio
    backup_chroma
    backup_configs
    
    # 创建元数据
    create_metadata
    
    # 压缩备份
    compress_backup
    
    # 验证备份
    if verify_backup; then
        # 清理旧备份
        cleanup_old_backups
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        log_success "备份完成! 耗时: ${duration}秒"
        send_notification "成功" "备份完成，文件: ${BACKUP_NAME}.tar.gz，耗时: ${duration}秒"
    else
        log_error "备份验证失败!"
        send_notification "失败" "备份验证失败: ${BACKUP_NAME}.tar.gz"
        exit 1
    fi
}

# 错误处理
trap 'log_error "备份过程中发生错误"; send_notification "失败" "备份过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
