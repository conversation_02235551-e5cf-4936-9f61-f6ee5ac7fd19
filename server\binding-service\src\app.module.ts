import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BindingModule } from './binding/binding.module';
import { CacheModule } from './cache/cache.service';
import productionConfig from '../../knowledge-service/src/config/production.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [productionConfig],
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService) => ({
        type: 'postgres',
        host: configService.get('production.metadataDatabase.host'),
        port: configService.get('production.metadataDatabase.port'),
        username: configService.get('production.metadataDatabase.username'),
        password: configService.get('production.metadataDatabase.password'),
        database: configService.get('production.metadataDatabase.database'),
        ssl: configService.get('production.metadataDatabase.ssl'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: process.env.NODE_ENV !== 'production',
        logging: process.env.NODE_ENV === 'development',
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
      }),
      inject: ['ConfigService'],
    }),
    CacheModule,
    BindingModule,
  ],
})
export class AppModule {}
