# 文本语音场景生成系统部署指南

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 或同等性能
- **内存**: 8GB RAM
- **显卡**: NVIDIA GTX 1060 / AMD RX 580 或同等性能
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

#### 推荐配置
- **CPU**: Intel i7-10700K / AMD Ryzen 7 3700X 或更高
- **内存**: 16GB RAM 或更多
- **显卡**: NVIDIA RTX 3070 / AMD RX 6700 XT 或更高
- **存储**: 50GB 可用空间 (SSD推荐)
- **网络**: 高速稳定的互联网连接

### 软件要求

- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 16.x 或更高版本
- **Python**: 3.8+ (用于AI模型)
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

## 安装步骤

### 1. 环境准备

```bash
# 安装 Node.js 依赖
npm install

# 安装 Python 依赖
pip install -r requirements.txt

# 安装 AI 模型依赖
pip install torch torchvision torchaudio
pip install transformers
pip install sentence-transformers
```

### 2. 配置文件设置

创建配置文件 `config/production.json`:

```json
{
  "ai": {
    "sceneGeneration": {
      "debug": false,
      "enableRealTimeGeneration": true,
      "qualityLevel": "balanced",
      "sceneUnderstanding": {
        "modelType": "transformer",
        "language": "zh-CN",
        "confidenceThreshold": 0.7
      },
      "layoutGeneration": {
        "algorithm": "constraint_satisfaction",
        "maxIterations": 1000,
        "convergenceThreshold": 0.01
      },
      "assetMatching": {
        "embeddingModel": "sentence-transformers",
        "similarityThreshold": 0.7,
        "maxResults": 10
      }
    },
    "speech": {
      "recognition": {
        "language": "zh-CN",
        "continuous": true,
        "interimResults": true
      },
      "synthesis": {
        "language": "zh-CN",
        "voice": "female",
        "enableEmotionalSynthesis": true
      }
    }
  },
  "performance": {
    "targetFPS": 60,
    "maxPolygons": 100000,
    "maxTextureSize": 1024,
    "enableLOD": true,
    "enableGeometryMerging": true,
    "enableTextureCompression": true,
    "enableInstancing": true
  },
  "storage": {
    "assetPath": "./assets",
    "cachePath": "./cache",
    "tempPath": "./temp"
  },
  "server": {
    "port": 3000,
    "host": "localhost",
    "cors": {
      "origin": "*",
      "credentials": true
    }
  }
}
```

### 3. 数据库配置

如果使用数据库存储场景和资产信息：

```bash
# 安装数据库依赖
npm install mongodb mongoose

# 或者使用 PostgreSQL
npm install pg sequelize
```

配置数据库连接：

```json
{
  "database": {
    "type": "mongodb",
    "url": "mongodb://localhost:27017/scene_generation",
    "options": {
      "useNewUrlParser": true,
      "useUnifiedTopology": true
    }
  }
}
```

### 4. 资产库配置

设置3D资产库：

```bash
# 创建资产目录结构
mkdir -p assets/{models,textures,materials,environments}
mkdir -p assets/models/{furniture,lighting,electronics,decoration}

# 下载基础资产包
wget https://example.com/basic-assets.zip
unzip basic-assets.zip -d assets/
```

### 5. AI模型下载

```bash
# 下载预训练模型
python scripts/download_models.py

# 或手动下载
mkdir -p models/scene_understanding
mkdir -p models/layout_generation
mkdir -p models/asset_matching

# 下载具体模型文件...
```

## 部署配置

### 1. 开发环境部署

```bash
# 启动开发服务器
npm run dev

# 启动AI服务
python ai_server.py --mode development

# 启动编辑器
npm run start:editor
```

### 2. 生产环境部署

#### 使用 Docker

创建 `Dockerfile`:

```dockerfile
FROM node:16-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY requirements.txt ./

# 安装依赖
RUN npm ci --only=production
RUN apk add --no-cache python3 py3-pip
RUN pip3 install -r requirements.txt

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "start"]
```

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  scene-generation:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - AI_MODEL_PATH=/app/models
    volumes:
      - ./assets:/app/assets
      - ./models:/app/models
      - ./cache:/app/cache
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  redis_data:
```

部署命令：

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f scene-generation
```

#### 使用 PM2

```bash
# 安装 PM2
npm install -g pm2

# 创建 PM2 配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'scene-generation-api',
      script: 'dist/server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'ai-service',
      script: 'ai_server.py',
      interpreter: 'python3',
      instances: 1,
      env: {
        PYTHONPATH: '.',
        AI_MODE: 'production'
      }
    }
  ]
};
EOF

# 启动服务
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save
pm2 startup
```

### 3. 负载均衡配置

使用 Nginx 作为反向代理：

```nginx
upstream scene_generation {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    listen 80;
    server_name your-domain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # 静态资源
    location /assets/ {
        alias /app/assets/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API 请求
    location /api/ {
        proxy_pass http://scene_generation;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket 支持
    location /ws/ {
        proxy_pass http://scene_generation;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 前端应用
    location / {
        try_files $uri $uri/ /index.html;
        root /app/dist;
    }
}
```

## 监控和日志

### 1. 应用监控

使用 Prometheus 和 Grafana：

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'scene-generation'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
```

### 2. 日志配置

```javascript
// logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'scene-generation' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
};
```

### 3. 健康检查

```javascript
// health.js
app.get('/health', (req, res) => {
  const health = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    services: {
      database: checkDatabase(),
      ai_models: checkAIModels(),
      storage: checkStorage()
    }
  };
  
  res.json(health);
});
```

## 性能优化

### 1. 缓存策略

```javascript
// cache.js
const Redis = require('redis');
const client = Redis.createClient();

// 场景生成结果缓存
const cacheSceneResult = async (key, result, ttl = 3600) => {
  await client.setex(key, ttl, JSON.stringify(result));
};

// 资产匹配缓存
const cacheAssetMatch = async (query, assets, ttl = 7200) => {
  const key = `asset_match:${hashQuery(query)}`;
  await client.setex(key, ttl, JSON.stringify(assets));
};
```

### 2. 数据库优化

```javascript
// 索引优化
db.scenes.createIndex({ "description": "text" });
db.assets.createIndex({ "category": 1, "tags": 1 });
db.generations.createIndex({ "timestamp": -1 });

// 查询优化
const findSimilarScenes = async (description) => {
  return await db.scenes.find({
    $text: { $search: description }
  }).limit(10);
};
```

### 3. AI模型优化

```python
# model_optimization.py
import torch
from torch.quantization import quantize_dynamic

# 模型量化
def optimize_model(model):
    quantized_model = quantize_dynamic(
        model, 
        {torch.nn.Linear}, 
        dtype=torch.qint8
    )
    return quantized_model

# 批处理优化
def batch_inference(model, inputs, batch_size=32):
    results = []
    for i in range(0, len(inputs), batch_size):
        batch = inputs[i:i+batch_size]
        with torch.no_grad():
            batch_results = model(batch)
        results.extend(batch_results)
    return results
```

## 安全配置

### 1. API 安全

```javascript
// security.js
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 100次请求
  message: '请求过于频繁，请稍后再试'
});

app.use(helmet());
app.use('/api/', limiter);

// API 密钥验证
const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  if (!apiKey || !isValidApiKey(apiKey)) {
    return res.status(401).json({ error: '无效的API密钥' });
  }
  next();
};
```

### 2. 数据加密

```javascript
// encryption.js
const crypto = require('crypto');

const encrypt = (text, key) => {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

const decrypt = (encryptedText, key) => {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};
```

## 故障排除

### 常见问题

1. **AI模型加载失败**
   ```bash
   # 检查模型文件
   ls -la models/
   
   # 检查Python环境
   python -c "import torch; print(torch.__version__)"
   
   # 重新下载模型
   python scripts/download_models.py --force
   ```

2. **内存不足**
   ```bash
   # 监控内存使用
   free -h
   
   # 调整模型配置
   # 在配置文件中设置较小的batch_size
   ```

3. **网络连接问题**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   
   # 检查防火墙
   ufw status
   ```

### 日志分析

```bash
# 查看应用日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log

# 查看系统日志
journalctl -u scene-generation -f
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/scene_generation_$DATE"

mkdir -p $BACKUP_DIR

# 备份数据库
mongodump --out $BACKUP_DIR/mongodb

# 备份资产文件
tar -czf $BACKUP_DIR/assets.tar.gz assets/

# 备份配置文件
cp -r config/ $BACKUP_DIR/

# 备份模型文件
tar -czf $BACKUP_DIR/models.tar.gz models/

echo "备份完成: $BACKUP_DIR"
```

### 2. 数据恢复

```bash
#!/bin/bash
# restore.sh

BACKUP_DIR=$1

if [ -z "$BACKUP_DIR" ]; then
    echo "请指定备份目录"
    exit 1
fi

# 恢复数据库
mongorestore $BACKUP_DIR/mongodb

# 恢复资产文件
tar -xzf $BACKUP_DIR/assets.tar.gz

# 恢复配置文件
cp -r $BACKUP_DIR/config/ .

# 恢复模型文件
tar -xzf $BACKUP_DIR/models.tar.gz

echo "恢复完成"
```

## 更新和维护

### 1. 版本更新

```bash
# 更新代码
git pull origin main

# 更新依赖
npm update
pip install -r requirements.txt --upgrade

# 重新构建
npm run build

# 重启服务
pm2 restart all
```

### 2. 模型更新

```bash
# 下载新模型
python scripts/download_models.py --version latest

# 测试新模型
python scripts/test_models.py

# 部署新模型
python scripts/deploy_models.py
```

这个部署指南提供了完整的生产环境部署方案，包括系统要求、安装步骤、配置优化、监控日志、安全设置和故障排除等各个方面。
