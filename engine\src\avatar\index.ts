/**
 * 头像系统
 * 导出所有头像相关的组件和系统
 */

// 组件
export { FacialAnimationComponent, FacialExpressionType, VisemeType } from './components/FacialAnimationComponent';
export { LipSyncComponent } from './components/LipSyncComponent';
export { AIAnimationSynthesisComponent } from './components/AIAnimationSynthesisComponent';
export { FacialAnimationEditorComponent, EditorState } from './components/FacialAnimationEditorComponent';
export {
  DigitalHumanComponent,
  DigitalHumanComponentConfig,
  DigitalHumanSource,
  FaceGeometry,
  BodyMorphTargets,
  ClothingSlot,
  ClothingSlots,
  ClothingSlotType,
  PersonalityTraits
} from './components/DigitalHumanComponent';

// 系统
export { FacialAnimationSystem } from './systems/FacialAnimationSystem';
export { FacialAnimationModelAdapterSystem } from './systems/FacialAnimationModelAdapterSystem';
export { LipSyncSystem } from './systems/LipSyncSystem';
export { AIAnimationSynthesisSystem } from './systems/AIAnimationSynthesisSystem';
export { FacialAnimationEditorSystem } from './systems/FacialAnimationEditorSystem';
export {
  DigitalHumanSystem,
  DigitalHumanSystemConfig,
  DigitalHumanGenerationRequest
} from './systems/DigitalHumanSystem';

// 适配器
export { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from './adapters/FacialAnimationModelAdapter';

// 动画
export { FacialAnimationClip } from './animation/FacialAnimationClip';
export type { ExpressionKeyframe, VisemeKeyframe } from './animation/FacialAnimationClip';

// 多动作融合系统
export * from './animation/MultiActionFusionTypes';
export { MultiActionFusionManager } from './animation/MultiActionFusionManager';
export { ActionConflictResolver } from './animation/ActionConflictResolver';
export { AnimationStateMachine } from './animation/AnimationStateMachine';
export { AnimationRetargeter } from './animation/AnimationRetargeter';

// BIP集成系统
export { BIPIntegrationSystem } from './systems/BIPIntegrationSystem';

// 数字人导入系统
export { DigitalHumanImportSystem, SupportedFileFormat } from './systems/DigitalHumanImportSystem';

// 数字人文件格式
export * from './formats/DigitalHumanFormat';

// 数字人转换器
export { DigitalHumanConverter } from './converters/DigitalHumanConverter';

// 照片到3D转换管道
export { PhotoTo3DPipeline } from './generation/PhotoTo3DPipeline';

// AI
export { EmotionBasedAnimationGenerator } from './ai/EmotionBasedAnimationGenerator';
export type { EmotionAnalysisResult } from './ai/EmotionBasedAnimationGenerator';
export { AIModel } from './ai/AIModel';
export type { AnimationGenerationRequest, AnimationGenerationResult } from './ai/AnimationGenerationTypes';
export { EmotionType } from './ai/AnimationGenerationTypes';

// AI处理服务
export {
  FaceDetectionService,
  FaceDetectionConfig,
  FaceDetectionResult,
  FaceInfo,
  FaceLandmark,
  LandmarkType,
  Face3DReconstructionService,
  Face3DReconstructionConfig,
  ProcessedPhoto,
  FaceFeatures,
  FaceMesh,
  TextureGenerationService,
  TextureGenerationConfig,
  FaceTexture,
  UVMapping,
  AIProcessingManager,
  AIProcessingManagerConfig,
  DigitalHumanGenerationRequest as AIDigitalHumanGenerationRequest,
  DigitalHumanGenerationResult
} from './ai';

// BIP骨骼支持
export {
  BIPSkeletonParser,
  BIPBone,
  BIPBoneType,
  BIPSkeletonData,
  BIPBoneHierarchy,
  BIPBoneNode,
  BIPParserConfig,
  DOFLimits,
  BIPToStandardMapping,
  StandardBoneType,
  StandardBone,
  StandardSkeletonData,
  StandardBoneHierarchy,
  StandardBoneNode,
  BoneMappingRule,
  MappingConfig
} from './bip';

// 数字人文件格式
export {
  DIGITAL_HUMAN_PACKAGE_VERSION,
  DIGITAL_HUMAN_PACKAGE_EXTENSION,
  DIGITAL_HUMAN_PACKAGE_MIME_TYPE,
  DigitalHumanPackageHeader,
  DigitalHumanMetadata,
  GeometryInfo,
  TextureInfo,
  SkeletonInfo,
  AnimationInfo,
  ClothingInfo,
  ExpressionInfo,
  DigitalHumanPackageContent,
  PackageManifest,
  PackageFile,
  PackageValidationResult,
  PackageBuildOptions,
  PackageParseOptions,
  PACKAGE_CONSTANTS,
  DigitalHumanPackageManager,
  PackageManagerConfig
} from './formats';

// 高级换装系统
export {
  ClothingSystem,
  ClothingSystemConfig,
  ClothingCategory,
  ClothingMaterialType,
  ClothingPhysics,
  ClothingFittingParams,
  ClothingItem,
  FittedClothing,
  ClothingOutfit
} from './clothing/ClothingSystem';
