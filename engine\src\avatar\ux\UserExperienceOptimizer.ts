import { Logger } from '../../../core/Logger';
import { EventEmitter } from 'events';

/**
 * 操作历史记录
 */
interface OperationRecord {
  id: string;
  type: string;
  timestamp: number;
  data: any;
  undoData?: any;
  description: string;
}

/**
 * 预设配置
 */
interface PresetConfig {
  id: string;
  name: string;
  description: string;
  category: string;
  data: any;
  thumbnail?: string;
  tags: string[];
  createdAt: number;
  usageCount: number;
}

/**
 * 实时预览配置
 */
interface PreviewConfig {
  enabled: boolean;
  quality: 'low' | 'medium' | 'high';
  updateInterval: number; // ms
  maxPreviewTime: number; // ms
  enablePhysics: boolean;
  enableAnimations: boolean;
}

/**
 * 用户偏好设置
 */
interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  autoSave: boolean;
  autoSaveInterval: number; // seconds
  showTooltips: boolean;
  enableHotkeys: boolean;
  previewQuality: 'low' | 'medium' | 'high';
  enableSounds: boolean;
  panelLayout: 'default' | 'compact' | 'custom';
  customLayout?: any;
}

/**
 * 用户体验优化器
 * 实现M4阶段的用户体验优化功能
 */
export class UserExperienceOptimizer extends EventEmitter {
  private logger: Logger;
  private operationHistory: OperationRecord[] = [];
  private currentHistoryIndex: number = -1;
  private maxHistorySize: number = 100;
  
  private presets: Map<string, PresetConfig> = new Map();
  private userPreferences: UserPreferences;
  private previewConfig: PreviewConfig;
  
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private previewUpdateTimer: NodeJS.Timeout | null = null;
  
  // 性能监控
  private operationTimes: Map<string, number[]> = new Map();
  private userInteractionMetrics = {
    totalOperations: 0,
    averageOperationTime: 0,
    mostUsedOperations: new Map<string, number>(),
    errorCount: 0,
    undoCount: 0,
    redoCount: 0
  };

  constructor() {
    super();
    this.logger = new Logger('UserExperienceOptimizer');
    
    this.userPreferences = this.getDefaultPreferences();
    this.previewConfig = this.getDefaultPreviewConfig();
    
    this.initializePresets();
    this.startAutoSave();
    this.startPreviewUpdates();
  }

  /**
   * 获取默认用户偏好
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      theme: 'light',
      language: 'zh-CN',
      autoSave: true,
      autoSaveInterval: 30,
      showTooltips: true,
      enableHotkeys: true,
      previewQuality: 'medium',
      enableSounds: true,
      panelLayout: 'default'
    };
  }

  /**
   * 获取默认预览配置
   */
  private getDefaultPreviewConfig(): PreviewConfig {
    return {
      enabled: true,
      quality: 'medium',
      updateInterval: 100,
      maxPreviewTime: 5000,
      enablePhysics: false,
      enableAnimations: true
    };
  }

  /**
   * 初始化预设
   */
  private initializePresets(): void {
    // 添加一些默认预设
    this.addPreset({
      id: 'basic-human',
      name: '基础人形',
      description: '标准的人形数字人配置',
      category: 'character',
      data: {
        height: 170,
        bodyType: 'average',
        skinTone: 'medium'
      },
      tags: ['human', 'basic', 'default'],
      createdAt: Date.now(),
      usageCount: 0
    });

    this.addPreset({
      id: 'cartoon-style',
      name: '卡通风格',
      description: '卡通化的数字人风格',
      category: 'style',
      data: {
        faceStyle: 'cartoon',
        eyeSize: 1.2,
        headSize: 1.1
      },
      tags: ['cartoon', 'stylized', 'cute'],
      createdAt: Date.now(),
      usageCount: 0
    });
  }

  /**
   * 记录操作
   */
  public recordOperation(type: string, data: any, undoData?: any, description?: string): string {
    const operationId = this.generateOperationId();
    const startTime = Date.now();

    const record: OperationRecord = {
      id: operationId,
      type,
      timestamp: startTime,
      data,
      undoData,
      description: description || `执行 ${type} 操作`
    };

    // 如果当前不在历史末尾，删除后续历史
    if (this.currentHistoryIndex < this.operationHistory.length - 1) {
      this.operationHistory = this.operationHistory.slice(0, this.currentHistoryIndex + 1);
    }

    // 添加新操作
    this.operationHistory.push(record);
    this.currentHistoryIndex++;

    // 限制历史大小
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift();
      this.currentHistoryIndex--;
    }

    // 更新统计信息
    this.updateOperationMetrics(type, Date.now() - startTime);

    this.emit('operationRecorded', { operationId, type, description });
    return operationId;
  }

  /**
   * 撤销操作
   */
  public async undo(): Promise<boolean> {
    if (this.currentHistoryIndex < 0) {
      this.logger.warn('没有可撤销的操作');
      return false;
    }

    const operation = this.operationHistory[this.currentHistoryIndex];
    
    try {
      if (operation.undoData) {
        // 执行撤销操作
        await this.executeUndoOperation(operation);
        this.currentHistoryIndex--;
        
        this.userInteractionMetrics.undoCount++;
        this.emit('operationUndone', { operationId: operation.id, type: operation.type });
        
        this.logger.info(`撤销操作成功: ${operation.description}`);
        return true;
      } else {
        this.logger.warn(`操作 ${operation.type} 不支持撤销`);
        return false;
      }
    } catch (error) {
      this.logger.error('撤销操作失败:', error);
      this.userInteractionMetrics.errorCount++;
      return false;
    }
  }

  /**
   * 重做操作
   */
  public async redo(): Promise<boolean> {
    if (this.currentHistoryIndex >= this.operationHistory.length - 1) {
      this.logger.warn('没有可重做的操作');
      return false;
    }

    const operation = this.operationHistory[this.currentHistoryIndex + 1];
    
    try {
      // 执行重做操作
      await this.executeRedoOperation(operation);
      this.currentHistoryIndex++;
      
      this.userInteractionMetrics.redoCount++;
      this.emit('operationRedone', { operationId: operation.id, type: operation.type });
      
      this.logger.info(`重做操作成功: ${operation.description}`);
      return true;
    } catch (error) {
      this.logger.error('重做操作失败:', error);
      this.userInteractionMetrics.errorCount++;
      return false;
    }
  }

  /**
   * 获取操作历史
   */
  public getOperationHistory(): OperationRecord[] {
    return [...this.operationHistory];
  }

  /**
   * 清空操作历史
   */
  public clearHistory(): void {
    this.operationHistory = [];
    this.currentHistoryIndex = -1;
    this.emit('historyCleaned');
  }

  /**
   * 添加预设
   */
  public addPreset(preset: PresetConfig): void {
    this.presets.set(preset.id, preset);
    this.emit('presetAdded', { presetId: preset.id, name: preset.name });
  }

  /**
   * 应用预设
   */
  public async applyPreset(presetId: string): Promise<boolean> {
    const preset = this.presets.get(presetId);
    if (!preset) {
      this.logger.warn(`预设不存在: ${presetId}`);
      return false;
    }

    try {
      // 记录当前状态用于撤销
      const currentState = await this.getCurrentState();
      
      // 应用预设
      await this.applyPresetData(preset.data);
      
      // 记录操作
      this.recordOperation('apply_preset', preset.data, currentState, `应用预设: ${preset.name}`);
      
      // 更新使用计数
      preset.usageCount++;
      
      this.emit('presetApplied', { presetId, name: preset.name });
      this.logger.info(`预设应用成功: ${preset.name}`);
      
      return true;
    } catch (error) {
      this.logger.error('预设应用失败:', error);
      return false;
    }
  }

  /**
   * 获取预设列表
   */
  public getPresets(category?: string): PresetConfig[] {
    const presets = Array.from(this.presets.values());
    
    if (category) {
      return presets.filter(preset => preset.category === category);
    }
    
    return presets.sort((a, b) => b.usageCount - a.usageCount);
  }

  /**
   * 删除预设
   */
  public deletePreset(presetId: string): boolean {
    if (this.presets.has(presetId)) {
      this.presets.delete(presetId);
      this.emit('presetDeleted', { presetId });
      return true;
    }
    return false;
  }

  /**
   * 更新用户偏好
   */
  public updatePreferences(preferences: Partial<UserPreferences>): void {
    const oldPreferences = { ...this.userPreferences };
    this.userPreferences = { ...this.userPreferences, ...preferences };
    
    // 应用偏好变更
    this.applyPreferenceChanges(oldPreferences, this.userPreferences);
    
    this.emit('preferencesUpdated', { 
      oldPreferences, 
      newPreferences: this.userPreferences 
    });
  }

  /**
   * 获取用户偏好
   */
  public getPreferences(): UserPreferences {
    return { ...this.userPreferences };
  }

  /**
   * 更新预览配置
   */
  public updatePreviewConfig(config: Partial<PreviewConfig>): void {
    this.previewConfig = { ...this.previewConfig, ...config };
    
    if (config.enabled !== undefined) {
      if (config.enabled) {
        this.startPreviewUpdates();
      } else {
        this.stopPreviewUpdates();
      }
    }
    
    if (config.updateInterval !== undefined) {
      this.restartPreviewUpdates();
    }
    
    this.emit('previewConfigUpdated', { config: this.previewConfig });
  }

  /**
   * 获取预览配置
   */
  public getPreviewConfig(): PreviewConfig {
    return { ...this.previewConfig };
  }

  /**
   * 启动实时预览
   */
  public startRealTimePreview(): void {
    this.previewConfig.enabled = true;
    this.startPreviewUpdates();
    this.emit('previewStarted');
  }

  /**
   * 停止实时预览
   */
  public stopRealTimePreview(): void {
    this.previewConfig.enabled = false;
    this.stopPreviewUpdates();
    this.emit('previewStopped');
  }

  /**
   * 获取用户交互指标
   */
  public getUserMetrics(): any {
    return {
      ...this.userInteractionMetrics,
      averageOperationTime: this.calculateAverageOperationTime(),
      mostUsedOperations: Array.from(this.userInteractionMetrics.mostUsedOperations.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
    };
  }

  /**
   * 获取优化建议
   */
  public getOptimizationSuggestions(): string[] {
    const suggestions: string[] = [];
    
    // 基于用户行为分析提供建议
    if (this.userInteractionMetrics.undoCount > this.userInteractionMetrics.totalOperations * 0.3) {
      suggestions.push('考虑启用更频繁的自动保存以减少撤销操作');
    }
    
    if (this.userInteractionMetrics.errorCount > 10) {
      suggestions.push('建议查看帮助文档或教程以减少操作错误');
    }
    
    const avgTime = this.calculateAverageOperationTime();
    if (avgTime > 1000) {
      suggestions.push('考虑降低预览质量以提升操作响应速度');
    }
    
    return suggestions;
  }

  // 私有方法实现...
  
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateOperationMetrics(type: string, duration: number): void {
    this.userInteractionMetrics.totalOperations++;
    
    // 记录操作时间
    if (!this.operationTimes.has(type)) {
      this.operationTimes.set(type, []);
    }
    this.operationTimes.get(type)!.push(duration);
    
    // 更新使用计数
    const currentCount = this.userInteractionMetrics.mostUsedOperations.get(type) || 0;
    this.userInteractionMetrics.mostUsedOperations.set(type, currentCount + 1);
  }

  private calculateAverageOperationTime(): number {
    let totalTime = 0;
    let totalCount = 0;
    
    for (const times of this.operationTimes.values()) {
      totalTime += times.reduce((sum, time) => sum + time, 0);
      totalCount += times.length;
    }
    
    return totalCount > 0 ? totalTime / totalCount : 0;
  }

  private async executeUndoOperation(operation: OperationRecord): Promise<void> {
    // TODO: 实现具体的撤销逻辑
    this.logger.info(`执行撤销操作: ${operation.type}`);
  }

  private async executeRedoOperation(operation: OperationRecord): Promise<void> {
    // TODO: 实现具体的重做逻辑
    this.logger.info(`执行重做操作: ${operation.type}`);
  }

  private async getCurrentState(): Promise<any> {
    // TODO: 获取当前系统状态
    return {};
  }

  private async applyPresetData(data: any): Promise<void> {
    // TODO: 应用预设数据
    this.logger.info('应用预设数据');
  }

  private applyPreferenceChanges(oldPrefs: UserPreferences, newPrefs: UserPreferences): void {
    // 应用自动保存设置变更
    if (oldPrefs.autoSave !== newPrefs.autoSave || oldPrefs.autoSaveInterval !== newPrefs.autoSaveInterval) {
      this.restartAutoSave();
    }
    
    // 应用主题变更
    if (oldPrefs.theme !== newPrefs.theme) {
      this.emit('themeChanged', { theme: newPrefs.theme });
    }
    
    // 应用语言变更
    if (oldPrefs.language !== newPrefs.language) {
      this.emit('languageChanged', { language: newPrefs.language });
    }
  }

  private startAutoSave(): void {
    if (this.userPreferences.autoSave) {
      this.autoSaveTimer = setInterval(() => {
        this.emit('autoSave');
      }, this.userPreferences.autoSaveInterval * 1000);
    }
  }

  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  private restartAutoSave(): void {
    this.stopAutoSave();
    this.startAutoSave();
  }

  private startPreviewUpdates(): void {
    if (this.previewConfig.enabled && !this.previewUpdateTimer) {
      this.previewUpdateTimer = setInterval(() => {
        this.emit('previewUpdate');
      }, this.previewConfig.updateInterval);
    }
  }

  private stopPreviewUpdates(): void {
    if (this.previewUpdateTimer) {
      clearInterval(this.previewUpdateTimer);
      this.previewUpdateTimer = null;
    }
  }

  private restartPreviewUpdates(): void {
    this.stopPreviewUpdates();
    this.startPreviewUpdates();
  }

  /**
   * 销毁优化器
   */
  public dispose(): void {
    this.stopAutoSave();
    this.stopPreviewUpdates();
    this.removeAllListeners();
    this.logger.info('用户体验优化器已销毁');
  }
}
