const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 测试配置
const config = {
  apiGateway: 'http://localhost:8080',
  timeout: 30000,
  testUser: {
    username: '<EMAIL>',
    password: 'test123456'
  }
};

// 全局变量
let authToken = '';
let knowledgeBaseId = '';
let digitalHumanId = '';
let documentId = '';

describe('RAG数字人交互系统集成测试', () => {
  
  beforeAll(async () => {
    // 等待系统启动
    await waitForSystem();
  });

  describe('1. 系统健康检查', () => {
    test('API网关健康检查', async () => {
      const response = await axios.get(`${config.apiGateway}/health`);
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('ok');
    });

    test('各服务健康检查', async () => {
      const services = [
        '/api/knowledge-bases/health',
        '/api/digital-humans/health',
        '/api/rag/health'
      ];

      for (const service of services) {
        const response = await axios.get(`${config.apiGateway}${service}`);
        expect(response.status).toBe(200);
      }
    });
  });

  describe('2. 用户认证', () => {
    test('用户登录获取Token', async () => {
      const response = await axios.post(`${config.apiGateway}/api/auth/login`, {
        username: config.testUser.username,
        password: config.testUser.password
      });

      expect(response.status).toBe(200);
      expect(response.data.access_token).toBeDefined();
      authToken = response.data.access_token;
    });
  });

  describe('3. 知识库管理', () => {
    test('创建知识库', async () => {
      const response = await axios.post(
        `${config.apiGateway}/api/knowledge-bases`,
        {
          name: '测试知识库',
          description: '用于集成测试的知识库',
          category: 'test',
          language: 'zh-CN'
        },
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(201);
      expect(response.data.id).toBeDefined();
      knowledgeBaseId = response.data.id;
    });

    test('获取知识库列表', async () => {
      const response = await axios.get(
        `${config.apiGateway}/api/knowledge-bases`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);
    });

    test('获取知识库详情', async () => {
      const response = await axios.get(
        `${config.apiGateway}/api/knowledge-bases/${knowledgeBaseId}`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.id).toBe(knowledgeBaseId);
      expect(response.data.name).toBe('测试知识库');
    });
  });

  describe('4. 文档上传和处理', () => {
    test('上传文档到知识库', async () => {
      // 创建测试文档
      const testContent = `
        # 人工智能简介
        
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        
        ## 主要应用领域
        
        1. 机器学习
        2. 自然语言处理
        3. 计算机视觉
        4. 专家系统
        
        ## 发展历程
        
        人工智能的发展可以追溯到20世纪50年代，经历了多次起伏。
        近年来，随着深度学习技术的突破，人工智能迎来了新的发展高潮。
      `;

      const testFilePath = path.join(__dirname, 'test-document.txt');
      fs.writeFileSync(testFilePath, testContent);

      const formData = new FormData();
      formData.append('file', fs.createReadStream(testFilePath));
      formData.append('title', '人工智能简介');
      formData.append('category', 'AI');
      formData.append('description', '人工智能基础知识介绍');

      const response = await axios.post(
        `${config.apiGateway}/api/knowledge-bases/${knowledgeBaseId}/documents`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            ...formData.getHeaders()
          },
          timeout: config.timeout
        }
      );

      expect(response.status).toBe(201);
      expect(response.data.id).toBeDefined();
      documentId = response.data.id;

      // 清理测试文件
      fs.unlinkSync(testFilePath);
    });

    test('等待文档处理完成', async () => {
      let processed = false;
      let attempts = 0;
      const maxAttempts = 30;

      while (!processed && attempts < maxAttempts) {
        const response = await axios.get(
          `${config.apiGateway}/api/knowledge-bases/${knowledgeBaseId}/documents`,
          {
            headers: { Authorization: `Bearer ${authToken}` }
          }
        );

        const document = response.data.data.find(doc => doc.id === documentId);
        if (document && document.processingStatus === 'completed') {
          processed = true;
        } else {
          await new Promise(resolve => setTimeout(resolve, 2000));
          attempts++;
        }
      }

      expect(processed).toBe(true);
    });
  });

  describe('5. 数字人管理', () => {
    test('创建数字人', async () => {
      const response = await axios.post(
        `${config.apiGateway}/api/digital-humans`,
        {
          name: '测试数字人',
          description: '用于集成测试的数字人',
          modelConfig: {
            voice: 'female',
            language: 'zh-CN'
          }
        },
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(201);
      expect(response.data.id).toBeDefined();
      digitalHumanId = response.data.id;
    });

    test('绑定知识库到数字人', async () => {
      const response = await axios.post(
        `${config.apiGateway}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
        {
          knowledgeBaseIds: [knowledgeBaseId],
          bindingConfig: {
            type: 'primary',
            priority: 1,
            searchWeight: 1.0
          }
        },
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(201);
      expect(response.data.bindingIds).toBeDefined();
      expect(response.data.bindingIds.length).toBe(1);
    });

    test('获取数字人绑定的知识库', async () => {
      const response = await axios.get(
        `${config.apiGateway}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBe(1);
      expect(response.data[0].knowledgeBase.id).toBe(knowledgeBaseId);
    });
  });

  describe('6. RAG查询测试', () => {
    test('执行RAG查询', async () => {
      const response = await axios.post(
        `${config.apiGateway}/api/rag/query`,
        {
          question: '什么是人工智能？',
          digitalHumanId: digitalHumanId,
          maxResults: 5,
          temperature: 0.7,
          language: 'zh-CN'
        },
        {
          headers: { Authorization: `Bearer ${authToken}` },
          timeout: config.timeout
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.answer).toBeDefined();
      expect(response.data.sources).toBeDefined();
      expect(Array.isArray(response.data.sources)).toBe(true);
      expect(response.data.confidence).toBeGreaterThan(0);
      expect(response.data.sessionId).toBeDefined();
    });

    test('向量搜索测试', async () => {
      const response = await axios.post(
        `${config.apiGateway}/api/rag/search`,
        {
          query: '机器学习',
          digitalHumanId: digitalHumanId,
          maxResults: 3
        },
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.results).toBeDefined();
      expect(Array.isArray(response.data.results)).toBe(true);
      expect(response.data.searchTime).toBeGreaterThan(0);
    });
  });

  describe('7. 清理测试数据', () => {
    test('删除文档', async () => {
      const response = await axios.delete(
        `${config.apiGateway}/api/knowledge-bases/${knowledgeBaseId}/documents/${documentId}`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
    });

    test('解绑知识库', async () => {
      const response = await axios.delete(
        `${config.apiGateway}/api/digital-humans/${digitalHumanId}/knowledge-bases/${knowledgeBaseId}`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
    });

    test('删除数字人', async () => {
      const response = await axios.delete(
        `${config.apiGateway}/api/digital-humans/${digitalHumanId}`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
    });

    test('删除知识库', async () => {
      const response = await axios.delete(
        `${config.apiGateway}/api/knowledge-bases/${knowledgeBaseId}`,
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(response.status).toBe(200);
    });
  });
});

// 辅助函数
async function waitForSystem() {
  console.log('等待系统启动...');
  let ready = false;
  let attempts = 0;
  const maxAttempts = 60;

  while (!ready && attempts < maxAttempts) {
    try {
      const response = await axios.get(`${config.apiGateway}/health`, {
        timeout: 5000
      });
      if (response.status === 200) {
        ready = true;
        console.log('系统已就绪');
      }
    } catch (error) {
      attempts++;
      console.log(`等待系统启动... (${attempts}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  if (!ready) {
    throw new Error('系统启动超时');
  }
}
