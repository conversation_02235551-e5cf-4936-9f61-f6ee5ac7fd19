# RAG数字人交互系统部署指南

本指南详细介绍了如何在不同环境中部署RAG数字人交互系统。

## 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [Docker部署](#docker部署)
4. [Kubernetes部署](#kubernetes部署)
5. [生产环境配置](#生产环境配置)
6. [监控和日志](#监控和日志)
7. [故障排除](#故障排除)

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 8核心 3.0GHz
- **内存**: 16GB RAM
- **存储**: 200GB NVMe SSD
- **网络**: 1Gbps

#### 生产环境配置
- **CPU**: 16核心 3.0GHz+
- **内存**: 32GB RAM+
- **存储**: 500GB NVMe SSD+
- **网络**: 10Gbps

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Kubernetes**: 1.24+ (可选)
- **Nginx**: 1.18+ (生产环境)

## 环境准备

### 1. 安装Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 安装Docker Compose

```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
echo "net.core.somaxconn=65535" >> /etc/sysctl.conf
sysctl -p
```

## Docker部署

### 1. 克隆项目

```bash
git clone https://github.com/your-org/rag-digital-human.git
cd rag-digital-human
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.production.example .env.production

# 编辑配置文件
vim .env.production
```

**重要配置项**:

```bash
# 数据库密码
POSTGRES_PASSWORD=your_secure_password

# Redis密码
REDIS_PASSWORD=your_redis_password

# JWT密钥
JWT_SECRET=your_very_secure_jwt_secret_key

# OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key

# MinIO配置
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key

# 域名配置
DOMAIN=yourdomain.com
```

### 3. 生成SSL证书

```bash
# 使用Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com

# 或使用自签名证书（仅用于测试）
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem
```

### 4. 部署系统

```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 部署生产环境
./scripts/deploy.sh production deploy
```

### 5. 验证部署

```bash
# 运行健康检查
./scripts/health-check.sh

# 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 查看日志
docker-compose -f docker-compose.production.yml logs -f
```

## Kubernetes部署

### 1. 准备Kubernetes集群

```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 验证集群连接
kubectl cluster-info
```

### 2. 创建命名空间

```bash
kubectl apply -f k8s/namespace.yaml
```

### 3. 配置密钥

```bash
# 编辑密钥配置
vim k8s/configmap.yaml

# 应用配置
kubectl apply -f k8s/configmap.yaml
```

### 4. 部署存储

```bash
# 部署PostgreSQL
kubectl apply -f k8s/postgres.yaml

# 部署Redis
kubectl apply -f k8s/redis.yaml

# 部署MinIO
kubectl apply -f k8s/minio.yaml

# 部署Chroma
kubectl apply -f k8s/chroma.yaml
```

### 5. 部署应用服务

```bash
# 部署知识库服务
kubectl apply -f k8s/knowledge-service.yaml

# 部署绑定服务
kubectl apply -f k8s/binding-service.yaml

# 部署RAG引擎
kubectl apply -f k8s/rag-engine.yaml

# 部署API网关
kubectl apply -f k8s/api-gateway.yaml

# 部署Nginx
kubectl apply -f k8s/nginx.yaml
```

### 6. 配置Ingress

```bash
# 部署Ingress控制器
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 配置Ingress规则
kubectl apply -f k8s/ingress.yaml
```

## 生产环境配置

### 1. 负载均衡配置

```nginx
# nginx/nginx.conf
upstream api_gateway {
    server api-gateway-1:3000 weight=3;
    server api-gateway-2:3000 weight=3;
    server api-gateway-3:3000 weight=2;
    keepalive 32;
}

upstream rag_engine {
    server rag-engine-1:3002 weight=1;
    server rag-engine-2:3002 weight=1;
    keepalive 16;
}
```

### 2. 数据库优化

```sql
-- PostgreSQL优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

### 3. Redis优化

```redis
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 4. 安全配置

```bash
# 防火墙配置
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp
sudo ufw deny 6379/tcp

# 设置文件权限
chmod 600 .env.production
chmod 600 nginx/ssl/*
```

## 监控和日志

### 1. Prometheus监控

```bash
# 启动监控服务
docker-compose -f docker-compose.production.yml up -d prometheus grafana

# 访问Grafana
open http://localhost:3000
# 用户名: admin
# 密码: 见环境变量GRAFANA_PASSWORD
```

### 2. 日志管理

```bash
# 查看实时日志
docker-compose -f docker-compose.production.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f knowledge-service

# 日志轮转配置
cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
```

### 3. 告警配置

```yaml
# monitoring/alertmanager.yml
global:
  smtp_smarthost: 'smtp.yourdomain.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'RAG系统告警: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警: {{ .Annotations.summary }}
      描述: {{ .Annotations.description }}
      {{ end }}
```

## 故障排除

### 1. 常见问题

#### 服务启动失败

```bash
# 检查容器状态
docker-compose -f docker-compose.production.yml ps

# 查看容器日志
docker-compose -f docker-compose.production.yml logs service-name

# 检查端口占用
netstat -tlnp | grep :3000
```

#### 数据库连接失败

```bash
# 检查PostgreSQL状态
docker-compose -f docker-compose.production.yml exec postgres pg_isready

# 检查连接配置
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -l

# 重置数据库密码
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "ALTER USER postgres PASSWORD 'newpassword';"
```

#### 内存不足

```bash
# 检查内存使用
free -h
docker stats

# 增加swap空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' >> /etc/fstab
```

### 2. 性能优化

#### 数据库性能

```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 创建索引
CREATE INDEX CONCURRENTLY idx_documents_status 
ON knowledge_documents(processing_status);
```

#### 缓存优化

```bash
# Redis内存分析
docker-compose -f docker-compose.production.yml exec redis redis-cli --bigkeys

# 清理过期缓存
docker-compose -f docker-compose.production.yml exec redis redis-cli FLUSHDB
```

### 3. 备份和恢复

```bash
# 创建备份
./scripts/backup.sh

# 恢复备份
./scripts/restore.sh /backup/rag_backup_20250709_120000.tar.gz

# 验证备份
./scripts/verify-backup.sh /backup/rag_backup_20250709_120000.tar.gz
```

### 4. 升级和维护

```bash
# 更新系统
./scripts/deploy.sh production update

# 滚动重启
docker-compose -f docker-compose.production.yml restart

# 清理旧镜像
docker image prune -a -f
```

## 联系支持

如果遇到部署问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **文档**: https://docs.yourdomain.com
- **GitHub Issues**: https://github.com/your-org/rag-digital-human/issues

---

**注意**: 请确保在生产环境中使用强密码和安全配置，定期更新系统和依赖包。
