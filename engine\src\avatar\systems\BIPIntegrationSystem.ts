/**
 * BIP骨骼集成系统
 * 负责BIP文件的导入、骨骼映射、动画重定向等功能
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPSkeletonParser, BIPSkeletonData } from '../bip/BIPSkeletonParser';
import { BIPToStandardMapping, StandardSkeletonData } from '../bip/BIPToStandardMapping';
import { DigitalHumanComponent } from '../components/DigitalHumanComponent';
import { AvatarRigComponent } from '../components/AvatarRigComponent';

/**
 * BIP集成系统配置
 */
export interface BIPIntegrationConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否自动修复缺失骨骼 */
  autoFixMissing?: boolean;
  /** 是否严格模式 */
  strictMode?: boolean;
  /** 最大并发处理数 */
  maxConcurrentProcessing?: number;
}

/**
 * BIP导入结果
 */
export interface BIPImportResult {
  /** 是否成功 */
  success: boolean;
  /** 骨骼ID */
  skeletonId?: string;
  /** 骨骼数量 */
  boneCount?: number;
  /** 动画数量 */
  animationCount?: number;
  /** 警告信息 */
  warnings?: string[];
  /** 错误信息 */
  error?: string;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * BIP骨骼集成系统
 */
export class BIPIntegrationSystem extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 6;

  /** 配置 */
  private config: BIPIntegrationConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** BIP解析器 */
  private bipParser: BIPSkeletonParser;

  /** 骨骼映射器 */
  private boneMapper: BIPToStandardMapping;

  /** 处理队列 */
  private processingQueue: Array<{
    file: File;
    entity: Entity;
    resolve: (result: BIPImportResult) => void;
    reject: (error: Error) => void;
  }> = [];

  /** 当前处理中的任务 */
  private activeProcessing: Set<string> = new Set();

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: BIPIntegrationConfig = {}) {
    super(world, BIPIntegrationSystem.PRIORITY);

    this.config = {
      debug: false,
      autoFixMissing: true,
      strictMode: false,
      maxConcurrentProcessing: 3,
      ...config
    };

    // 初始化组件
    this.bipParser = new BIPSkeletonParser({
      debug: this.config.debug,
      validateStructure: true,
      calculateWorldTransforms: true,
      generateHierarchy: true
    });

    this.boneMapper = new BIPToStandardMapping({
      debug: this.config.debug,
      strictMode: this.config.strictMode,
      autoFixMissing: this.config.autoFixMissing
    });

    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.bipParser.on('parseCompleted', (skeletonData: BIPSkeletonData) => {
      this.emit('bipParsed', skeletonData);
    });

    this.boneMapper.on('mappingCompleted', (standardData: StandardSkeletonData) => {
      this.emit('skeletonMapped', standardData);
    });
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('[BIPIntegrationSystem] 系统初始化');
    }
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 处理队列中的任务
    this.processQueue();
  }

  /**
   * 导入BIP骨骼文件
   * @param file BIP文件
   * @param digitalHuman 数字人实体
   * @returns 导入结果
   */
  public async importBIPSkeleton(file: File, digitalHuman: Entity): Promise<BIPImportResult> {
    return new Promise((resolve, reject) => {
      // 添加到处理队列
      this.processingQueue.push({
        file,
        entity: digitalHuman,
        resolve,
        reject
      });

      if (this.config.debug) {
        console.log(`[BIPIntegrationSystem] 添加BIP导入任务到队列: ${file.name}`);
      }
    });
  }

  /**
   * 处理队列
   */
  private processQueue(): void {
    // 检查是否可以处理新任务
    while (
      this.processingQueue.length > 0 &&
      this.activeProcessing.size < (this.config.maxConcurrentProcessing || 3)
    ) {
      const task = this.processingQueue.shift()!;
      const taskId = `${task.file.name}_${Date.now()}`;
      
      this.activeProcessing.add(taskId);
      this.processImportTask(task, taskId);
    }
  }

  /**
   * 处理导入任务
   * @param task 导入任务
   * @param taskId 任务ID
   */
  private async processImportTask(
    task: {
      file: File;
      entity: Entity;
      resolve: (result: BIPImportResult) => void;
      reject: (error: Error) => void;
    },
    taskId: string
  ): Promise<void> {
    try {
      if (this.config.debug) {
        console.log(`[BIPIntegrationSystem] 开始处理BIP导入任务: ${task.file.name}`);
      }

      // 1. 解析BIP文件
      const arrayBuffer = await task.file.arrayBuffer();
      const bipData = await this.bipParser.parseBIPFile(arrayBuffer);

      // 2. 验证BIP数据
      const validation = this.validateBIPData(bipData);
      if (!validation.isValid) {
        throw new Error(`BIP文件验证失败: ${validation.errors.join(', ')}`);
      }

      // 3. 映射到标准骨骼
      const standardSkeleton = await this.boneMapper.mapToStandardSkeleton(bipData);

      // 4. 应用到数字人
      await this.applySkeletonToDigitalHuman(standardSkeleton, task.entity);

      // 5. 处理动画数据（如果有）
      const animationCount = await this.importBIPAnimations(bipData, task.entity);

      const result: BIPImportResult = {
        success: true,
        skeletonId: standardSkeleton.rootBoneType,
        boneCount: standardSkeleton.bones.size,
        animationCount,
        warnings: validation.warnings
      };

      task.resolve(result);
      this.emit('bipImportCompleted', task.entity, result);

    } catch (error) {
      const result: BIPImportResult = {
        success: false,
        error: error.message
      };

      task.reject(error as Error);
      this.emit('bipImportError', task.entity, error);

      if (this.config.debug) {
        console.error('[BIPIntegrationSystem] BIP导入失败', task.file.name, error);
      }
    } finally {
      this.activeProcessing.delete(taskId);
    }
  }

  /**
   * 验证BIP数据
   * @param bipData BIP骨骼数据
   * @returns 验证结果
   */
  private validateBIPData(bipData: BIPSkeletonData): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查基本结构
    if (!bipData.bones || bipData.bones.length === 0) {
      errors.push('BIP文件不包含有效的骨骼数据');
    }

    // 检查必需骨骼
    const requiredBones = ['Bip01', 'Bip01 Pelvis', 'Bip01 Spine'];
    for (const boneName of requiredBones) {
      if (!bipData.bones.find(b => b.name === boneName)) {
        warnings.push(`缺少推荐的骨骼: ${boneName}`);
      }
    }

    // 检查骨骼层级
    if (!this.validateBoneHierarchy(bipData)) {
      warnings.push('骨骼层级结构可能不完整');
    }

    // 检查骨骼数量
    if (bipData.bones.length < 10) {
      warnings.push('骨骼数量较少，可能影响动画质量');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证骨骼层级
   * @param bipData BIP骨骼数据
   * @returns 是否有效
   */
  private validateBoneHierarchy(bipData: BIPSkeletonData): boolean {
    // 检查是否有根骨骼
    const rootBones = bipData.bones.filter(bone => bone.parentId === -1);
    if (rootBones.length !== 1) {
      return false;
    }

    // 检查是否所有骨骼都有有效的父子关系
    for (const bone of bipData.bones) {
      if (bone.parentId >= 0) {
        const parent = bipData.bones.find(b => b.id === bone.parentId);
        if (!parent) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 应用骨骼到数字人
   * @param skeleton 标准骨骼数据
   * @param digitalHuman 数字人实体
   */
  private async applySkeletonToDigitalHuman(
    skeleton: StandardSkeletonData,
    digitalHuman: Entity
  ): Promise<void> {
    // 获取数字人的骨骼组件
    const rigComponent = digitalHuman.getComponent<AvatarRigComponent>(AvatarRigComponent.TYPE);
    if (!rigComponent) {
      throw new Error('数字人缺少骨骼组件');
    }

    // 应用新的骨骼结构
    for (const [boneType, bone] of skeleton.bones) {
      // TODO: 创建实际的骨骼实体和Three.js骨骼对象
      // 这里需要根据标准骨骼数据创建对应的实体和Three.js骨骼
      const boneEntity = new Entity(this.world);
      boneEntity.name = bone.name;
      
      // 添加到世界
      this.world.addEntity(boneEntity);
      
      // 设置到骨骼组件
      rigComponent.setBone(boneType as any, boneEntity);
    }

    // 重新计算骨骼权重
    await this.recalculateSkinWeights(digitalHuman, skeleton);

    // 更新骨骼绑定
    rigComponent.updateBindings();

    if (this.config.debug) {
      console.log(`[BIPIntegrationSystem] 骨骼应用完成，共 ${skeleton.bones.size} 个骨骼`);
    }
  }

  /**
   * 重新计算蒙皮权重
   * @param digitalHuman 数字人实体
   * @param skeleton 骨骼数据
   */
  private async recalculateSkinWeights(
    digitalHuman: Entity,
    skeleton: StandardSkeletonData
  ): Promise<void> {
    // TODO: 实现蒙皮权重重新计算
    if (this.config.debug) {
      console.log('[BIPIntegrationSystem] 重新计算蒙皮权重');
    }
  }

  /**
   * 导入BIP动画
   * @param bipData BIP骨骼数据
   * @param digitalHuman 数字人实体
   * @returns 导入的动画数量
   */
  private async importBIPAnimations(
    bipData: BIPSkeletonData,
    digitalHuman: Entity
  ): Promise<number> {
    // TODO: 实现BIP动画导入
    // 这里需要从BIP数据中提取动画信息并应用到数字人
    
    if (this.config.debug) {
      console.log('[BIPIntegrationSystem] 导入BIP动画');
    }

    return 0; // 暂时返回0
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 发出事件
   * @param event 事件名称
   * @param args 参数
   */
  private emit(event: string, ...args: any[]): void {
    this.eventEmitter.emit(event, ...args);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理处理队列
    this.processingQueue.length = 0;
    this.activeProcessing.clear();

    // 销毁组件
    this.bipParser.dispose();
    this.boneMapper.dispose();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    if (this.config.debug) {
      console.log('[BIPIntegrationSystem] 系统已销毁');
    }
  }
}
