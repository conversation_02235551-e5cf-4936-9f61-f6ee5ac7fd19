import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { KnowledgeController } from './knowledge.controller';
import { KnowledgeService } from './knowledge.service';
import { BindingController } from './binding.controller';
import { BindingService } from './binding.service';
import { RAGController } from './rag.controller';
import { RAGService } from './rag.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
  ],
  controllers: [
    KnowledgeController,
    BindingController,
    RAGController,
  ],
  providers: [
    KnowledgeService,
    BindingService,
    RAGService,
  ],
  exports: [
    KnowledgeService,
    BindingService,
    RAGService,
  ],
})
export class KnowledgeModule {}
