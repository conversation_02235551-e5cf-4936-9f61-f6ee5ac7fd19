import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { KnowledgeDocument, DocumentChunk } from '../entities';
import { StorageService } from '../storage/storage.service';
import { CacheService } from '../cache/cache.service';
import { DocumentParserService } from './document-parser.service';
import { DocumentChunkerService, ChunkOptions } from './document-chunker.service';
import { EmbeddingService } from './embedding.service';
import { VectorDatabaseService } from './vector-database.service';

export interface ProcessingResult {
  documentId: string;
  status: 'completed' | 'failed';
  chunkCount: number;
  processingTime: number;
  error?: string;
}

@Injectable()
export class DocumentProcessingService {
  constructor(
    @InjectRepository(KnowledgeDocument)
    private readonly documentRepository: Repository<KnowledgeDocument>,
    @InjectRepository(DocumentChunk)
    private readonly chunkRepository: Repository<DocumentChunk>,
    private readonly storageService: StorageService,
    private readonly cacheService: CacheService,
    private readonly parserService: DocumentParserService,
    private readonly chunkerService: DocumentChunkerService,
    private readonly embeddingService: EmbeddingService,
    private readonly vectorDbService: VectorDatabaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理上传的文档
   */
  async processDocument(documentId: string): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      console.log(`开始处理文档: ${documentId}`);

      // 1. 获取文档信息
      const document = await this.getDocumentInfo(documentId);
      if (!document) {
        throw new Error('文档不存在');
      }

      // 2. 更新处理状态
      await this.updateProcessingStatus(documentId, 'processing');

      // 3. 下载并解析文档
      const parsedContent = await this.downloadAndParseDocument(document);

      // 4. 文档分块
      const chunks = await this.chunkDocument(parsedContent.content);

      // 5. 生成向量嵌入
      const embeddings = await this.generateEmbeddings(chunks);

      // 6. 存储到向量数据库
      await this.storeVectors(document, chunks, embeddings);

      // 7. 保存文档块记录
      await this.saveChunkRecords(documentId, chunks);

      // 8. 更新处理状态
      await this.updateProcessingStatus(documentId, 'completed');

      // 9. 更新知识库统计
      await this.updateKnowledgeBaseStats(document.knowledgeBaseId);

      const processingTime = Date.now() - startTime;
      console.log(`文档处理完成: ${documentId}, 耗时: ${processingTime}ms`);

      return {
        documentId,
        status: 'completed',
        chunkCount: chunks.length,
        processingTime,
      };

    } catch (error) {
      console.error(`文档处理失败: ${documentId}`, error);
      
      await this.updateProcessingStatus(documentId, 'failed', error.message);
      
      const processingTime = Date.now() - startTime;
      
      return {
        documentId,
        status: 'failed',
        chunkCount: 0,
        processingTime,
        error: error.message,
      };
    }
  }

  /**
   * 获取文档信息
   */
  private async getDocumentInfo(documentId: string): Promise<KnowledgeDocument | null> {
    return await this.documentRepository.findOne({
      where: { id: documentId },
      relations: ['knowledgeBase'],
    });
  }

  /**
   * 下载并解析文档
   */
  private async downloadAndParseDocument(document: KnowledgeDocument): Promise<any> {
    // 从对象存储下载文件
    const fileBuffer = await this.storageService.downloadFile(document.filePath);
    
    // 解析文档内容
    const parsedContent = await this.parserService.parseDocument(
      fileBuffer,
      document.fileType,
      document.filename,
    );

    // 验证解析结果
    if (!this.parserService.validateParsedContent(parsedContent)) {
      throw new Error('文档解析结果无效');
    }

    return parsedContent;
  }

  /**
   * 文档分块
   */
  private async chunkDocument(content: string): Promise<any[]> {
    const chunkOptions: ChunkOptions = {
      chunkSize: this.configService.get('production.documentProcessing.chunkSize', 1000),
      overlap: this.configService.get('production.documentProcessing.chunkOverlap', 200),
      preserveSentences: true,
      minChunkSize: 100,
      maxChunkSize: 2000,
    };

    const chunks = await this.chunkerService.chunkDocument(content, chunkOptions);
    
    // 合并过小的块
    return this.chunkerService.mergeSmallChunks(chunks, chunkOptions.minChunkSize);
  }

  /**
   * 生成向量嵌入
   */
  private async generateEmbeddings(chunks: any[]): Promise<number[][]> {
    const texts = chunks.map(chunk => chunk.content);
    const embeddingResults = await this.embeddingService.generateEmbeddings(texts);
    
    return embeddingResults.map(result => result.embedding);
  }

  /**
   * 存储向量到数据库
   */
  private async storeVectors(
    document: KnowledgeDocument,
    chunks: any[],
    embeddings: number[][],
  ): Promise<void> {
    const vectorRecords = chunks.map((chunk, index) => ({
      id: `${document.id}_chunk_${index}`,
      vector: embeddings[index],
      metadata: {
        document_id: document.id,
        knowledge_base_id: document.knowledgeBaseId,
        chunk_index: index,
        content: chunk.content,
        filename: document.filename,
        file_type: document.fileType,
        ...document.metadata,
      },
    }));

    // 使用知识库ID作为命名空间
    await this.vectorDbService.storeVectors(
      document.knowledgeBaseId,
      vectorRecords,
    );
  }

  /**
   * 保存文档块记录
   */
  private async saveChunkRecords(
    documentId: string,
    chunks: any[],
  ): Promise<void> {
    const chunkEntities = chunks.map((chunk, index) => {
      const entity = this.chunkRepository.create({
        documentId,
        chunkIndex: index,
        content: chunk.content,
        contentHash: chunk.metadata.hash,
        startOffset: chunk.startOffset,
        endOffset: chunk.endOffset,
        metadata: chunk.metadata,
        vectorId: `${documentId}_chunk_${index}`,
      });
      return entity;
    });

    // 批量保存
    await this.chunkRepository.save(chunkEntities);
  }

  /**
   * 更新处理状态
   */
  private async updateProcessingStatus(
    documentId: string,
    status: string,
    error?: string,
  ): Promise<void> {
    const updateData: any = {
      processingStatus: status,
    };

    if (status === 'completed') {
      updateData.processedAt = new Date();
    }

    if (error) {
      updateData.metadata = { error };
    }

    await this.documentRepository.update(documentId, updateData);
  }

  /**
   * 更新知识库统计
   */
  private async updateKnowledgeBaseStats(knowledgeBaseId: string): Promise<void> {
    // 统计文档数量
    const documentCount = await this.documentRepository.count({
      where: {
        knowledgeBaseId,
        processingStatus: 'completed',
      },
    });

    // 统计块数量
    const totalChunks = await this.chunkRepository
      .createQueryBuilder('chunk')
      .innerJoin('chunk.document', 'document')
      .where('document.knowledgeBaseId = :knowledgeBaseId', { knowledgeBaseId })
      .andWhere('document.processingStatus = :status', { status: 'completed' })
      .getCount();

    // 更新知识库统计
    await this.documentRepository.manager.query(
      `UPDATE knowledge_bases SET document_count = $1, total_chunks = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3`,
      [documentCount, totalChunks, knowledgeBaseId],
    );

    // 清除相关缓存
    await this.clearKnowledgeBaseCache(knowledgeBaseId);
  }

  /**
   * 清除知识库缓存
   */
  private async clearKnowledgeBaseCache(knowledgeBaseId: string): Promise<void> {
    const cacheKeys = [
      `knowledge_base:${knowledgeBaseId}`,
      `knowledge_base:${knowledgeBaseId}:documents`,
      `knowledge_base:${knowledgeBaseId}:stats`,
    ];

    for (const key of cacheKeys) {
      await this.cacheService.del(key);
    }
  }

  /**
   * 批量处理文档
   */
  async batchProcessDocuments(documentIds: string[]): Promise<ProcessingResult[]> {
    const results: ProcessingResult[] = [];
    
    for (const documentId of documentIds) {
      try {
        const result = await this.processDocument(documentId);
        results.push(result);
      } catch (error) {
        results.push({
          documentId,
          status: 'failed',
          chunkCount: 0,
          processingTime: 0,
          error: error.message,
        });
      }
    }

    return results;
  }

  /**
   * 重新处理文档
   */
  async reprocessDocument(documentId: string): Promise<ProcessingResult> {
    // 删除现有的块和向量
    await this.deleteDocumentChunks(documentId);
    
    // 重新处理
    return await this.processDocument(documentId);
  }

  /**
   * 删除文档块
   */
  private async deleteDocumentChunks(documentId: string): Promise<void> {
    const document = await this.getDocumentInfo(documentId);
    if (!document) {
      return;
    }

    // 删除向量数据库中的记录
    const chunks = await this.chunkRepository.find({
      where: { documentId },
    });

    if (chunks.length > 0) {
      const vectorIds = chunks.map(chunk => chunk.vectorId);
      await this.vectorDbService.deleteVectors(document.knowledgeBaseId, vectorIds);
    }

    // 删除数据库中的块记录
    await this.chunkRepository.delete({ documentId });
  }
}
