/**
 * 数字人交互控制器
 * 集成语音、动作、表情控制，实现完整的数字人交互体验，支持实时响应和状态同步
 */

import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { SpeechRecognitionService, VoiceCommand } from '../speech/SpeechRecognitionService';
import { SpeechSynthesisService, LipSyncData } from '../speech/SpeechSynthesisService';
import { DialogueManager, DialogueResponse, EmotionType, ActionType } from '../dialogue/DialogueManager';
import { DigitalHumanNavigationComponent } from '../navigation/DigitalHumanNavigationComponent';

/**
 * 数字人状态枚举
 */
export enum DigitalHumanState {
  IDLE = 'idle',
  LISTENING = 'listening',
  THINKING = 'thinking',
  SPEAKING = 'speaking',
  MOVING = 'moving',
  INTERACTING = 'interacting',
  ERROR = 'error'
}

/**
 * 表情类型枚举
 */
export enum FacialExpression {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  SURPRISED = 'surprised',
  ANGRY = 'angry',
  CONFUSED = 'confused',
  EXCITED = 'excited',
  THINKING = 'thinking'
}

/**
 * 动画状态接口
 */
export interface AnimationState {
  name: string;
  duration: number;
  loop: boolean;
  weight: number;
  fadeInTime: number;
  fadeOutTime: number;
}

/**
 * 数字人外观配置接口
 */
export interface DigitalHumanAppearance {
  model: string;
  textures: Record<string, string>;
  materials: Record<string, any>;
  scale: THREE.Vector3;
  animations: Record<string, AnimationState>;
}

/**
 * 交互配置接口
 */
export interface InteractionConfig {
  enableVoiceInput: boolean;
  enableVoiceOutput: boolean;
  enableGestures: boolean;
  enableFacialExpressions: boolean;
  enableLipSync: boolean;
  responseDelay: number;
  idleTimeout: number;
  maxConversationLength: number;
  autoNavigation: boolean;
}

/**
 * 数字人交互事件接口
 */
export interface InteractionEvent {
  type: string;
  data: any;
  timestamp: Date;
  source: 'voice' | 'gesture' | 'navigation' | 'system';
}

/**
 * 动画控制器
 */
export class AnimationController {
  private mixer: THREE.AnimationMixer | null = null;
  private actions: Map<string, THREE.AnimationAction> = new Map();
  private currentAction: THREE.AnimationAction | null = null;
  private model: THREE.Object3D | null = null;

  constructor(model?: THREE.Object3D) {
    if (model) {
      this.setModel(model);
    }
  }

  /**
   * 设置模型
   */
  public setModel(model: THREE.Object3D): void {
    this.model = model;
    this.mixer = new THREE.AnimationMixer(model);
    this.loadAnimations();
  }

  /**
   * 加载动画
   */
  private loadAnimations(): void {
    if (!this.model || !this.mixer) return;

    // 这里应该加载实际的动画文件
    // 暂时创建一些基础动画
    const animations = this.createBasicAnimations();
    
    animations.forEach((clip, name) => {
      const action = this.mixer!.clipAction(clip);
      this.actions.set(name, action);
    });
  }

  /**
   * 创建基础动画
   */
  private createBasicAnimations(): Map<string, THREE.AnimationClip> {
    const animations = new Map<string, THREE.AnimationClip>();

    // 创建简单的动画剪辑
    const idleClip = this.createIdleAnimation();
    const waveClip = this.createWaveAnimation();
    const pointClip = this.createPointAnimation();
    const thinkClip = this.createThinkAnimation();

    animations.set('idle', idleClip);
    animations.set('wave', waveClip);
    animations.set('point', pointClip);
    animations.set('think', thinkClip);

    return animations;
  }

  /**
   * 创建待机动画
   */
  private createIdleAnimation(): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];
    
    // 简单的呼吸动画
    const times = [0, 2, 4];
    const values = [1, 1.02, 1];
    
    const scaleTrack = new THREE.VectorKeyframeTrack(
      '.scale',
      times,
      values.flatMap(v => [v, v, v])
    );
    
    tracks.push(scaleTrack);
    
    return new THREE.AnimationClip('idle', 4, tracks);
  }

  /**
   * 创建挥手动画
   */
  private createWaveAnimation(): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];
    
    // 简单的旋转动画
    const times = [0, 0.5, 1, 1.5, 2];
    const values = [0, 0.3, -0.3, 0.3, 0];
    
    const rotationTrack = new THREE.NumberKeyframeTrack(
      '.rotation[y]',
      times,
      values
    );
    
    tracks.push(rotationTrack);
    
    return new THREE.AnimationClip('wave', 2, tracks);
  }

  /**
   * 创建指向动画
   */
  private createPointAnimation(): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];
    
    const times = [0, 1, 2];
    const values = [0, 0.5, 0];
    
    const rotationTrack = new THREE.NumberKeyframeTrack(
      '.rotation[x]',
      times,
      values
    );
    
    tracks.push(rotationTrack);
    
    return new THREE.AnimationClip('point', 2, tracks);
  }

  /**
   * 创建思考动画
   */
  private createThinkAnimation(): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];
    
    const times = [0, 1, 2, 3, 4];
    const values = [0, -0.1, 0.1, -0.1, 0];
    
    const rotationTrack = new THREE.NumberKeyframeTrack(
      '.rotation[z]',
      times,
      values
    );
    
    tracks.push(rotationTrack);
    
    return new THREE.AnimationClip('think', 4, tracks);
  }

  /**
   * 播放动画
   */
  public playAnimation(
    name: string,
    loop: boolean = false,
    fadeTime: number = 0.3
  ): boolean {
    const action = this.actions.get(name);
    if (!action) {
      console.warn(`动画 ${name} 不存在`);
      return false;
    }

    // 停止当前动画
    if (this.currentAction && this.currentAction !== action) {
      this.currentAction.fadeOut(fadeTime);
    }

    // 播放新动画
    action.reset();
    action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);
    action.fadeIn(fadeTime);
    action.play();

    this.currentAction = action;
    
    console.log(`播放动画: ${name}`);
    return true;
  }

  /**
   * 停止动画
   */
  public stopAnimation(fadeTime: number = 0.3): void {
    if (this.currentAction) {
      this.currentAction.fadeOut(fadeTime);
      this.currentAction = null;
    }
  }

  /**
   * 更新动画
   */
  public update(deltaTime: number): void {
    if (this.mixer) {
      this.mixer.update(deltaTime);
    }
  }

  /**
   * 获取当前动画
   */
  public getCurrentAnimation(): string | null {
    return this.currentAction ? this.currentAction.getClip().name : null;
  }

  /**
   * 检查动画是否存在
   */
  public hasAnimation(name: string): boolean {
    return this.actions.has(name);
  }

  /**
   * 获取所有动画名称
   */
  public getAnimationNames(): string[] {
    return Array.from(this.actions.keys());
  }
}

/**
 * 表情控制器
 */
export class FacialExpressionController {
  private currentExpression: FacialExpression = FacialExpression.NEUTRAL;
  private expressionIntensity: number = 1.0;
  private model: THREE.Object3D | null = null;
  private morphTargets: Map<string, number> = new Map();

  constructor(model?: THREE.Object3D) {
    if (model) {
      this.setModel(model);
    }
  }

  /**
   * 设置模型
   */
  public setModel(model: THREE.Object3D): void {
    this.model = model;
    this.initializeMorphTargets();
  }

  /**
   * 初始化变形目标
   */
  private initializeMorphTargets(): void {
    if (!this.model) return;

    // 查找具有变形目标的网格
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.morphTargetDictionary) {
        Object.keys(child.morphTargetDictionary).forEach(name => {
          this.morphTargets.set(name, 0);
        });
      }
    });
  }

  /**
   * 设置表情
   */
  public setExpression(
    expression: FacialExpression,
    intensity: number = 1.0,
    duration: number = 0.5
  ): void {
    this.currentExpression = expression;
    this.expressionIntensity = Math.max(0, Math.min(1, intensity));

    console.log(`设置表情: ${expression}, 强度: ${this.expressionIntensity}`);

    // 应用表情
    this.applyExpression(expression, this.expressionIntensity, duration);
  }

  /**
   * 应用表情
   */
  private applyExpression(
    expression: FacialExpression,
    intensity: number,
    duration: number
  ): void {
    if (!this.model) return;

    // 表情映射到变形目标
    const expressionMap: Record<FacialExpression, Record<string, number>> = {
      [FacialExpression.NEUTRAL]: {},
      [FacialExpression.HAPPY]: { 'smile': 1.0, 'eyeSquint': 0.3 },
      [FacialExpression.SAD]: { 'frown': 1.0, 'eyeClose': 0.2 },
      [FacialExpression.SURPRISED]: { 'eyeWide': 1.0, 'mouthOpen': 0.5 },
      [FacialExpression.ANGRY]: { 'frown': 0.8, 'eyeSquint': 0.8, 'browDown': 1.0 },
      [FacialExpression.CONFUSED]: { 'browUp': 0.6, 'eyeSquint': 0.3 },
      [FacialExpression.EXCITED]: { 'smile': 1.0, 'eyeWide': 0.5 },
      [FacialExpression.THINKING]: { 'browUp': 0.4, 'eyeSquint': 0.2 }
    };

    const targetMorphs = expressionMap[expression] || {};

    // 重置所有变形目标
    this.morphTargets.forEach((value, name) => {
      this.morphTargets.set(name, 0);
    });

    // 设置目标表情的变形目标
    Object.entries(targetMorphs).forEach(([morphName, value]) => {
      if (this.morphTargets.has(morphName)) {
        this.morphTargets.set(morphName, value * intensity);
      }
    });

    // 应用到模型
    this.applyMorphTargets(duration);
  }

  /**
   * 应用变形目标
   */
  private applyMorphTargets(duration: number): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.morphTargetInfluences) {
        this.morphTargets.forEach((value, name) => {
          const index = child.morphTargetDictionary?.[name];
          if (index !== undefined && child.morphTargetInfluences) {
            // 简单的线性插值（实际应该使用补间动画）
            child.morphTargetInfluences[index] = value;
          }
        });
      }
    });
  }

  /**
   * 获取当前表情
   */
  public getCurrentExpression(): FacialExpression {
    return this.currentExpression;
  }

  /**
   * 获取表情强度
   */
  public getExpressionIntensity(): number {
    return this.expressionIntensity;
  }

  /**
   * 重置表情
   */
  public resetExpression(duration: number = 0.5): void {
    this.setExpression(FacialExpression.NEUTRAL, 1.0, duration);
  }
}

/**
 * 数字人交互控制器组件
 */
export class DigitalHumanInteractionController extends Component {
  public static readonly TYPE = 'DigitalHumanInteraction';

  private speechRecognition: SpeechRecognitionService;
  private speechSynthesis: SpeechSynthesisService;
  private dialogueManager: DialogueManager;
  private navigationComponent: DigitalHumanNavigationComponent | null = null;
  private animationController: AnimationController;
  private facialController: FacialExpressionController;

  private state: DigitalHumanState = DigitalHumanState.IDLE;
  private config: InteractionConfig;
  private sessionId: string;
  private lastInteractionTime: Date = new Date();
  private idleTimer: number = 0;

  // 事件回调
  public onStateChanged?: (state: DigitalHumanState) => void;
  public onInteractionEvent?: (event: InteractionEvent) => void;
  public onResponse?: (response: DialogueResponse) => void;

  constructor(
    entity: Entity,
    speechRecognition: SpeechRecognitionService,
    speechSynthesis: SpeechSynthesisService,
    dialogueManager: DialogueManager,
    config: InteractionConfig,
    sessionId: string = 'default'
  ) {
    super(entity, DigitalHumanInteractionController.TYPE);

    this.speechRecognition = speechRecognition;
    this.speechSynthesis = speechSynthesis;
    this.dialogueManager = dialogueManager;
    this.config = config;
    this.sessionId = sessionId;

    this.animationController = new AnimationController();
    this.facialController = new FacialExpressionController();

    this.setupEventHandlers();
    this.startIdleTimer();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 语音识别事件
    this.speechRecognition.onTranscript = (transcript, confidence) => {
      this.handleVoiceInput(transcript, confidence);
    };

    this.speechRecognition.onCommand = (command) => {
      this.handleVoiceCommand(command);
    };

    this.speechRecognition.onStateChange = (state) => {
      if (state === 'listening') {
        this.setState(DigitalHumanState.LISTENING);
      }
    };

    // 语音合成事件
    this.speechSynthesis.onSpeechStart = () => {
      this.setState(DigitalHumanState.SPEAKING);
    };

    this.speechSynthesis.onSpeechEnd = () => {
      this.setState(DigitalHumanState.IDLE);
      this.startListening();
    };

    this.speechSynthesis.onLipSyncUpdate = (lipSyncData) => {
      this.handleLipSync(lipSyncData);
    };

    // 导航组件事件
    this.navigationComponent = this.entity.getComponent<DigitalHumanNavigationComponent>(
      DigitalHumanNavigationComponent.TYPE
    );

    if (this.navigationComponent) {
      this.navigationComponent.onStateChanged = (data) => {
        if (data.state === 'moving') {
          this.setState(DigitalHumanState.MOVING);
        }
      };

      this.navigationComponent.onReachedStopPoint = (stopPoint) => {
        this.handleStopPointReached(stopPoint);
      };
    }
  }

  /**
   * 处理语音输入
   */
  private async handleVoiceInput(transcript: string, confidence: number): Promise<void> {
    if (confidence < 0.5) {
      console.log('语音识别置信度过低，忽略输入');
      return;
    }

    console.log(`收到语音输入: ${transcript} (置信度: ${confidence})`);

    this.setState(DigitalHumanState.THINKING);
    this.updateLastInteractionTime();

    // 设置思考表情和动画
    this.facialController.setExpression(FacialExpression.THINKING, 0.6);
    this.animationController.playAnimation('think', true);

    try {
      // 处理对话
      const response = await this.dialogueManager.processUserInput(
        this.sessionId,
        transcript,
        {
          currentLocation: this.getCurrentLocation(),
          userProfile: this.getUserProfile()
        }
      );

      // 触发响应事件
      if (this.onResponse) {
        this.onResponse(response);
      }

      // 执行响应
      await this.executeResponse(response);

    } catch (error) {
      console.error('处理语音输入时出错:', error);
      this.setState(DigitalHumanState.ERROR);
      await this.speakError('抱歉，我遇到了一些问题，请稍后再试。');
    }
  }

  /**
   * 处理语音命令
   */
  private async handleVoiceCommand(command: VoiceCommand): Promise<void> {
    console.log(`执行语音命令: ${command.action}`);

    this.updateLastInteractionTime();

    const event: InteractionEvent = {
      type: 'voice_command',
      data: command,
      timestamp: new Date(),
      source: 'voice'
    };

    if (this.onInteractionEvent) {
      this.onInteractionEvent(event);
    }

    switch (command.action) {
      case 'start_navigation':
        this.startNavigation();
        break;
      case 'stop_navigation':
        this.stopNavigation();
        break;
      case 'explain_current':
        await this.explainCurrentLocation();
        break;
      case 'next_point':
        this.navigateToNext();
        break;
      case 'previous_point':
        this.navigateToPrevious();
        break;
      case 'repeat_last':
        await this.repeatLastResponse();
        break;
      default:
        console.warn(`未知的语音命令: ${command.action}`);
    }
  }

  /**
   * 执行响应
   */
  private async executeResponse(response: DialogueResponse): Promise<void> {
    // 设置表情
    this.setExpressionFromEmotion(response.emotion.emotion, response.emotion.intensity);

    // 执行动作
    this.executeActions(response.actions);

    // 语音输出
    if (this.config.enableVoiceOutput) {
      await this.speechSynthesis.speak(
        response.text,
        response.emotion.emotion,
        response.emotion.intensity
      );
    }
  }

  /**
   * 根据情感设置表情
   */
  private setExpressionFromEmotion(emotion: EmotionType, intensity: number): void {
    let expression: FacialExpression;

    switch (emotion) {
      case EmotionType.POSITIVE:
        expression = FacialExpression.HAPPY;
        break;
      case EmotionType.EXCITED:
        expression = FacialExpression.EXCITED;
        break;
      case EmotionType.NEGATIVE:
        expression = FacialExpression.SAD;
        break;
      case EmotionType.CONFUSED:
        expression = FacialExpression.CONFUSED;
        break;
      case EmotionType.FRUSTRATED:
        expression = FacialExpression.ANGRY;
        break;
      case EmotionType.SATISFIED:
        expression = FacialExpression.HAPPY;
        break;
      default:
        expression = FacialExpression.NEUTRAL;
    }

    this.facialController.setExpression(expression, intensity);
  }

  /**
   * 执行动作
   */
  private executeActions(actionMapping: any): void {
    if (!this.config.enableGestures) return;

    for (const action of actionMapping.actions) {
      setTimeout(() => {
        this.executeAction(action);
      }, action.delay);
    }
  }

  /**
   * 执行单个动作
   */
  private executeAction(action: any): void {
    console.log(`执行动作: ${action.type}`, action.parameters);

    switch (action.type) {
      case ActionType.GESTURE:
        this.playGesture(action.parameters.type);
        break;
      case ActionType.POINT:
        this.pointToDirection(action.parameters.direction);
        break;
      case ActionType.NOD:
        this.playGesture('nod');
        break;
      case ActionType.SHAKE_HEAD:
        this.playGesture('shake_head');
        break;
      case ActionType.SMILE:
        this.facialController.setExpression(
          FacialExpression.HAPPY,
          action.parameters.intensity || 0.8
        );
        break;
      case ActionType.THINK:
        this.animationController.playAnimation('think', true);
        break;
      case ActionType.MOVE:
        this.moveToPosition(action.parameters.position);
        break;
      case ActionType.LOOK_AT:
        this.lookAtTarget(action.parameters.target);
        break;
      case ActionType.TURN_TO:
        this.turnToDirection(action.parameters.direction);
        break;
    }
  }

  /**
   * 播放手势动画
   */
  private playGesture(gestureType: string): void {
    const animationMap: Record<string, string> = {
      'wave': 'wave',
      'point': 'point',
      'nod': 'nod',
      'shake_head': 'shake_head',
      'explain': 'explain',
      'present': 'present',
      'goodbye': 'wave'
    };

    const animationName = animationMap[gestureType] || 'idle';
    this.animationController.playAnimation(animationName, false);
  }

  /**
   * 指向方向
   */
  private pointToDirection(direction: string): void {
    console.log(`指向方向: ${direction}`);
    this.animationController.playAnimation('point', false);
  }

  /**
   * 移动到位置
   */
  private moveToPosition(position: THREE.Vector3): void {
    if (this.navigationComponent) {
      // 这里可以实现移动到指定位置的逻辑
      console.log(`移动到位置: ${position.x}, ${position.y}, ${position.z}`);
    }
  }

  /**
   * 看向目标
   */
  private lookAtTarget(target: THREE.Vector3): void {
    console.log(`看向目标: ${target.x}, ${target.y}, ${target.z}`);
    // 这里可以实现头部转向的逻辑
  }

  /**
   * 转向方向
   */
  private turnToDirection(direction: string): void {
    console.log(`转向方向: ${direction}`);
    // 这里可以实现身体转向的逻辑
  }

  /**
   * 处理口型同步
   */
  private handleLipSync(lipSyncData: LipSyncData): void {
    if (!this.config.enableLipSync) return;

    // 这里可以实现口型同步的逻辑
    console.log(`口型同步: ${lipSyncData.viseme}, 强度: ${lipSyncData.intensity}`);
  }

  /**
   * 处理停留点到达
   */
  private async handleStopPointReached(stopPoint: any): Promise<void> {
    this.setState(DigitalHumanState.INTERACTING);

    // 自动介绍当前位置
    if (this.config.autoNavigation) {
      await this.explainCurrentLocation();
    }
  }

  /**
   * 开始导航
   */
  private startNavigation(): void {
    if (this.navigationComponent) {
      this.navigationComponent.startMoving();
      this.setState(DigitalHumanState.MOVING);
    }
  }

  /**
   * 停止导航
   */
  private stopNavigation(): void {
    if (this.navigationComponent) {
      this.navigationComponent.stopMoving();
      this.setState(DigitalHumanState.IDLE);
    }
  }

  /**
   * 解释当前位置
   */
  private async explainCurrentLocation(): Promise<void> {
    const location = this.getCurrentLocation();
    const query = `介绍一下${location}的展品`;

    try {
      const response = await this.dialogueManager.processUserInput(
        this.sessionId,
        query
      );

      await this.executeResponse(response);
    } catch (error) {
      console.error('解释当前位置时出错:', error);
    }
  }

  /**
   * 导航到下一个点
   */
  private navigateToNext(): void {
    if (this.navigationComponent) {
      // 这里可以实现导航到下一个停留点的逻辑
      console.log('导航到下一个点');
    }
  }

  /**
   * 导航到上一个点
   */
  private navigateToPrevious(): void {
    if (this.navigationComponent) {
      // 这里可以实现导航到上一个停留点的逻辑
      console.log('导航到上一个点');
    }
  }

  /**
   * 重复最后的响应
   */
  private async repeatLastResponse(): Promise<void> {
    // 这里可以实现重复最后响应的逻辑
    console.log('重复最后的响应');
  }

  /**
   * 语音错误提示
   */
  private async speakError(message: string): Promise<void> {
    this.facialController.setExpression(FacialExpression.CONFUSED, 0.7);

    if (this.config.enableVoiceOutput) {
      await this.speechSynthesis.speak(message, EmotionType.NEGATIVE, 0.5);
    }
  }

  /**
   * 开始监听
   */
  public startListening(): void {
    if (this.config.enableVoiceInput && this.state === DigitalHumanState.IDLE) {
      this.speechRecognition.start();
    }
  }

  /**
   * 停止监听
   */
  public stopListening(): void {
    this.speechRecognition.stop();
  }

  /**
   * 设置状态
   */
  private setState(newState: DigitalHumanState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;

      console.log(`数字人状态变化: ${oldState} -> ${newState}`);

      if (this.onStateChanged) {
        this.onStateChanged(newState);
      }

      // 根据状态播放相应动画
      this.updateAnimationForState(newState);
    }
  }

  /**
   * 根据状态更新动画
   */
  private updateAnimationForState(state: DigitalHumanState): void {
    switch (state) {
      case DigitalHumanState.IDLE:
        this.animationController.playAnimation('idle', true);
        this.facialController.setExpression(FacialExpression.NEUTRAL);
        break;
      case DigitalHumanState.LISTENING:
        this.facialController.setExpression(FacialExpression.NEUTRAL, 0.8);
        break;
      case DigitalHumanState.THINKING:
        this.animationController.playAnimation('think', true);
        this.facialController.setExpression(FacialExpression.THINKING, 0.6);
        break;
      case DigitalHumanState.SPEAKING:
        this.facialController.setExpression(FacialExpression.HAPPY, 0.5);
        break;
      case DigitalHumanState.ERROR:
        this.facialController.setExpression(FacialExpression.CONFUSED, 0.8);
        break;
    }
  }

  /**
   * 开始空闲计时器
   */
  private startIdleTimer(): void {
    setInterval(() => {
      this.idleTimer += 1000; // 每秒增加1000ms

      if (this.idleTimer >= this.config.idleTimeout && this.state === DigitalHumanState.IDLE) {
        this.handleIdleTimeout();
      }
    }, 1000);
  }

  /**
   * 处理空闲超时
   */
  private handleIdleTimeout(): void {
    console.log('数字人空闲超时');

    // 播放空闲动画或提示
    const idleMessages = [
      '有什么我可以帮助您的吗？',
      '您可以问我关于展品的问题。',
      '需要我为您介绍这里的展品吗？'
    ];

    const randomMessage = idleMessages[Math.floor(Math.random() * idleMessages.length)];

    if (this.config.enableVoiceOutput) {
      this.speechSynthesis.speak(randomMessage, EmotionType.NEUTRAL, 0.7);
    }

    this.resetIdleTimer();
  }

  /**
   * 重置空闲计时器
   */
  private resetIdleTimer(): void {
    this.idleTimer = 0;
  }

  /**
   * 更新最后交互时间
   */
  private updateLastInteractionTime(): void {
    this.lastInteractionTime = new Date();
    this.resetIdleTimer();
  }

  /**
   * 获取当前位置
   */
  private getCurrentLocation(): string {
    // 这里可以实现获取当前位置的逻辑
    return '展厅中央';
  }

  /**
   * 获取用户画像
   */
  private getUserProfile(): any {
    // 这里可以实现获取用户画像的逻辑
    return {
      id: 'default-user',
      preferences: ['历史', '艺术'],
      interests: ['文化', '科技'],
      language: 'zh',
      expertiseLevel: 'intermediate',
      visitHistory: []
    };
  }

  /**
   * 设置模型
   */
  public setModel(model: THREE.Object3D): void {
    this.animationController.setModel(model);
    this.facialController.setModel(model);
  }

  /**
   * 获取当前状态
   */
  public getState(): DigitalHumanState {
    return this.state;
  }

  /**
   * 获取配置
   */
  public getConfig(): InteractionConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<InteractionConfig>): void {
    Object.assign(this.config, config);
  }

  /**
   * 组件更新
   */
  public update(deltaTime: number): void {
    this.animationController.update(deltaTime);
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): any {
    return {
      state: this.state,
      lastInteractionTime: this.lastInteractionTime,
      idleTime: this.idleTimer,
      currentAnimation: this.animationController.getCurrentAnimation(),
      currentExpression: this.facialController.getCurrentExpression(),
      speechRecognitionStats: this.speechRecognition.getStatistics(),
      speechSynthesisStats: this.speechSynthesis.getStatistics(),
      config: this.getConfig()
    };
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    this.stopListening();
    this.speechSynthesis.stop();

    // 清理事件回调
    this.onStateChanged = undefined;
    this.onInteractionEvent = undefined;
    this.onResponse = undefined;

    console.log('数字人交互控制器已销毁');
  }
}
