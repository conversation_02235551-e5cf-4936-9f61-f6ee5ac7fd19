import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../cache/cache.service';
import { EmbeddingService } from '../embedding/embedding.service';
import { VectorSearchService } from '../vector-search/vector-search.service';
import { BindingService } from '../binding/binding.service';
import { LLMService } from '../llm/llm.service';

export interface RAGQuery {
  question: string;
  digitalHumanId: string;
  sessionId?: string;
  context?: string;
  maxResults?: number;
  temperature?: number;
  language?: string;
}

export interface RAGResponse {
  answer: string;
  sources: SearchResult[];
  confidence: number;
  responseTime: number;
  sessionId: string;
  metadata: {
    knowledgeBasesUsed: string[];
    totalChunks: number;
    llmModel: string;
    processingSteps: string[];
  };
}

export interface SearchResult {
  content: string;
  score: number;
  source: {
    documentId: string;
    filename: string;
    knowledgeBaseId: string;
    knowledgeBaseName: string;
    chunkIndex: number;
  };
  metadata: any;
}

export interface KnowledgeBaseSearchResult {
  knowledgeBaseId: string;
  knowledgeBaseName: string;
  results: SearchResult[];
  searchTime: number;
  totalResults: number;
}

@Injectable()
export class RAGService {
  constructor(
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly embeddingService: EmbeddingService,
    private readonly vectorSearchService: VectorSearchService,
    private readonly bindingService: BindingService,
    private readonly llmService: LLMService,
  ) {}

  /**
   * 执行RAG查询
   */
  async query(query: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();
    const sessionId = query.sessionId || this.generateSessionId();
    const processingSteps: string[] = [];

    try {
      processingSteps.push('开始RAG查询');

      // 1. 获取数字人绑定的知识库
      const knowledgeBases = await this.getDigitalHumanKnowledgeBases(query.digitalHumanId);
      processingSteps.push(`获取到 ${knowledgeBases.length} 个绑定的知识库`);

      if (knowledgeBases.length === 0) {
        throw new Error('该数字人未绑定任何知识库');
      }

      // 2. 生成查询向量
      const queryEmbedding = await this.generateQueryEmbedding(query.question);
      processingSteps.push('生成查询向量');

      // 3. 并行搜索多个知识库
      const searchResults = await this.parallelSearch(
        queryEmbedding,
        knowledgeBases,
        query.maxResults || 10,
      );
      processingSteps.push(`并行搜索完成，共找到 ${searchResults.reduce((sum, r) => sum + r.results.length, 0)} 个相关片段`);

      // 4. 合并和排序结果
      const mergedResults = await this.mergeAndRankResults(searchResults, query);
      processingSteps.push(`结果合并排序完成，保留前 ${mergedResults.length} 个结果`);

      // 5. 构建上下文
      const context = this.buildContext(mergedResults, query.context);
      processingSteps.push('构建上下文完成');

      // 6. 生成回答
      const answer = await this.generateAnswer(query.question, context, query);
      processingSteps.push('生成回答完成');

      // 7. 计算置信度
      const confidence = this.calculateConfidence(mergedResults, answer);
      processingSteps.push('计算置信度完成');

      const responseTime = Date.now() - startTime;

      const response: RAGResponse = {
        answer,
        sources: mergedResults,
        confidence,
        responseTime,
        sessionId,
        metadata: {
          knowledgeBasesUsed: knowledgeBases.map(kb => kb.knowledgeBase.id),
          totalChunks: mergedResults.length,
          llmModel: this.llmService.getModelName(),
          processingSteps,
        },
      };

      // 缓存会话上下文
      await this.cacheSessionContext(sessionId, query, response);

      return response;

    } catch (error) {
      console.error('RAG查询失败:', error);
      throw new Error(`RAG查询失败: ${error.message}`);
    }
  }

  /**
   * 获取数字人绑定的知识库
   */
  private async getDigitalHumanKnowledgeBases(digitalHumanId: string): Promise<any[]> {
    const cacheKey = `digital_human:${digitalHumanId}:knowledge_bases`;
    let knowledgeBases = await this.cacheService.get(cacheKey);

    if (!knowledgeBases) {
      knowledgeBases = await this.bindingService.getDigitalHumanKnowledgeBases(digitalHumanId);
      await this.cacheService.set(cacheKey, knowledgeBases, 3600);
    }

    return knowledgeBases.filter(kb => kb.config?.isActive !== false);
  }

  /**
   * 生成查询向量
   */
  private async generateQueryEmbedding(question: string): Promise<number[]> {
    const result = await this.embeddingService.generateEmbedding(question);
    return result.embedding;
  }

  /**
   * 并行搜索多个知识库
   */
  private async parallelSearch(
    queryEmbedding: number[],
    knowledgeBases: any[],
    maxResults: number,
  ): Promise<KnowledgeBaseSearchResult[]> {
    const searchPromises = knowledgeBases.map(async (kb) => {
      const startTime = Date.now();
      
      try {
        const results = await this.vectorSearchService.search({
          vector: queryEmbedding,
          namespace: kb.knowledgeBase.id,
          topK: Math.ceil(maxResults / knowledgeBases.length) + 5, // 每个知识库多搜索一些
          includeMetadata: true,
          filter: {
            knowledge_base_id: kb.knowledgeBase.id,
          },
        });

        const searchTime = Date.now() - startTime;

        return {
          knowledgeBaseId: kb.knowledgeBase.id,
          knowledgeBaseName: kb.knowledgeBase.name,
          results: results.matches.map(match => ({
            content: match.metadata.content,
            score: match.score,
            source: {
              documentId: match.metadata.document_id,
              filename: match.metadata.filename,
              knowledgeBaseId: kb.knowledgeBase.id,
              knowledgeBaseName: kb.knowledgeBase.name,
              chunkIndex: match.metadata.chunk_index,
            },
            metadata: match.metadata,
          })),
          searchTime,
          totalResults: results.matches.length,
        };
      } catch (error) {
        console.error(`搜索知识库 ${kb.knowledgeBase.id} 失败:`, error);
        return {
          knowledgeBaseId: kb.knowledgeBase.id,
          knowledgeBaseName: kb.knowledgeBase.name,
          results: [],
          searchTime: Date.now() - startTime,
          totalResults: 0,
        };
      }
    });

    return await Promise.all(searchPromises);
  }

  /**
   * 合并和排序结果
   */
  private async mergeAndRankResults(
    searchResults: KnowledgeBaseSearchResult[],
    query: RAGQuery,
  ): Promise<SearchResult[]> {
    // 合并所有结果
    const allResults: SearchResult[] = [];
    
    for (const kbResult of searchResults) {
      allResults.push(...kbResult.results);
    }

    // 去重（基于内容相似度）
    const uniqueResults = await this.deduplicateResults(allResults);

    // 重新排序
    const rankedResults = await this.rankResults(uniqueResults, query);

    // 返回前N个结果
    const maxResults = query.maxResults || 10;
    return rankedResults.slice(0, maxResults);
  }

  /**
   * 去重结果
   */
  private async deduplicateResults(results: SearchResult[]): Promise<SearchResult[]> {
    const uniqueResults: SearchResult[] = [];
    const contentHashes = new Set<string>();

    for (const result of results) {
      // 简单的内容哈希去重
      const contentHash = this.hashContent(result.content);
      
      if (!contentHashes.has(contentHash)) {
        contentHashes.add(contentHash);
        uniqueResults.push(result);
      }
    }

    return uniqueResults;
  }

  /**
   * 重新排序结果
   */
  private async rankResults(results: SearchResult[], query: RAGQuery): Promise<SearchResult[]> {
    // 综合排序算法：相似度分数 + 知识库权重 + 内容质量
    return results.sort((a, b) => {
      // 基础相似度分数
      let scoreA = a.score;
      let scoreB = b.score;

      // 知识库权重加成（主要知识库权重更高）
      const kbWeightA = this.getKnowledgeBaseWeight(a.source.knowledgeBaseId);
      const kbWeightB = this.getKnowledgeBaseWeight(b.source.knowledgeBaseId);
      
      scoreA *= kbWeightA;
      scoreB *= kbWeightB;

      // 内容质量加成（长度适中的内容权重更高）
      const qualityA = this.calculateContentQuality(a.content);
      const qualityB = this.calculateContentQuality(b.content);
      
      scoreA *= qualityA;
      scoreB *= qualityB;

      return scoreB - scoreA;
    });
  }

  /**
   * 获取知识库权重
   */
  private getKnowledgeBaseWeight(knowledgeBaseId: string): number {
    // 这里可以根据知识库的重要性、质量等因素设置权重
    // 暂时返回默认权重
    return 1.0;
  }

  /**
   * 计算内容质量
   */
  private calculateContentQuality(content: string): number {
    const length = content.length;
    
    // 长度在100-500字符之间的内容质量最高
    if (length >= 100 && length <= 500) {
      return 1.2;
    } else if (length >= 50 && length <= 800) {
      return 1.0;
    } else {
      return 0.8;
    }
  }

  /**
   * 构建上下文
   */
  private buildContext(results: SearchResult[], additionalContext?: string): string {
    let context = '';
    
    if (additionalContext) {
      context += `背景信息：${additionalContext}\n\n`;
    }
    
    context += '相关知识：\n';
    
    results.forEach((result, index) => {
      context += `${index + 1}. ${result.content}\n`;
      context += `   来源：${result.source.filename} (${result.source.knowledgeBaseName})\n\n`;
    });

    return context;
  }

  /**
   * 生成回答
   */
  private async generateAnswer(
    question: string,
    context: string,
    query: RAGQuery,
  ): Promise<string> {
    const prompt = this.buildPrompt(question, context, query);
    
    return await this.llmService.generateResponse(prompt, {
      temperature: query.temperature || 0.7,
      maxTokens: 1000,
      language: query.language || 'zh-CN',
    });
  }

  /**
   * 构建提示词
   */
  private buildPrompt(question: string, context: string, query: RAGQuery): string {
    return `你是一个专业的AI助手，请基于以下知识内容回答用户的问题。

${context}

用户问题：${question}

请要求：
1. 基于提供的知识内容进行回答
2. 回答要准确、详细且有条理
3. 如果知识内容不足以回答问题，请诚实说明
4. 使用${query.language || '中文'}回答

回答：`;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(results: SearchResult[], answer: string): number {
    if (results.length === 0) {
      return 0;
    }

    // 基于搜索结果的平均分数和数量计算置信度
    const avgScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
    const resultCountFactor = Math.min(results.length / 5, 1); // 结果数量因子
    const answerLengthFactor = Math.min(answer.length / 200, 1); // 回答长度因子

    return Math.min(avgScore * resultCountFactor * answerLengthFactor, 1);
  }

  /**
   * 缓存会话上下文
   */
  private async cacheSessionContext(
    sessionId: string,
    query: RAGQuery,
    response: RAGResponse,
  ): Promise<void> {
    const sessionData = {
      lastQuery: query,
      lastResponse: response,
      timestamp: new Date().toISOString(),
    };

    await this.cacheService.set(`session:${sessionId}`, sessionData, 3600);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算内容哈希
   */
  private hashContent(content: string): string {
    // 简单的字符串哈希
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 流式查询
   */
  async queryStream(query: RAGQuery): Promise<AsyncGenerator<string, void, unknown>> {
    const sessionId = query.sessionId || this.generateSessionId();

    // 获取知识库和搜索结果
    const knowledgeBases = await this.getDigitalHumanKnowledgeBases(query.digitalHumanId);
    const queryEmbedding = await this.generateQueryEmbedding(query.question);
    const searchResults = await this.parallelSearch(queryEmbedding, knowledgeBases, query.maxResults || 10);
    const mergedResults = await this.mergeAndRankResults(searchResults, query);
    const context = this.buildContext(mergedResults, query.context);

    // 构建提示词
    const prompt = this.buildPrompt(query.question, context, query);

    // 返回流式生成器
    return this.llmService.generateStreamResponse(prompt, {
      temperature: query.temperature || 0.7,
      maxTokens: 1000,
      language: query.language || 'zh-CN',
    });
  }

  /**
   * 仅搜索（不生成回答）
   */
  async searchOnly(query: { question: string; digitalHumanId: string; maxResults?: number; threshold?: number }): Promise<any> {
    const startTime = Date.now();

    const knowledgeBases = await this.getDigitalHumanKnowledgeBases(query.digitalHumanId);
    const queryEmbedding = await this.generateQueryEmbedding(query.question);
    const searchResults = await this.parallelSearch(queryEmbedding, knowledgeBases, query.maxResults || 10);
    const mergedResults = await this.mergeAndRankResults(searchResults, { ...query, digitalHumanId: query.digitalHumanId });

    // 应用阈值过滤
    const filteredResults = query.threshold
      ? mergedResults.filter(r => r.score >= query.threshold)
      : mergedResults;

    return {
      results: filteredResults,
      searchTime: Date.now() - startTime,
      totalResults: filteredResults.length,
    };
  }

  /**
   * 获取会话历史
   */
  async getSessionHistory(sessionId: string): Promise<any> {
    const sessionData = await this.cacheService.get(`session:${sessionId}`);

    if (!sessionData) {
      return {
        sessionId,
        history: [],
      };
    }

    // 这里可以扩展为完整的会话历史管理
    return {
      sessionId,
      history: [
        {
          question: sessionData.lastQuery?.question,
          answer: sessionData.lastResponse?.answer,
          timestamp: sessionData.timestamp,
          confidence: sessionData.lastResponse?.confidence,
        },
      ],
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    const status = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        vectorDatabase: 'ok',
        llm: 'ok',
        cache: 'ok',
      },
    };

    try {
      // 检查向量数据库
      await this.vectorSearchService.getSearchStats('test');
    } catch (error) {
      status.services.vectorDatabase = 'error';
      status.status = 'degraded';
    }

    try {
      // 检查LLM服务
      await this.llmService.generateResponse('test', { maxTokens: 1 });
    } catch (error) {
      status.services.llm = 'error';
      status.status = 'degraded';
    }

    try {
      // 检查缓存服务
      await this.cacheService.set('health_check', 'ok', 10);
      await this.cacheService.get('health_check');
    } catch (error) {
      status.services.cache = 'error';
      status.status = 'degraded';
    }

    return status;
  }
}
