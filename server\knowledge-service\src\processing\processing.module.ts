import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { KnowledgeDocument, DocumentChunk } from '../entities';
import { StorageModule } from '../storage/storage.module';
import { CacheModule } from '../cache/cache.module';
import { DocumentParserService } from './document-parser.service';
import { DocumentChunkerService } from './document-chunker.service';
import { EmbeddingService } from './embedding.service';
import { VectorDatabaseService } from './vector-database.service';
import { DocumentProcessingService } from './document-processing.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeDocument, DocumentChunk]),
    ConfigModule,
    StorageModule,
    CacheModule,
  ],
  providers: [
    DocumentParserService,
    DocumentChunkerService,
    EmbeddingService,
    VectorDatabaseService,
    DocumentProcessingService,
  ],
  exports: [
    DocumentParserService,
    DocumentChunkerService,
    EmbeddingService,
    VectorDatabaseService,
    DocumentProcessingService,
  ],
})
export class ProcessingModule {}
