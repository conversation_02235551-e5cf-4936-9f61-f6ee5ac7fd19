/**
 * 数字人文件格式定义
 * 定义自定义数字人文件格式的结构和规范
 */

/**
 * 数字人文件版本
 */
export const DIGITAL_HUMAN_FORMAT_VERSION = '1.0.0';

/**
 * 数字人元数据
 */
export interface DigitalHumanMetadata {
  /** 数字人名称 */
  name: string;
  /** 创建者 */
  creator: string;
  /** 创建时间 */
  createdAt: string;
  /** 修改时间 */
  modifiedAt: string;
  /** 版本 */
  version: string;
  /** 描述 */
  description?: string;
  /** 标签 */
  tags?: string[];
  /** 许可证类型 */
  license?: string;
  /** 缩略图URL */
  thumbnail?: string;
}

/**
 * 模型数据
 */
export interface ModelData {
  /** 模型格式 */
  format: 'gltf' | 'glb' | 'fbx' | 'obj';
  /** 模型URL或内嵌数据 */
  data: string | ArrayBuffer;
  /** 材质列表 */
  materials?: MaterialData[];
  /** 纹理列表 */
  textures?: TextureData[];
  /** 网格信息 */
  meshes?: MeshData[];
}

/**
 * 材质数据
 */
export interface MaterialData {
  /** 材质名称 */
  name: string;
  /** 材质类型 */
  type: 'standard' | 'pbr' | 'toon' | 'custom';
  /** 材质属性 */
  properties: {
    /** 基础颜色 */
    baseColor?: [number, number, number, number];
    /** 金属度 */
    metallic?: number;
    /** 粗糙度 */
    roughness?: number;
    /** 法线强度 */
    normalScale?: number;
    /** 自发光颜色 */
    emissive?: [number, number, number];
    /** 透明度 */
    opacity?: number;
    /** 双面渲染 */
    doubleSided?: boolean;
  };
  /** 纹理映射 */
  textureMaps?: {
    /** 漫反射贴图 */
    diffuse?: string;
    /** 法线贴图 */
    normal?: string;
    /** 金属度贴图 */
    metallic?: string;
    /** 粗糙度贴图 */
    roughness?: string;
    /** 环境遮蔽贴图 */
    ao?: string;
    /** 自发光贴图 */
    emissive?: string;
  };
}

/**
 * 纹理数据
 */
export interface TextureData {
  /** 纹理名称 */
  name: string;
  /** 纹理URL或内嵌数据 */
  data: string | ArrayBuffer;
  /** 纹理格式 */
  format: 'jpg' | 'png' | 'webp' | 'ktx2' | 'dds';
  /** 纹理尺寸 */
  size: [number, number];
  /** 是否生成Mipmap */
  generateMipmaps?: boolean;
  /** 包装模式 */
  wrapS?: 'repeat' | 'clamp' | 'mirror';
  /** 包装模式 */
  wrapT?: 'repeat' | 'clamp' | 'mirror';
  /** 过滤模式 */
  minFilter?: 'nearest' | 'linear' | 'nearestMipmap' | 'linearMipmap';
  /** 过滤模式 */
  magFilter?: 'nearest' | 'linear';
}

/**
 * 网格数据
 */
export interface MeshData {
  /** 网格名称 */
  name: string;
  /** 顶点数量 */
  vertexCount: number;
  /** 面数量 */
  faceCount: number;
  /** 材质索引 */
  materialIndex?: number;
  /** 骨骼权重信息 */
  skinning?: SkinningData;
}

/**
 * 蒙皮数据
 */
export interface SkinningData {
  /** 骨骼索引 */
  boneIndices: number[];
  /** 骨骼权重 */
  boneWeights: number[];
  /** 绑定矩阵 */
  bindMatrices: number[][];
}

/**
 * 骨骼数据
 */
export interface SkeletonData {
  /** 骨骼列表 */
  bones: BoneData[];
  /** 根骨骼索引 */
  rootBoneIndex: number;
  /** 骨骼层级 */
  hierarchy: BoneHierarchy;
}

/**
 * 骨骼数据
 */
export interface BoneData {
  /** 骨骼名称 */
  name: string;
  /** 骨骼索引 */
  index: number;
  /** 父骨骼索引 */
  parentIndex: number;
  /** 本地变换矩阵 */
  localMatrix: number[];
  /** 世界变换矩阵 */
  worldMatrix: number[];
  /** 绑定姿势矩阵 */
  bindMatrix: number[];
  /** 子骨骼索引列表 */
  children: number[];
}

/**
 * 骨骼层级
 */
export interface BoneHierarchy {
  /** 深度映射 */
  depthMap: Record<number, number>;
  /** 最大深度 */
  maxDepth: number;
}

/**
 * 动画数据
 */
export interface AnimationData {
  /** 动画列表 */
  animations: AnimationClipData[];
  /** 默认动画 */
  defaultAnimation?: string;
}

/**
 * 动画片段数据
 */
export interface AnimationClipData {
  /** 动画名称 */
  name: string;
  /** 持续时间 */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 是否循环 */
  loop: boolean;
  /** 动画轨道 */
  tracks: AnimationTrackData[];
}

/**
 * 动画轨道数据
 */
export interface AnimationTrackData {
  /** 目标骨骼名称 */
  boneName: string;
  /** 轨道类型 */
  type: 'position' | 'rotation' | 'scale';
  /** 关键帧数据 */
  keyframes: KeyframeData[];
  /** 插值类型 */
  interpolation: 'linear' | 'step' | 'cubic';
}

/**
 * 关键帧数据
 */
export interface KeyframeData {
  /** 时间 */
  time: number;
  /** 值 */
  value: number[];
  /** 输入切线 */
  inTangent?: number[];
  /** 输出切线 */
  outTangent?: number[];
}

/**
 * 表情数据
 */
export interface FacialData {
  /** 表情混合形状 */
  blendShapes: BlendShapeData[];
  /** 表情预设 */
  presets: FacialPresetData[];
}

/**
 * 混合形状数据
 */
export interface BlendShapeData {
  /** 名称 */
  name: string;
  /** 索引 */
  index: number;
  /** 默认权重 */
  defaultWeight: number;
  /** 最小权重 */
  minWeight: number;
  /** 最大权重 */
  maxWeight: number;
}

/**
 * 表情预设数据
 */
export interface FacialPresetData {
  /** 预设名称 */
  name: string;
  /** 混合形状权重 */
  weights: Record<string, number>;
}

/**
 * 服装数据
 */
export interface ClothingData {
  /** 服装列表 */
  items: ClothingItemData[];
  /** 默认服装组合 */
  defaultOutfit?: string[];
}

/**
 * 服装项目数据
 */
export interface ClothingItemData {
  /** 服装名称 */
  name: string;
  /** 服装类型 */
  type: 'top' | 'bottom' | 'shoes' | 'accessory' | 'hair';
  /** 服装槽位 */
  slot: string;
  /** 模型数据 */
  model: ModelData;
  /** 物理属性 */
  physics?: ClothingPhysicsData;
}

/**
 * 服装物理数据
 */
export interface ClothingPhysicsData {
  /** 质量 */
  mass: number;
  /** 阻尼 */
  damping: number;
  /** 弹性 */
  elasticity: number;
  /** 约束 */
  constraints: PhysicsConstraintData[];
}

/**
 * 物理约束数据
 */
export interface PhysicsConstraintData {
  /** 约束类型 */
  type: 'distance' | 'angle' | 'collision';
  /** 约束参数 */
  parameters: Record<string, number>;
}

/**
 * 完整的数字人文件格式
 */
export interface DigitalHumanFile {
  /** 格式版本 */
  version: string;
  /** 元数据 */
  metadata: DigitalHumanMetadata;
  /** 模型数据 */
  model: ModelData;
  /** 骨骼数据 */
  skeleton?: SkeletonData;
  /** 动画数据 */
  animations?: AnimationData;
  /** 表情数据 */
  facial?: FacialData;
  /** 服装数据 */
  clothing?: ClothingData;
  /** 扩展数据 */
  extensions?: Record<string, any>;
}

/**
 * 数字人文件验证器
 */
export class DigitalHumanFileValidator {
  /**
   * 验证数字人文件
   * @param data 文件数据
   * @returns 验证结果
   */
  public static validate(data: any): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查版本
    if (!data.version) {
      errors.push('缺少版本信息');
    } else if (data.version !== DIGITAL_HUMAN_FORMAT_VERSION) {
      warnings.push(`版本不匹配，期望 ${DIGITAL_HUMAN_FORMAT_VERSION}，实际 ${data.version}`);
    }

    // 检查元数据
    if (!data.metadata) {
      errors.push('缺少元数据');
    } else {
      if (!data.metadata.name) {
        errors.push('缺少数字人名称');
      }
      if (!data.metadata.creator) {
        warnings.push('缺少创建者信息');
      }
    }

    // 检查模型数据
    if (!data.model) {
      errors.push('缺少模型数据');
    } else {
      if (!data.model.format) {
        errors.push('缺少模型格式信息');
      }
      if (!data.model.data) {
        errors.push('缺少模型数据');
      }
    }

    // 检查骨骼数据（可选）
    if (data.skeleton) {
      if (!data.skeleton.bones || !Array.isArray(data.skeleton.bones)) {
        errors.push('骨骼数据格式错误');
      }
    }

    // 检查动画数据（可选）
    if (data.animations) {
      if (!data.animations.animations || !Array.isArray(data.animations.animations)) {
        errors.push('动画数据格式错误');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 创建默认数字人文件
   * @param name 数字人名称
   * @param creator 创建者
   * @returns 默认文件结构
   */
  public static createDefault(name: string, creator: string): DigitalHumanFile {
    const now = new Date().toISOString();

    return {
      version: DIGITAL_HUMAN_FORMAT_VERSION,
      metadata: {
        name,
        creator,
        createdAt: now,
        modifiedAt: now,
        version: '1.0.0',
        description: '默认数字人',
        tags: [],
        license: 'private'
      },
      model: {
        format: 'gltf',
        data: '',
        materials: [],
        textures: [],
        meshes: []
      }
    };
  }
}
