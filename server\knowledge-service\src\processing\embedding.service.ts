import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface EmbeddingModel {
  embed(text: string): Promise<number[]>;
  embedBatch(texts: string[]): Promise<number[][]>;
  getDimension(): number;
}

export interface EmbeddingResult {
  embedding: number[];
  text: string;
  tokenCount: number;
}

@Injectable()
export class EmbeddingService {
  private model: EmbeddingModel;

  constructor(private readonly configService: ConfigService) {
    this.initializeModel();
  }

  /**
   * 初始化嵌入模型
   */
  private initializeModel(): void {
    const modelType = this.configService.get('embedding.model', 'openai');
    
    switch (modelType) {
      case 'openai':
        this.model = new OpenAIEmbeddingModel(this.configService);
        break;
      case 'sentence-bert':
        this.model = new SentenceBertEmbeddingModel(this.configService);
        break;
      case 'local':
        this.model = new LocalEmbeddingModel(this.configService);
        break;
      default:
        throw new Error(`不支持的嵌入模型: ${modelType}`);
    }
  }

  /**
   * 生成单个文本的嵌入
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    if (!text || text.trim().length === 0) {
      throw new Error('文本内容为空');
    }

    const cleanText = this.preprocessText(text);
    const embedding = await this.model.embed(cleanText);
    
    return {
      embedding,
      text: cleanText,
      tokenCount: this.estimateTokenCount(cleanText),
    };
  }

  /**
   * 批量生成嵌入
   */
  async generateEmbeddings(texts: string[]): Promise<EmbeddingResult[]> {
    if (!texts || texts.length === 0) {
      throw new Error('文本列表为空');
    }

    const cleanTexts = texts.map(text => this.preprocessText(text));
    const embeddings = await this.model.embedBatch(cleanTexts);
    
    return embeddings.map((embedding, index) => ({
      embedding,
      text: cleanTexts[index],
      tokenCount: this.estimateTokenCount(cleanTexts[index]),
    }));
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n+/g, ' ') // 替换换行符
      .substring(0, 8000); // 限制长度
  }

  /**
   * 估算token数量
   */
  private estimateTokenCount(text: string): number {
    // 简单估算：中文按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }

  /**
   * 获取嵌入维度
   */
  getDimension(): number {
    return this.model.getDimension();
  }

  /**
   * 计算余弦相似度
   */
  calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('嵌入向量维度不匹配');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }
}

/**
 * OpenAI嵌入模型
 */
class OpenAIEmbeddingModel implements EmbeddingModel {
  private apiKey: string;
  private model: string;
  private dimension: number;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('openai.apiKey');
    this.model = configService.get('openai.embeddingModel', 'text-embedding-ada-002');
    this.dimension = 1536; // text-embedding-ada-002的维度
  }

  async embed(text: string): Promise<number[]> {
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        input: text,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data[0].embedding;
  }

  async embedBatch(texts: string[]): Promise<number[][]> {
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        input: texts,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data.map((item: any) => item.embedding);
  }

  getDimension(): number {
    return this.dimension;
  }
}

/**
 * Sentence-BERT嵌入模型
 */
class SentenceBertEmbeddingModel implements EmbeddingModel {
  private apiUrl: string;
  private dimension: number;

  constructor(configService: ConfigService) {
    this.apiUrl = configService.get('sentenceBert.apiUrl');
    this.dimension = configService.get('sentenceBert.dimension', 768);
  }

  async embed(text: string): Promise<number[]> {
    const response = await fetch(`${this.apiUrl}/embed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      throw new Error(`Sentence-BERT API错误: ${response.statusText}`);
    }

    const data = await response.json();
    return data.embedding;
  }

  async embedBatch(texts: string[]): Promise<number[][]> {
    const response = await fetch(`${this.apiUrl}/embed_batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ texts }),
    });

    if (!response.ok) {
      throw new Error(`Sentence-BERT API错误: ${response.statusText}`);
    }

    const data = await response.json();
    return data.embeddings;
  }

  getDimension(): number {
    return this.dimension;
  }
}

/**
 * 本地嵌入模型（示例）
 */
class LocalEmbeddingModel implements EmbeddingModel {
  private dimension: number;

  constructor(configService: ConfigService) {
    this.dimension = configService.get('local.dimension', 512);
  }

  async embed(text: string): Promise<number[]> {
    // 这里应该调用本地模型
    // 暂时返回随机向量作为示例
    return Array.from({ length: this.dimension }, () => Math.random() - 0.5);
  }

  async embedBatch(texts: string[]): Promise<number[][]> {
    return Promise.all(texts.map(text => this.embed(text)));
  }

  getDimension(): number {
    return this.dimension;
  }
}
