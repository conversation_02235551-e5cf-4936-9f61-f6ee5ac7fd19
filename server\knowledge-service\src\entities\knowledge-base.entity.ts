import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { KnowledgeDocument } from './knowledge-document.entity';
import { DigitalHumanKnowledgeBinding } from './digital-human-knowledge-binding.entity';

@Entity('knowledge_bases')
export class KnowledgeBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  category: string;

  @Column({ type: 'varchar', length: 10, default: 'zh-CN' })
  language: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  vectorIndexName: string;

  @Column({ type: 'integer', default: 0 })
  documentCount: number;

  @Column({ type: 'integer', default: 0 })
  totalChunks: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 50, default: 'active' })
  status: string;

  @OneToMany(() => KnowledgeDocument, (document) => document.knowledgeBase)
  documents: KnowledgeDocument[];

  @OneToMany(
    () => DigitalHumanKnowledgeBinding,
    (binding) => binding.knowledgeBase,
  )
  digitalHumanBindings: DigitalHumanKnowledgeBinding[];
}
