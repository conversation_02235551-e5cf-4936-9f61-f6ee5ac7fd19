/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { AIModelFactory } from './AIModelFactory';
import { AIModelLoadOptions } from './AIModelLoadOptions';
import { IAIModel } from './models/IAIModel';
import { GPTModel } from './models/GPTModel';
import { StableDiffusionModel } from './models/StableDiffusionModel';
import { BERTModel } from './models/BERTModel';
import { RoBERTaModel } from './models/RoBERTaModel';
import { DistilBERTModel } from './models/DistilBERTModel';
import { ALBERTModel } from './models/ALBERTModel';
import { XLNetModel } from './models/XLNetModel';
import { BARTModel } from './models/BARTModel';
import { T5Model } from './models/T5Model';
import { SceneUnderstandingModel } from './scene/SceneUnderstandingModel';
import { LayoutGenerationModel } from './scene/LayoutGenerationModel';
import { AssetMatchingModel } from './scene/AssetMatchingModel';

/**
 * AI模型管理器配置
 */
export interface AIModelManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 模型缓存大小 */
  cacheSize?: number;
  /** 是否使用本地模型 */
  useLocalModels?: boolean;
  /** 模型API密钥 */
  apiKeys?: Record<string, string>;
  /** 模型基础URL */
  baseUrls?: Record<string, string>;
  /** 模型版本 */
  modelVersions?: Record<string, string>;
}

/**
 * AI模型管理器
 * 负责加载、管理和使用AI模型
 */
export class AIModelManager extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 50;

  /** 配置 */
  private config: AIModelManagerConfig;



  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AIModelManagerConfig = {
    debug: false,
    cacheSize: 5,
    useLocalModels: false,
    apiKeys: {},
    baseUrls: {},
    modelVersions: {}
  };

  /** 已加载的模型 */
  private loadedModels: Map<string, IAIModel> = new Map();

  /** 模型加载进度 */
  private modelLoadProgress: Map<string, number> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 模型工厂 */
  private modelFactory!: AIModelFactory;

  /** 模型加载队列 */
  private loadQueue: Array<{
    modelType: AIModelType;
    config: AIModelConfig;
    options?: AIModelLoadOptions;
    resolve: (model: IAIModel | null) => void;
    reject: (error: Error) => void;
  }> = [];

  /** 是否正在加载模型 */
  private isLoading: boolean = false;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: AIModelManagerConfig = {}) {
    // 使用系统优先级作为参数
    super(AIModelManager.PRIORITY);

    // 设置世界引用
    this.setWorld(world);

    // 合并配置
    this.config = {
      ...AIModelManager.DEFAULT_CONFIG,
      ...config
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('初始化AI模型管理器');
      console.log(`世界实例: ${this.world ? '已设置' : '未设置'}`);
    }

    // 创建模型工厂
    this.modelFactory = new AIModelFactory(this.config);

    // 初始化事件发射器
    this.eventEmitter = new EventEmitter();
  }

  /**
   * 加载模型
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 加载选项
   * @returns 模型实例
   */
  public async loadModel(
    modelType: AIModelType,
    config: AIModelConfig = {},
    options: AIModelLoadOptions = {}
  ): Promise<IAIModel | null> {
    // 生成模型ID
    const modelId = this.generateModelId(modelType, config);

    // 检查模型是否已加载
    if (this.loadedModels.has(modelId)) {
      if (this.config.debug) {
        console.log(`模型已加载: ${modelId}`);
      }
      return this.loadedModels.get(modelId) || null;
    }

    // 创建加载承诺
    return new Promise<IAIModel | null>((resolve, reject) => {
      // 添加到加载队列
      this.loadQueue.push({
        modelType,
        config,
        options,
        resolve,
        reject
      });

      // 如果没有正在加载的模型，开始加载
      if (!this.isLoading) {
        this.processLoadQueue();
      }
    });
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue(): Promise<void> {
    // 如果队列为空，返回
    if (this.loadQueue.length === 0) {
      this.isLoading = false;
      return;
    }

    // 设置加载状态
    this.isLoading = true;

    // 获取队列中的第一个任务
    const task = this.loadQueue.shift();
    if (!task) {
      this.isLoading = false;
      return;
    }

    try {
      // 生成模型ID
      const modelId = this.generateModelId(task.modelType, task.config);

      // 设置加载进度
      this.modelLoadProgress.set(modelId, 0);
      this.eventEmitter.emit('modelLoadProgress', {
        modelId,
        progress: 0
      });

      // 创建模型实例
      const model = await this.createModelInstance(task.modelType, task.config, task.options);

      // 如果模型创建成功，添加到已加载模型列表
      if (model) {
        this.loadedModels.set(modelId, model);

        // 如果超过缓存大小，移除最早加载的模型
        if (this.loadedModels.size > (this.config.cacheSize || 5)) {
          const firstEntry = this.loadedModels.keys().next();
          if (!firstEntry.done && firstEntry.value) {
            this.loadedModels.delete(firstEntry.value);
          }
        }

        // 设置加载进度为100%
        this.modelLoadProgress.set(modelId, 1);
        this.eventEmitter.emit('modelLoadProgress', {
          modelId,
          progress: 1
        });

        // 触发模型加载完成事件
        this.eventEmitter.emit('modelLoaded', {
          modelId,
          model
        });

        // 解析承诺
        task.resolve(model);
      } else {
        // 如果模型创建失败，触发错误事件
        this.eventEmitter.emit('modelLoadError', {
          modelId,
          error: new Error(`无法创建模型: ${modelId}`)
        });

        // 拒绝承诺
        task.reject(new Error(`无法创建模型: ${modelId}`));
      }
    } catch (error) {
      // 触发错误事件
      this.eventEmitter.emit('modelLoadError', {
        modelId: this.generateModelId(task.modelType, task.config),
        error
      });

      // 拒绝承诺
      task.reject(error as Error);
    } finally {
      // 继续处理队列
      this.processLoadQueue();
    }
  }

  /**
   * 创建模型实例
   * @param modelType 模型类型
   * @param config 模型配置
   * @param options 加载选项
   * @returns 模型实例
   */
  private async createModelInstance(
    modelType: AIModelType,
    config: AIModelConfig,
    options: AIModelLoadOptions = {}
  ): Promise<IAIModel | null> {
    try {
      // 使用加载选项（如果需要）
      if (options.useCache !== undefined) {
        // 这里可以根据需要使用options参数
      }

      // 使用模型工厂创建模型实例
      if (this.modelFactory && options.useFactory !== false) {
        const model = this.modelFactory.createModel(modelType, config);
        if (model) {
          return model;
        }
        // 如果工厂创建失败，回退到直接创建
        if (this.config.debug) {
          console.warn(`模型工厂创建模型失败，回退到直接创建: ${modelType}`);
        }
      }

      // 根据模型类型创建不同的模型实例
      switch (modelType) {
        case AIModelType.GPT:
          return new GPTModel(config, this.config);
        case AIModelType.STABLE_DIFFUSION:
          return new StableDiffusionModel(config, this.config);
        case AIModelType.BERT:
          return new BERTModel(config, this.config);
        case AIModelType.ROBERTA:
          // 转换为RoBERTa特定配置
          return new RoBERTaModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'distilled' | undefined
          }, this.config);
        case AIModelType.DISTILBERT:
          // 转换为DistilBERT特定配置
          return new DistilBERTModel({
            ...config,
            variant: config.variant as 'base' | 'multilingual' | undefined
          }, this.config);
        case AIModelType.ALBERT:
          // 转换为ALBERT特定配置
          return new ALBERTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'xlarge' | 'xxlarge' | undefined
          }, this.config);
        case AIModelType.XLNET:
          // 转换为XLNet特定配置
          return new XLNetModel({
            ...config,
            variant: config.variant as 'base' | 'large' | undefined
          }, this.config);
        case AIModelType.BART:
          // 转换为BART特定配置
          return new BARTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'cnn' | undefined
          }, this.config);
        case AIModelType.T5:
          // 转换为T5特定配置
          return new T5Model({
            ...config,
            variant: config.variant as 'small' | 'base' | 'large' | 'xl' | 'xxl' | undefined
          }, this.config);
        case AIModelType.SCENE_UNDERSTANDING:
          // 场景理解模型
          return new SceneUnderstandingModel(config, this.config);
        case AIModelType.LAYOUT_GENERATION:
          // 布局生成模型
          return new LayoutGenerationModel(config, this.config);
        case AIModelType.ASSET_MATCHING:
          // 资产匹配模型
          return new AssetMatchingModel(config, this.config);
        default:
          console.error(`不支持的模型类型: ${modelType}`);
          return null;
      }
    } catch (error) {
      console.error(`创建模型实例失败: ${error}`);
      return null;
    }
  }

  /**
   * 生成模型ID
   * @param modelType 模型类型
   * @param config 模型配置
   * @returns 模型ID
   */
  private generateModelId(modelType: AIModelType, config: AIModelConfig): string {
    // 基本ID
    let id = `${modelType}`;

    // 添加版本信息
    if (config.version) {
      id += `-${config.version}`;
    } else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
      id += `-${this.config.modelVersions[modelType]}`;
    }

    // 添加其他配置信息
    if (config.variant) {
      id += `-${config.variant}`;
    }

    return id;
  }

  /**
   * 获取已加载的模型
   * @param modelId 模型ID
   * @returns 模型实例
   */
  public getModel(modelId: string): IAIModel | null {
    return this.loadedModels.get(modelId) || null;
  }

  /**
   * 获取所有已加载的模型
   * @returns 模型实例映射
   */
  public getAllModels(): Map<string, IAIModel> {
    return new Map(this.loadedModels);
  }

  /**
   * 卸载模型
   * @param modelId 模型ID
   * @returns 是否成功
   */
  public unloadModel(modelId: string): boolean {
    // 检查模型是否已加载
    if (!this.loadedModels.has(modelId)) {
      return false;
    }

    // 获取模型
    const model = this.loadedModels.get(modelId);

    // 卸载模型
    if (model) {
      (model as any).dispose();
    }

    // 从已加载模型列表中移除
    this.loadedModels.delete(modelId);

    // 触发模型卸载事件
    this.eventEmitter.emit('modelUnloaded', {
      modelId
    });

    return true;
  }

  /**
   * 卸载所有模型
   */
  public unloadAllModels(): void {
    // 卸载所有模型
    for (const [modelId, model] of this.loadedModels.entries()) {
      (model as any).dispose();

      // 触发模型卸载事件
      this.eventEmitter.emit('modelUnloaded', {
        modelId
      });
    }

    // 清空已加载模型列表
    this.loadedModels.clear();
  }

  /**
   * 获取模型加载进度
   * @param modelId 模型ID
   * @returns 加载进度 (0-1)
   */
  public getModelLoadProgress(modelId: string): number {
    return this.modelLoadProgress.get(modelId) || 0;
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   * @returns this 实例，用于链式调用
   */
  public addListener(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   * @returns this 实例，用于链式调用
   */
  public removeListener(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 监听事件（兼容System类）
   * @param event 事件名称
   * @param callback 回调函数
   * @returns this 实例，用于链式调用
   */
  public on(event: string, callback: (...args: any[]) => void): this {
    return this.addListener(event, callback);
  }

  /**
   * 取消监听事件（兼容System类）
   * @param event 事件名称
   * @param callback 回调函数
   * @returns this 实例，用于链式调用
   */
  public off(event: string, callback?: (...args: any[]) => void): this {
    if (callback) {
      return this.removeListener(event, callback);
    } else {
      // 移除指定事件的所有监听器
      // 注意：EventEmitter 的 removeAllListeners 方法可能不接受参数
      // 这里做一个兼容处理
      try {
        // @ts-ignore - 忽略类型检查
        this.eventEmitter.removeAllListeners(event);
      } catch (error) {
        console.warn(`无法移除事件 ${event} 的所有监听器:`, error);
      }
      return this;
    }
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 卸载所有模型
    this.unloadAllModels();

    // 清空事件监听器
    this.eventEmitter.removeAllListeners();
  }
}
