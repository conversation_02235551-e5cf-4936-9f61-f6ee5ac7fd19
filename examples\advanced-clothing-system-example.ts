/**
 * 高级换装系统使用示例
 * 展示如何使用数字人制作系统的高级换装功能
 */

import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/Transform';
import { DigitalHumanComponent, ClothingSlotType } from '../engine/src/avatar/components/DigitalHumanComponent';
import { 
  ClothingSystem, 
  ClothingItem, 
  ClothingCategory, 
  ClothingMaterialType,
  ClothingPhysics,
  ClothingFittingParams
} from '../engine/src/avatar/clothing/ClothingSystem';
import { ClothingEditor } from '../engine/src/ui/clothing/ClothingEditor';
import * as THREE from 'three';

/**
 * 高级换装系统示例类
 */
export class AdvancedClothingSystemExample {
  private world: World;
  private clothingSystem: ClothingSystem;
  private clothingEditor: ClothingEditor;
  private digitalHumanEntity: Entity;

  constructor() {
    this.world = new World();
    this.initializeExample();
  }

  /**
   * 初始化示例
   */
  private async initializeExample(): Promise<void> {
    console.log('🎨 初始化高级换装系统示例...');

    // 1. 创建换装系统
    this.clothingSystem = new ClothingSystem(this.world, {
      enablePhysics: true,
      enableAutoFitting: true,
      enableCollisionDetection: true,
      fittingQualityThreshold: 0.8,
      maxClothingItems: 1000,
      debug: true
    });

    // 2. 添加系统到世界
    this.world.addSystem(this.clothingSystem);

    // 3. 创建数字人实体
    await this.createDigitalHuman();

    // 4. 创建示例服装库
    await this.createClothingLibrary();

    // 5. 创建换装编辑器UI
    this.createClothingEditor();

    // 6. 演示换装功能
    await this.demonstrateClothingFeatures();

    console.log('✅ 高级换装系统示例初始化完成');
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHuman(): Promise<void> {
    console.log('👤 创建数字人实体...');

    // 创建实体
    this.digitalHumanEntity = new Entity(this.world);
    this.digitalHumanEntity.name = 'ExampleDigitalHuman';

    // 添加Transform组件
    const transform = new Transform();
    this.digitalHumanEntity.addComponent(transform);

    // 添加数字人组件
    const digitalHumanComponent = new DigitalHumanComponent(this.digitalHumanEntity, {
      name: '示例数字人',
      userId: 'example-user',
      bodyMorphTargets: {
        height: 0.1,
        weight: 0.0,
        muscle: 0.2,
        chest: 0.0,
        waist: 0.0,
        hips: 0.0,
        shoulders: 0.1,
        custom: new Map()
      }
    });

    this.digitalHumanEntity.addComponent(digitalHumanComponent);

    // 添加到世界
    this.world.addEntity(this.digitalHumanEntity);

    console.log('✅ 数字人实体创建完成');
  }

  /**
   * 创建服装库
   */
  private async createClothingLibrary(): Promise<void> {
    console.log('👕 创建示例服装库...');

    // 创建各种类型的服装项
    const clothingItems: ClothingItem[] = [
      // 休闲上衣
      {
        id: 'casual_tshirt_001',
        name: '休闲T恤',
        category: ClothingCategory.CASUAL,
        slotType: ClothingSlotType.UPPER_BODY,
        geometryUrl: '/assets/clothing/casual_tshirt.glb',
        textureUrls: new Map([
          ['diffuse', '/assets/textures/tshirt_diffuse.jpg'],
          ['normal', '/assets/textures/tshirt_normal.jpg']
        ]),
        materialType: ClothingMaterialType.COTTON,
        materialProperties: {
          color: '#4A90E2',
          roughness: 0.8,
          metalness: 0.0
        },
        physics: this.createDefaultPhysics(),
        fittingParams: this.createDefaultFittingParams(),
        compatibleBodyTypes: ['average', 'muscular', 'tall'],
        price: 29.99,
        tags: ['casual', 'comfortable', 'everyday'],
        creator: 'example-designer',
        version: '1.0.0'
      },

      // 正装衬衫
      {
        id: 'formal_shirt_001',
        name: '正装衬衫',
        category: ClothingCategory.FORMAL,
        slotType: ClothingSlotType.UPPER_BODY,
        geometryUrl: '/assets/clothing/formal_shirt.glb',
        textureUrls: new Map([
          ['diffuse', '/assets/textures/shirt_diffuse.jpg'],
          ['normal', '/assets/textures/shirt_normal.jpg']
        ]),
        materialType: ClothingMaterialType.COTTON,
        materialProperties: {
          color: '#FFFFFF',
          roughness: 0.6,
          metalness: 0.0
        },
        physics: this.createDefaultPhysics(),
        fittingParams: this.createDefaultFittingParams(),
        compatibleBodyTypes: ['average', 'tall', 'short'],
        price: 79.99,
        tags: ['formal', 'business', 'professional'],
        creator: 'example-designer',
        version: '1.0.0'
      },

      // 牛仔裤
      {
        id: 'casual_jeans_001',
        name: '经典牛仔裤',
        category: ClothingCategory.CASUAL,
        slotType: ClothingSlotType.LOWER_BODY,
        geometryUrl: '/assets/clothing/jeans.glb',
        textureUrls: new Map([
          ['diffuse', '/assets/textures/jeans_diffuse.jpg'],
          ['normal', '/assets/textures/jeans_normal.jpg']
        ]),
        materialType: ClothingMaterialType.DENIM,
        materialProperties: {
          color: '#4169E1',
          roughness: 0.9,
          metalness: 0.0
        },
        physics: this.createDefaultPhysics(),
        fittingParams: this.createDefaultFittingParams(),
        compatibleBodyTypes: ['average', 'muscular', 'tall', 'short'],
        price: 59.99,
        tags: ['casual', 'denim', 'classic'],
        creator: 'example-designer',
        version: '1.0.0'
      },

      // 运动鞋
      {
        id: 'sports_sneakers_001',
        name: '运动鞋',
        category: ClothingCategory.SPORTS,
        slotType: ClothingSlotType.FEET,
        geometryUrl: '/assets/clothing/sneakers.glb',
        textureUrls: new Map([
          ['diffuse', '/assets/textures/sneakers_diffuse.jpg'],
          ['normal', '/assets/textures/sneakers_normal.jpg']
        ]),
        materialType: ClothingMaterialType.SYNTHETIC,
        materialProperties: {
          color: '#FFFFFF',
          roughness: 0.7,
          metalness: 0.1
        },
        physics: this.createDefaultPhysics(),
        fittingParams: this.createDefaultFittingParams(),
        compatibleBodyTypes: ['average', 'muscular', 'tall', 'short'],
        price: 89.99,
        tags: ['sports', 'comfortable', 'athletic'],
        creator: 'example-designer',
        version: '1.0.0'
      },

      // 皮夹克
      {
        id: 'casual_leather_jacket_001',
        name: '皮夹克',
        category: ClothingCategory.CASUAL,
        slotType: ClothingSlotType.UPPER_BODY,
        geometryUrl: '/assets/clothing/leather_jacket.glb',
        textureUrls: new Map([
          ['diffuse', '/assets/textures/leather_diffuse.jpg'],
          ['normal', '/assets/textures/leather_normal.jpg']
        ]),
        materialType: ClothingMaterialType.LEATHER,
        materialProperties: {
          color: '#2C1810',
          roughness: 0.6,
          metalness: 0.0
        },
        physics: this.createDefaultPhysics(),
        fittingParams: this.createDefaultFittingParams(),
        compatibleBodyTypes: ['average', 'muscular'],
        price: 199.99,
        tags: ['casual', 'leather', 'stylish'],
        creator: 'example-designer',
        version: '1.0.0'
      }
    ];

    // 添加服装项到系统
    for (const item of clothingItems) {
      this.clothingSystem.addClothingItem(item);
    }

    console.log(`✅ 创建了 ${clothingItems.length} 个服装项`);
  }

  /**
   * 创建默认物理属性
   */
  private createDefaultPhysics(): ClothingPhysics {
    return {
      enabled: true,
      mass: 0.5,
      stiffness: 0.8,
      damping: 0.3,
      elasticity: 0.2,
      friction: 0.6,
      airResistance: 0.1,
      gravityScale: 1.0
    };
  }

  /**
   * 创建默认适配参数
   */
  private createDefaultFittingParams(): ClothingFittingParams {
    return {
      scale: new THREE.Vector3(1, 1, 1),
      offset: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      boneWeights: new Map(),
      morphTargetWeights: new Map()
    };
  }

  /**
   * 创建换装编辑器
   */
  private createClothingEditor(): void {
    console.log('🎨 创建换装编辑器UI...');

    // 创建容器元素
    const container = document.createElement('div');
    container.id = 'clothing-editor-container';
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;
      background: rgba(0, 0, 0, 0.8);
    `;

    document.body.appendChild(container);

    // 创建编辑器
    this.clothingEditor = new ClothingEditor(this.clothingSystem, {
      container,
      showPreview: true,
      enableDragDrop: true,
      theme: 'dark',
      language: 'zh'
    });

    // 设置当前数字人
    this.clothingEditor.setCurrentEntity(this.digitalHumanEntity.id);

    // 监听编辑器事件
    this.clothingEditor.on('itemEquipped', (itemId, slotType) => {
      console.log(`✅ 装备服装: ${itemId} 到插槽 ${slotType}`);
    });

    this.clothingEditor.on('outfitApplied', (outfitId) => {
      console.log(`✅ 应用服装组合: ${outfitId}`);
    });

    this.clothingEditor.on('error', (error) => {
      console.error('❌ 换装编辑器错误:', error);
    });

    console.log('✅ 换装编辑器创建完成');
  }

  /**
   * 演示换装功能
   */
  private async demonstrateClothingFeatures(): Promise<void> {
    console.log('🎭 演示换装功能...');

    // 1. 装备基础服装
    console.log('1. 装备基础服装...');
    await this.clothingSystem.equipClothing(this.digitalHumanEntity.id, 'casual_tshirt_001');
    await this.clothingSystem.equipClothing(this.digitalHumanEntity.id, 'casual_jeans_001');
    await this.clothingSystem.equipClothing(this.digitalHumanEntity.id, 'sports_sneakers_001');

    // 2. 创建服装组合
    console.log('2. 创建服装组合...');
    const casualOutfit = this.clothingSystem.createOutfit(
      '休闲装',
      new Map([
        [ClothingSlotType.UPPER_BODY, 'casual_tshirt_001'],
        [ClothingSlotType.LOWER_BODY, 'casual_jeans_001'],
        [ClothingSlotType.FEET, 'sports_sneakers_001']
      ]),
      ['casual', 'everyday']
    );

    // 3. 演示物理效果
    console.log('3. 演示物理效果...');
    const entityClothing = this.clothingSystem['equippedClothing'].get(this.digitalHumanEntity.id);
    if (entityClothing) {
      for (const [slotType, fittedClothing] of entityClothing) {
        this.clothingSystem.simulateClothPhysics(fittedClothing);
        console.log(`  - ${slotType}: 物理模拟已启用`);
      }
    }

    // 4. 搜索服装
    console.log('4. 搜索服装...');
    const searchResults = this.clothingSystem.searchClothingItems({
      category: ClothingCategory.CASUAL,
      materialType: ClothingMaterialType.COTTON,
      tags: ['comfortable']
    });
    console.log(`  - 找到 ${searchResults.length} 个匹配的服装项`);

    // 5. 显示系统统计
    console.log('5. 系统统计:');
    const stats = this.clothingSystem.getStats();
    console.log(`  - 服装项数量: ${stats.clothingItems}`);
    console.log(`  - 服装组合数量: ${stats.outfits}`);
    console.log(`  - 装备实体数量: ${stats.equippedEntities}`);
    console.log(`  - 缓存大小: ${stats.cacheSize}`);

    console.log('✅ 换装功能演示完成');
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    // 示例已在构造函数中初始化
    console.log('🚀 高级换装系统示例正在运行...');
    
    // 添加键盘快捷键
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Escape':
          this.dispose();
          break;
        case '1':
          this.clothingEditor.equipItemToSlot('casual_tshirt_001', ClothingSlotType.UPPER_BODY);
          break;
        case '2':
          this.clothingEditor.equipItemToSlot('formal_shirt_001', ClothingSlotType.UPPER_BODY);
          break;
        case '3':
          this.clothingEditor.equipItemToSlot('casual_leather_jacket_001', ClothingSlotType.UPPER_BODY);
          break;
      }
    });

    console.log('💡 提示: 按 ESC 退出，按 1/2/3 切换上衣');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理换装系统示例...');

    if (this.clothingEditor) {
      this.clothingEditor.dispose();
    }

    if (this.clothingSystem) {
      this.clothingSystem.dispose();
    }

    // 移除容器元素
    const container = document.getElementById('clothing-editor-container');
    if (container) {
      container.remove();
    }

    console.log('✅ 换装系统示例已清理');
  }
}

// 导出示例类
export default AdvancedClothingSystemExample;

// 如果直接运行此文件，则启动示例
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const example = new AdvancedClothingSystemExample();
    await example.run();
  });
}
