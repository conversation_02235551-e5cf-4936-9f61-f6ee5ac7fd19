/**
 * AI处理管理器
 * 统一管理所有AI处理服务
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { FaceDetectionService, FaceDetectionConfig } from './FaceDetectionService';
import { Face3DReconstructionService, Face3DReconstructionConfig, ProcessedPhoto, FaceMesh } from './Face3DReconstructionService';
import { TextureGenerationService, TextureGenerationConfig, FaceTexture } from './TextureGenerationService';

/**
 * AI处理管理器配置
 */
export interface AIProcessingManagerConfig {
  /** 人脸检测配置 */
  faceDetection?: FaceDetectionConfig;
  /** 3D重建配置 */
  face3DReconstruction?: Face3DReconstructionConfig;
  /** 纹理生成配置 */
  textureGeneration?: TextureGenerationConfig;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 缓存大小限制（MB） */
  cacheSize?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 数字人生成请求
 */
export interface DigitalHumanGenerationRequest {
  /** 请求ID */
  id: string;
  /** 照片文件 */
  photoFile: File;
  /** 生成选项 */
  options: {
    /** 输出分辨率 */
    resolution?: number;
    /** 质量级别 */
    quality?: 'low' | 'medium' | 'high' | 'ultra';
    /** 是否生成额外贴图 */
    generateExtraMaps?: boolean;
  };
  /** 进度回调 */
  onProgress?: (progress: number, stage: string) => void;
}

/**
 * 数字人生成结果
 */
export interface DigitalHumanGenerationResult {
  /** 请求ID */
  requestId: string;
  /** 处理后的照片 */
  processedPhoto: ProcessedPhoto;
  /** 人脸网格 */
  faceMesh: FaceMesh;
  /** 人脸纹理 */
  faceTexture: FaceTexture;
  /** 处理时间 */
  processingTime: number;
  /** 质量评分 */
  qualityScore: number;
}

/**
 * AI处理管理器
 */
export class AIProcessingManager extends EventEmitter {
  /** 配置 */
  private config: AIProcessingManagerConfig;

  /** 人脸检测服务 */
  private faceDetectionService: FaceDetectionService;

  /** 3D重建服务 */
  private face3DReconstructionService: Face3DReconstructionService;

  /** 纹理生成服务 */
  private textureGenerationService: TextureGenerationService;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 处理队列 */
  private processingQueue: DigitalHumanGenerationRequest[] = [];

  /** 当前处理中的请求 */
  private currentProcessing: DigitalHumanGenerationRequest | null = null;

  /** 结果缓存 */
  private resultCache: Map<string, DigitalHumanGenerationResult> = new Map();

  /** 缓存使用量（字节） */
  private cacheUsage: number = 0;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIProcessingManagerConfig = {}) {
    super();

    this.config = {
      enableCache: true,
      cacheSize: 100, // 100MB
      debug: false,
      ...config
    };

    // 创建服务实例
    this.faceDetectionService = new FaceDetectionService(this.config.faceDetection);
    this.face3DReconstructionService = new Face3DReconstructionService(this.config.face3DReconstruction);
    this.textureGenerationService = new TextureGenerationService(this.config.textureGeneration);

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 人脸检测事件
    this.faceDetectionService.on('detectionStarted', () => {
      this.emit('stageStarted', 'faceDetection');
    });

    this.faceDetectionService.on('detectionCompleted', (result) => {
      this.emit('stageCompleted', 'faceDetection', result);
    });

    this.faceDetectionService.on('detectionError', (error) => {
      this.emit('stageError', 'faceDetection', error);
    });

    // 3D重建事件
    this.face3DReconstructionService.on('reconstructionStarted', () => {
      this.emit('stageStarted', 'face3DReconstruction');
    });

    this.face3DReconstructionService.on('reconstructionCompleted', (result) => {
      this.emit('stageCompleted', 'face3DReconstruction', result);
    });

    this.face3DReconstructionService.on('reconstructionError', (error) => {
      this.emit('stageError', 'face3DReconstruction', error);
    });

    // 纹理生成事件
    this.textureGenerationService.on('textureGenerationStarted', () => {
      this.emit('stageStarted', 'textureGeneration');
    });

    this.textureGenerationService.on('textureGenerationCompleted', (result) => {
      this.emit('stageCompleted', 'textureGeneration', result);
    });

    this.textureGenerationService.on('textureGenerationError', (error) => {
      this.emit('stageError', 'textureGeneration', error);
    });
  }

  /**
   * 初始化管理器
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      if (this.config.debug) {
        console.log('[AIProcessingManager] 开始初始化');
      }

      // 初始化所有服务
      await Promise.all([
        this.faceDetectionService.initialize(),
        this.face3DReconstructionService.initialize(),
        this.textureGenerationService.initialize()
      ]);

      this.initialized = true;
      this.emit('initialized');

      if (this.config.debug) {
        console.log('[AIProcessingManager] 初始化完成');
      }
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 生成数字人
   * @param request 生成请求
   * @returns Promise<DigitalHumanGenerationResult>
   */
  public async generateDigitalHuman(request: DigitalHumanGenerationRequest): Promise<DigitalHumanGenerationResult> {
    if (!this.initialized) {
      throw new Error('管理器未初始化');
    }

    // 检查缓存
    if (this.config.enableCache) {
      const cacheKey = this.generateCacheKey(request);
      const cachedResult = this.resultCache.get(cacheKey);
      if (cachedResult) {
        if (this.config.debug) {
          console.log('[AIProcessingManager] 使用缓存结果', request.id);
        }
        return cachedResult;
      }
    }

    return new Promise((resolve, reject) => {
      // 添加完成和错误处理
      const originalRequest = { ...request };
      request.onProgress = (progress: number, stage: string) => {
        if (originalRequest.onProgress) {
          originalRequest.onProgress(progress, stage);
        }
      };

      // 添加到队列
      this.processingQueue.push(request);

      // 处理队列
      this.processQueue().then(() => {
        // 队列处理完成后，结果应该已经通过事件返回
      }).catch(reject);

      // 监听完成事件
      const onComplete = (result: DigitalHumanGenerationResult) => {
        if (result.requestId === request.id) {
          this.off('generationCompleted', onComplete);
          this.off('generationError', onError);
          resolve(result);
        }
      };

      const onError = (requestId: string, error: Error) => {
        if (requestId === request.id) {
          this.off('generationCompleted', onComplete);
          this.off('generationError', onError);
          reject(error);
        }
      };

      this.on('generationCompleted', onComplete);
      this.on('generationError', onError);
    });
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    if (this.currentProcessing || this.processingQueue.length === 0) {
      return;
    }

    this.currentProcessing = this.processingQueue.shift()!;
    
    try {
      const result = await this.processRequest(this.currentProcessing);
      
      // 缓存结果
      if (this.config.enableCache) {
        this.cacheResult(this.currentProcessing, result);
      }

      this.emit('generationCompleted', result);
    } catch (error) {
      this.emit('generationError', this.currentProcessing.id, error);
    } finally {
      this.currentProcessing = null;
      
      // 继续处理队列
      if (this.processingQueue.length > 0) {
        setTimeout(() => this.processQueue(), 100);
      }
    }
  }

  /**
   * 处理单个请求
   * @param request 生成请求
   * @returns Promise<DigitalHumanGenerationResult>
   */
  private async processRequest(request: DigitalHumanGenerationRequest): Promise<DigitalHumanGenerationResult> {
    const startTime = Date.now();

    if (this.config.debug) {
      console.log('[AIProcessingManager] 开始处理请求', request.id);
    }

    try {
      // 阶段1：人脸检测和预处理 (0-20%)
      if (request.onProgress) request.onProgress(0, '人脸检测');
      const processedPhoto = await this.faceDetectionService.preprocessPhoto(request.photoFile);
      if (request.onProgress) request.onProgress(0.2, '人脸检测完成');

      // 阶段2：3D人脸重建 (20-70%)
      if (request.onProgress) request.onProgress(0.2, '3D重建');
      const faceMesh = await this.face3DReconstructionService.reconstructFace(processedPhoto);
      if (request.onProgress) request.onProgress(0.7, '3D重建完成');

      // 阶段3：纹理生成 (70-100%)
      if (request.onProgress) request.onProgress(0.7, '纹理生成');
      const faceTexture = await this.textureGenerationService.generateFaceTexture(processedPhoto, faceMesh);
      if (request.onProgress) request.onProgress(1.0, '纹理生成完成');

      const processingTime = Date.now() - startTime;
      const qualityScore = this.calculateQualityScore(processedPhoto, faceMesh, faceTexture);

      const result: DigitalHumanGenerationResult = {
        requestId: request.id,
        processedPhoto,
        faceMesh,
        faceTexture,
        processingTime,
        qualityScore
      };

      if (this.config.debug) {
        console.log(`[AIProcessingManager] 请求处理完成 ${request.id}，耗时 ${processingTime}ms`);
      }

      return result;
    } catch (error) {
      if (this.config.debug) {
        console.error('[AIProcessingManager] 请求处理失败', request.id, error);
      }
      throw error;
    }
  }

  /**
   * 计算质量评分
   * @param photo 处理后的照片
   * @param mesh 人脸网格
   * @param texture 人脸纹理
   * @returns 质量评分 (0-1)
   */
  private calculateQualityScore(photo: ProcessedPhoto, mesh: FaceMesh, texture: FaceTexture): number {
    // TODO: 实现实际的质量评估算法
    // 考虑因素：
    // 1. 人脸检测置信度
    // 2. 网格质量
    // 3. 纹理清晰度
    // 4. 几何一致性

    let score = 0;
    
    // 人脸检测置信度权重 30%
    score += photo.confidence * 0.3;
    
    // 网格质量权重 40%
    const meshQuality = this.assessMeshQuality(mesh);
    score += meshQuality * 0.4;
    
    // 纹理质量权重 30%
    const textureQuality = this.assessTextureQuality(texture);
    score += textureQuality * 0.3;

    return Math.max(0, Math.min(1, score));
  }

  /**
   * 评估网格质量
   * @param mesh 人脸网格
   * @returns 质量评分 (0-1)
   */
  private assessMeshQuality(mesh: FaceMesh): number {
    // TODO: 实现网格质量评估
    // 考虑因素：顶点密度、拓扑结构、几何细节等
    return 0.8;
  }

  /**
   * 评估纹理质量
   * @param texture 人脸纹理
   * @returns 质量评分 (0-1)
   */
  private assessTextureQuality(texture: FaceTexture): number {
    // TODO: 实现纹理质量评估
    // 考虑因素：分辨率、清晰度、色彩一致性等
    return 0.8;
  }

  /**
   * 生成缓存键
   * @param request 生成请求
   * @returns 缓存键
   */
  private generateCacheKey(request: DigitalHumanGenerationRequest): string {
    // 基于文件内容和选项生成唯一键
    const fileInfo = `${request.photoFile.name}_${request.photoFile.size}_${request.photoFile.lastModified}`;
    const optionsInfo = JSON.stringify(request.options);
    return `${fileInfo}_${optionsInfo}`;
  }

  /**
   * 缓存结果
   * @param request 生成请求
   * @param result 生成结果
   */
  private cacheResult(request: DigitalHumanGenerationRequest, result: DigitalHumanGenerationResult): void {
    const cacheKey = this.generateCacheKey(request);
    
    // 估算结果大小
    const estimatedSize = this.estimateResultSize(result);
    
    // 检查缓存大小限制
    const maxCacheSize = (this.config.cacheSize || 100) * 1024 * 1024; // 转换为字节
    
    if (this.cacheUsage + estimatedSize > maxCacheSize) {
      this.clearOldCache();
    }

    this.resultCache.set(cacheKey, result);
    this.cacheUsage += estimatedSize;

    if (this.config.debug) {
      console.log(`[AIProcessingManager] 缓存结果 ${cacheKey}，大小 ${estimatedSize} 字节`);
    }
  }

  /**
   * 估算结果大小
   * @param result 生成结果
   * @returns 估算大小（字节）
   */
  private estimateResultSize(result: DigitalHumanGenerationResult): number {
    // 简单估算：网格数据 + 纹理数据
    const meshSize = result.faceMesh.vertices.byteLength + 
                    result.faceMesh.faces.byteLength + 
                    result.faceMesh.normals.byteLength + 
                    result.faceMesh.uvs.byteLength;
    
    const textureSize = result.faceTexture.resolution * result.faceTexture.resolution * 4; // RGBA
    
    return meshSize + textureSize;
  }

  /**
   * 清理旧缓存
   */
  private clearOldCache(): void {
    // 简单的LRU策略：清理一半缓存
    const entries = Array.from(this.resultCache.entries());
    const halfSize = Math.floor(entries.length / 2);
    
    for (let i = 0; i < halfSize; i++) {
      this.resultCache.delete(entries[i][0]);
    }
    
    this.cacheUsage = Math.floor(this.cacheUsage / 2);

    if (this.config.debug) {
      console.log('[AIProcessingManager] 清理缓存，剩余条目数:', this.resultCache.size);
    }
  }

  /**
   * 获取处理状态
   * @returns 处理状态信息
   */
  public getProcessingStatus(): {
    isProcessing: boolean;
    queueLength: number;
    currentRequest?: string;
    cacheSize: number;
  } {
    return {
      isProcessing: this.currentProcessing !== null,
      queueLength: this.processingQueue.length,
      currentRequest: this.currentProcessing?.id,
      cacheSize: this.resultCache.size
    };
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.resultCache.clear();
    this.cacheUsage = 0;
    
    if (this.config.debug) {
      console.log('[AIProcessingManager] 缓存已清理');
    }
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 清理队列
    this.processingQueue.length = 0;
    this.currentProcessing = null;

    // 清理缓存
    this.clearCache();

    // 销毁服务
    this.faceDetectionService.dispose();
    this.face3DReconstructionService.dispose();
    this.textureGenerationService.dispose();

    this.initialized = false;
    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[AIProcessingManager] 管理器已销毁');
    }
  }
}
