/**
 * MinIO存储服务
 * 用于数字人相关文件的存储管理
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * MinIO配置
 */
export interface MinIOConfig {
  /** 服务端点 */
  endpoint: string;
  /** 端口 */
  port?: number;
  /** 是否使用SSL */
  useSSL?: boolean;
  /** 访问密钥 */
  accessKey: string;
  /** 秘密密钥 */
  secretKey: string;
  /** 默认存储桶 */
  defaultBucket?: string;
  /** 区域 */
  region?: string;
}

/**
 * 文件上传选项
 */
export interface UploadOptions {
  /** 存储桶名称 */
  bucket?: string;
  /** 文件路径 */
  path?: string;
  /** 内容类型 */
  contentType?: string;
  /** 元数据 */
  metadata?: Record<string, string>;
  /** 是否覆盖现有文件 */
  overwrite?: boolean;
  /** 进度回调 */
  onProgress?: (progress: number) => void;
}

/**
 * 文件信息
 */
export interface FileInfo {
  /** 文件名 */
  name: string;
  /** 文件大小 */
  size: number;
  /** 最后修改时间 */
  lastModified: Date;
  /** 内容类型 */
  contentType: string;
  /** ETag */
  etag: string;
  /** 元数据 */
  metadata: Record<string, string>;
  /** 完整路径 */
  fullPath: string;
  /** 访问URL */
  url?: string;
}

/**
 * 数字人文件类型
 */
export enum DigitalHumanFileType {
  /** 源照片 */
  SOURCE_PHOTO = 'source_photo',
  /** 数字人模型 */
  MODEL = 'model',
  /** 纹理贴图 */
  TEXTURE = 'texture',
  /** 动画数据 */
  ANIMATION = 'animation',
  /** BIP骨骼文件 */
  BIP_SKELETON = 'bip_skeleton',
  /** 服装资源 */
  CLOTHING = 'clothing',
  /** 配置文件 */
  CONFIG = 'config',
  /** 缩略图 */
  THUMBNAIL = 'thumbnail'
}

/**
 * 数字人存储路径配置
 */
export interface DigitalHumanStoragePaths {
  /** 用户根目录 */
  userRoot: string;
  /** 数字人根目录 */
  digitalHumanRoot: string;
  /** 源照片目录 */
  sourcePhotos: string;
  /** 模型目录 */
  models: string;
  /** 纹理目录 */
  textures: string;
  /** 动画目录 */
  animations: string;
  /** BIP文件目录 */
  bipFiles: string;
  /** 服装目录 */
  clothing: string;
  /** 缩略图目录 */
  thumbnails: string;
}

/**
 * MinIO存储服务
 */
export class MinIOStorageService extends EventEmitter {
  /** 配置 */
  private config: MinIOConfig;

  /** MinIO客户端 */
  private client: any = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 默认存储桶 */
  private defaultBucket: string;

  /** 数字人存储路径配置 */
  private storagePaths: DigitalHumanStoragePaths;

  /**
   * 构造函数
   * @param config MinIO配置
   */
  constructor(config: MinIOConfig) {
    super();

    this.config = {
      port: 9000,
      useSSL: false,
      defaultBucket: 'digital-humans',
      region: 'us-east-1',
      ...config
    };

    this.defaultBucket = this.config.defaultBucket!;

    // 初始化存储路径配置
    this.storagePaths = {
      userRoot: 'users/{userId}',
      digitalHumanRoot: 'users/{userId}/digital-humans/{digitalHumanId}',
      sourcePhotos: 'users/{userId}/digital-humans/{digitalHumanId}/source-photos',
      models: 'users/{userId}/digital-humans/{digitalHumanId}/models',
      textures: 'users/{userId}/digital-humans/{digitalHumanId}/textures',
      animations: 'users/{userId}/digital-humans/{digitalHumanId}/animations',
      bipFiles: 'users/{userId}/digital-humans/{digitalHumanId}/bip-files',
      clothing: 'users/{userId}/digital-humans/{digitalHumanId}/clothing',
      thumbnails: 'users/{userId}/digital-humans/{digitalHumanId}/thumbnails'
    };
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // TODO: 实际集成MinIO客户端
      // const Minio = require('minio');
      // this.client = new Minio.Client({
      //   endPoint: this.config.endpoint,
      //   port: this.config.port,
      //   useSSL: this.config.useSSL,
      //   accessKey: this.config.accessKey,
      //   secretKey: this.config.secretKey
      // });

      // 模拟MinIO客户端
      this.client = {
        bucketExists: async (bucket: string) => true,
        makeBucket: async (bucket: string, region?: string) => {},
        putObject: async (bucket: string, name: string, data: any, size?: number, metadata?: any) => {
          return { etag: 'mock-etag-' + Date.now() };
        },
        getObject: async (bucket: string, name: string) => {
          return new ReadableStream();
        },
        statObject: async (bucket: string, name: string) => {
          return {
            size: 1024,
            lastModified: new Date(),
            etag: 'mock-etag',
            metaData: {}
          };
        },
        removeObject: async (bucket: string, name: string) => {},
        listObjects: (bucket: string, prefix?: string, recursive?: boolean) => {
          return {
            on: (event: string, callback: Function) => {
              if (event === 'data') {
                // 模拟返回一些对象
                setTimeout(() => {
                  callback({
                    name: 'test-file.jpg',
                    size: 1024,
                    lastModified: new Date(),
                    etag: 'mock-etag'
                  });
                }, 100);
              } else if (event === 'end') {
                setTimeout(callback, 200);
              }
            }
          };
        },
        presignedGetObject: async (bucket: string, name: string, expiry?: number) => {
          return `https://${this.config.endpoint}/${bucket}/${name}?mock=true`;
        }
      };

      // 确保默认存储桶存在
      await this.ensureBucketExists(this.defaultBucket);

      this.initialized = true;
      this.emit('initialized');

      console.log('[MinIOStorageService] 初始化完成');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 确保存储桶存在
   * @param bucket 存储桶名称
   */
  private async ensureBucketExists(bucket: string): Promise<void> {
    const exists = await this.client.bucketExists(bucket);
    if (!exists) {
      await this.client.makeBucket(bucket, this.config.region);
      console.log(`[MinIOStorageService] 创建存储桶: ${bucket}`);
    }
  }

  /**
   * 上传文件
   * @param file 文件数据
   * @param fileName 文件名
   * @param options 上传选项
   * @returns Promise<FileInfo>
   */
  public async uploadFile(
    file: File | Buffer | ArrayBuffer,
    fileName: string,
    options: UploadOptions = {}
  ): Promise<FileInfo> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    const bucket = options.bucket || this.defaultBucket;
    const fullPath = options.path ? `${options.path}/${fileName}` : fileName;

    try {
      // 确保存储桶存在
      await this.ensureBucketExists(bucket);

      // 检查是否覆盖
      if (!options.overwrite) {
        try {
          await this.client.statObject(bucket, fullPath);
          throw new Error(`文件已存在: ${fullPath}`);
        } catch (error) {
          // 文件不存在，可以继续上传
        }
      }

      // 准备上传数据
      let uploadData: any;
      let size: number;
      let contentType = options.contentType;

      if (file instanceof File) {
        uploadData = file.stream();
        size = file.size;
        contentType = contentType || file.type;
      } else if (file instanceof Buffer) {
        uploadData = file;
        size = file.length;
      } else if (file instanceof ArrayBuffer) {
        uploadData = Buffer.from(file);
        size = file.byteLength;
      } else {
        throw new Error('不支持的文件类型');
      }

      // 准备元数据
      const metadata = {
        'Content-Type': contentType || 'application/octet-stream',
        ...options.metadata
      };

      // 上传文件
      const result = await this.client.putObject(bucket, fullPath, uploadData, size, metadata);

      // 获取文件信息
      const stat = await this.client.statObject(bucket, fullPath);

      const fileInfo: FileInfo = {
        name: fileName,
        size: stat.size,
        lastModified: stat.lastModified,
        contentType: contentType || 'application/octet-stream',
        etag: result.etag,
        metadata: stat.metaData || {},
        fullPath,
        url: await this.getFileUrl(bucket, fullPath)
      };

      this.emit('fileUploaded', fileInfo);
      console.log(`[MinIOStorageService] 文件上传成功: ${fullPath}`);

      return fileInfo;
    } catch (error) {
      this.emit('uploadError', error);
      throw error;
    }
  }

  /**
   * 下载文件
   * @param bucket 存储桶名称
   * @param path 文件路径
   * @returns Promise<ReadableStream>
   */
  public async downloadFile(bucket: string = this.defaultBucket, path: string): Promise<ReadableStream> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    try {
      const stream = await this.client.getObject(bucket, path);
      this.emit('fileDownloaded', { bucket, path });
      return stream;
    } catch (error) {
      this.emit('downloadError', error);
      throw error;
    }
  }

  /**
   * 删除文件
   * @param bucket 存储桶名称
   * @param path 文件路径
   */
  public async deleteFile(bucket: string = this.defaultBucket, path: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    try {
      await this.client.removeObject(bucket, path);
      this.emit('fileDeleted', { bucket, path });
      console.log(`[MinIOStorageService] 文件删除成功: ${path}`);
    } catch (error) {
      this.emit('deleteError', error);
      throw error;
    }
  }

  /**
   * 获取文件信息
   * @param bucket 存储桶名称
   * @param path 文件路径
   * @returns Promise<FileInfo>
   */
  public async getFileInfo(bucket: string = this.defaultBucket, path: string): Promise<FileInfo> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    try {
      const stat = await this.client.statObject(bucket, path);
      
      return {
        name: path.split('/').pop() || path,
        size: stat.size,
        lastModified: stat.lastModified,
        contentType: stat.metaData['content-type'] || 'application/octet-stream',
        etag: stat.etag,
        metadata: stat.metaData || {},
        fullPath: path,
        url: await this.getFileUrl(bucket, path)
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 列出文件
   * @param bucket 存储桶名称
   * @param prefix 路径前缀
   * @param recursive 是否递归
   * @returns Promise<FileInfo[]>
   */
  public async listFiles(
    bucket: string = this.defaultBucket,
    prefix?: string,
    recursive: boolean = false
  ): Promise<FileInfo[]> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    return new Promise((resolve, reject) => {
      const files: FileInfo[] = [];
      const stream = this.client.listObjects(bucket, prefix, recursive);

      stream.on('data', (obj: any) => {
        files.push({
          name: obj.name.split('/').pop() || obj.name,
          size: obj.size,
          lastModified: obj.lastModified,
          contentType: 'application/octet-stream', // MinIO不返回content-type
          etag: obj.etag,
          metadata: {},
          fullPath: obj.name
        });
      });

      stream.on('end', () => {
        resolve(files);
      });

      stream.on('error', (error: any) => {
        reject(error);
      });
    });
  }

  /**
   * 获取文件访问URL
   * @param bucket 存储桶名称
   * @param path 文件路径
   * @param expiry 过期时间（秒）
   * @returns Promise<string>
   */
  public async getFileUrl(
    bucket: string = this.defaultBucket,
    path: string,
    expiry: number = 7 * 24 * 60 * 60 // 7天
  ): Promise<string> {
    if (!this.initialized) {
      throw new Error('服务未初始化');
    }

    try {
      return await this.client.presignedGetObject(bucket, path, expiry);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 构建数字人文件路径
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型
   * @param fileName 文件名
   * @returns 完整路径
   */
  public buildDigitalHumanPath(
    userId: string,
    digitalHumanId: string,
    fileType: DigitalHumanFileType,
    fileName: string
  ): string {
    let basePath: string;

    switch (fileType) {
      case DigitalHumanFileType.SOURCE_PHOTO:
        basePath = this.storagePaths.sourcePhotos;
        break;
      case DigitalHumanFileType.MODEL:
        basePath = this.storagePaths.models;
        break;
      case DigitalHumanFileType.TEXTURE:
        basePath = this.storagePaths.textures;
        break;
      case DigitalHumanFileType.ANIMATION:
        basePath = this.storagePaths.animations;
        break;
      case DigitalHumanFileType.BIP_SKELETON:
        basePath = this.storagePaths.bipFiles;
        break;
      case DigitalHumanFileType.CLOTHING:
        basePath = this.storagePaths.clothing;
        break;
      case DigitalHumanFileType.THUMBNAIL:
        basePath = this.storagePaths.thumbnails;
        break;
      default:
        basePath = this.storagePaths.digitalHumanRoot;
    }

    return basePath
      .replace('{userId}', userId)
      .replace('{digitalHumanId}', digitalHumanId) + '/' + fileName;
  }

  /**
   * 上传数字人文件
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型
   * @param file 文件数据
   * @param fileName 文件名
   * @param options 上传选项
   * @returns Promise<FileInfo>
   */
  public async uploadDigitalHumanFile(
    userId: string,
    digitalHumanId: string,
    fileType: DigitalHumanFileType,
    file: File | Buffer | ArrayBuffer,
    fileName: string,
    options: Omit<UploadOptions, 'path'> = {}
  ): Promise<FileInfo> {
    const path = this.buildDigitalHumanPath(userId, digitalHumanId, fileType, fileName);

    return this.uploadFile(file, fileName, {
      ...options,
      path: path.substring(0, path.lastIndexOf('/'))
    });
  }

  /**
   * 下载数字人文件
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型
   * @param fileName 文件名
   * @returns Promise<ReadableStream>
   */
  public async downloadDigitalHumanFile(
    userId: string,
    digitalHumanId: string,
    fileType: DigitalHumanFileType,
    fileName: string
  ): Promise<ReadableStream> {
    const path = this.buildDigitalHumanPath(userId, digitalHumanId, fileType, fileName);
    return this.downloadFile(this.defaultBucket, path);
  }

  /**
   * 删除数字人文件
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型
   * @param fileName 文件名
   */
  public async deleteDigitalHumanFile(
    userId: string,
    digitalHumanId: string,
    fileType: DigitalHumanFileType,
    fileName: string
  ): Promise<void> {
    const path = this.buildDigitalHumanPath(userId, digitalHumanId, fileType, fileName);
    return this.deleteFile(this.defaultBucket, path);
  }

  /**
   * 列出数字人文件
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型（可选）
   * @returns Promise<FileInfo[]>
   */
  public async listDigitalHumanFiles(
    userId: string,
    digitalHumanId: string,
    fileType?: DigitalHumanFileType
  ): Promise<FileInfo[]> {
    let prefix: string;

    if (fileType) {
      prefix = this.buildDigitalHumanPath(userId, digitalHumanId, fileType, '')
        .replace(/\/$/, ''); // 移除末尾的斜杠
    } else {
      prefix = this.storagePaths.digitalHumanRoot
        .replace('{userId}', userId)
        .replace('{digitalHumanId}', digitalHumanId);
    }

    return this.listFiles(this.defaultBucket, prefix, true);
  }

  /**
   * 获取数字人文件URL
   * @param userId 用户ID
   * @param digitalHumanId 数字人ID
   * @param fileType 文件类型
   * @param fileName 文件名
   * @param expiry 过期时间（秒）
   * @returns Promise<string>
   */
  public async getDigitalHumanFileUrl(
    userId: string,
    digitalHumanId: string,
    fileType: DigitalHumanFileType,
    fileName: string,
    expiry?: number
  ): Promise<string> {
    const path = this.buildDigitalHumanPath(userId, digitalHumanId, fileType, fileName);
    return this.getFileUrl(this.defaultBucket, path, expiry);
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    this.client = null;
    this.initialized = false;
    this.removeAllListeners();
    console.log('[MinIOStorageService] 服务已销毁');
  }
}
