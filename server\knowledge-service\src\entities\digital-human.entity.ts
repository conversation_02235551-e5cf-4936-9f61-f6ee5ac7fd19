import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { DigitalHumanKnowledgeBinding } from './digital-human-knowledge-binding.entity';

@Entity('digital_humans')
export class DigitalHuman {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'jsonb', nullable: true })
  modelConfig: any;

  @Column({ type: 'jsonb', nullable: true })
  voiceConfig: any;

  @Column({ type: 'jsonb', nullable: true })
  animationConfig: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 50, default: 'active' })
  status: string;

  @OneToMany(
    () => DigitalHumanKnowledgeBinding,
    (binding) => binding.digitalHuman,
  )
  knowledgeBindings: DigitalHumanKnowledgeBinding[];
}
