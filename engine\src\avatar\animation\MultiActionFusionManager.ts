/**
 * 多动作融合管理器
 * 管理数字人的多个动作融合、冲突解决和状态切换
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { BIPSkeletonParser, BIPSkeletonData } from '../bip/BIPSkeletonParser';
import { BIPToStandardMapping, StandardSkeletonData } from '../bip/BIPToStandardMapping';
import { ActionConflictResolver } from './ActionConflictResolver';
import { AnimationStateMachine } from './AnimationStateMachine';
import {
  BIPData,
  BIPAnimation,
  BatchImportResult,
  BIPImportResult,
  ActionConflict,
  ConflictResolution,
  UnifiedSkeleton,
  BoneHierarchy,
  AnimationClip,
  ActionSequence,
  ActionBlendConfig,
  PlayActionOptions,
  ActionLibraryItem
} from './MultiActionFusionTypes';

/**
 * 融合管理器配置
 */
export interface FusionManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 最大并发处理数 */
  maxConcurrentProcessing?: number;
  /** 自动解决冲突 */
  autoResolveConflicts?: boolean;
}

/**
 * 多动作融合管理器
 */
export class MultiActionFusionManager extends EventEmitter {
  /** 关联的实体 */
  private entity: Entity;

  /** 世界实例 */
  private world: World;

  /** 配置 */
  private config: FusionManagerConfig;

  /** 动作库 */
  private actionLibrary: Map<string, AnimationClip> = new Map();

  /** 动作库项目 */
  private actionLibraryItems: Map<string, ActionLibraryItem> = new Map();

  /** 动画状态机 */
  private animationStateMachine: AnimationStateMachine;

  /** 冲突解决器 */
  private conflictResolver: ActionConflictResolver;

  /** BIP解析器 */
  private bipParser: BIPSkeletonParser;

  /** BIP映射器 */
  private bipMapper: BIPToStandardMapping;

  /** 统一骨骼系统 */
  private unifiedSkeleton: UnifiedSkeleton | null = null;

  /** 处理队列 */
  private processingQueue: File[] = [];

  /** 当前处理中的文件 */
  private activeProcessing: Set<string> = new Set();

  /**
   * 构造函数
   * @param entity 关联实体
   * @param world 世界实例
   * @param config 配置
   */
  constructor(entity: Entity, world: World, config: FusionManagerConfig = {}) {
    super();

    this.entity = entity;
    this.world = world;
    this.config = {
      debug: false,
      maxConcurrentProcessing: 3,
      autoResolveConflicts: true,
      ...config
    };

    // 初始化组件
    this.animationStateMachine = new AnimationStateMachine(entity, {
      debug: this.config.debug
    });

    this.conflictResolver = new ActionConflictResolver({
      autoResolve: this.config.autoResolveConflicts,
      debug: this.config.debug
    });

    this.bipParser = new BIPSkeletonParser({
      debug: this.config.debug
    });

    this.bipMapper = new BIPToStandardMapping({
      debug: this.config.debug
    });

    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.conflictResolver.on('conflictResolved', (conflict: ActionConflict, resolution: ConflictResolution) => {
      this.emit('conflictResolved', conflict, resolution);
    });

    this.animationStateMachine.on('transitionCompleted', (from: string, to: string) => {
      this.emit('actionTransitionCompleted', from, to);
    });
  }

  /**
   * 批量导入BIP文件
   * @param files BIP文件列表
   * @returns 批量导入结果
   */
  public async importMultipleBIPFiles(files: File[]): Promise<BatchImportResult> {
    if (this.config.debug) {
      console.log(`[MultiActionFusionManager] 开始批量导入 ${files.length} 个BIP文件`);
    }

    this.emit('batchImportStarted', files);

    const results: BIPImportResult[] = [];
    const conflicts: ActionConflict[] = [];
    const bipDataList: BIPData[] = [];

    // 1. 并行解析所有BIP文件
    const parsePromises = files.map(file => this.parseBIPFile(file));
    const parseResults = await Promise.allSettled(parsePromises);

    for (let i = 0; i < parseResults.length; i++) {
      const result = parseResults[i];
      const file = files[i];

      if (result.status === 'fulfilled') {
        bipDataList.push(result.value);
        results.push({
          fileName: file.name,
          success: true,
          animationCount: result.value.animations.length
        });
      } else {
        results.push({
          fileName: file.name,
          success: false,
          error: result.reason.message
        });
      }
    }

    // 2. 创建统一骨骼系统
    if (bipDataList.length > 0) {
      this.unifiedSkeleton = await this.createUnifiedSkeleton(bipDataList);
    }

    // 3. 批量重定向动画
    const allAnimations: AnimationClip[] = [];
    for (const bipData of bipDataList) {
      try {
        const animations = await this.retargetBIPAnimations(bipData.animations, this.unifiedSkeleton!);
        allAnimations.push(...animations);

        // 添加到动作库
        for (const animation of animations) {
          this.actionLibrary.set(animation.name, animation);
          this.actionLibraryItems.set(animation.name, {
            name: animation.name,
            duration: animation.duration,
            sourceFile: bipData.fileName,
            status: 'ready',
            hasConflict: false,
            clip: animation
          });
        }
      } catch (error) {
        if (this.config.debug) {
          console.error('[MultiActionFusionManager] 重定向动画失败', bipData.fileName, error);
        }
      }
    }

    // 4. 检测动作冲突
    if (allAnimations.length > 0) {
      const detectedConflicts = this.conflictResolver.detectConflicts(allAnimations);
      conflicts.push(...detectedConflicts);

      // 标记有冲突的动作
      for (const conflict of detectedConflicts) {
        const item1 = this.actionLibraryItems.get(conflict.animation1.name);
        const item2 = this.actionLibraryItems.get(conflict.animation2.name);
        if (item1) item1.hasConflict = true;
        if (item2) item2.hasConflict = true;
      }
    }

    // 5. 解决冲突
    if (conflicts.length > 0 && this.config.autoResolveConflicts) {
      const resolutions = await this.conflictResolver.resolveConflicts(conflicts);
      await this.applyConflictResolutions(resolutions);
    }

    // 6. 构建动画状态机
    if (this.actionLibrary.size > 0) {
      await this.buildAnimationStateMachine();
    }

    const batchResult: BatchImportResult = {
      totalFiles: files.length,
      successCount: results.filter(r => r.success).length,
      results,
      conflicts,
      actionCount: this.actionLibrary.size
    };

    this.emit('batchImportCompleted', batchResult);

    if (this.config.debug) {
      console.log(`[MultiActionFusionManager] 批量导入完成，成功 ${batchResult.successCount}/${batchResult.totalFiles} 个文件`);
    }

    return batchResult;
  }

  /**
   * 解析BIP文件
   * @param file BIP文件
   * @returns BIP数据
   */
  private async parseBIPFile(file: File): Promise<BIPData> {
    const arrayBuffer = await file.arrayBuffer();
    const skeletonData = await this.bipParser.parseBIPFile(arrayBuffer);

    // TODO: 从BIP数据中提取动画
    const animations: BIPAnimation[] = [];

    return {
      fileName: file.name,
      skeleton: skeletonData,
      animations
    };
  }

  /**
   * 创建统一骨骼系统
   * @param bipDataList BIP数据列表
   * @returns 统一骨骼系统
   */
  private async createUnifiedSkeleton(bipDataList: BIPData[]): Promise<UnifiedSkeleton> {
    const allBones = new Set<string>();
    const hierarchies: BoneHierarchy[] = [];

    // 收集所有骨骼信息
    for (const bipData of bipDataList) {
      for (const bone of bipData.skeleton.bones) {
        allBones.add(bone.name);
      }

      // 构建骨骼层级
      const hierarchy = this.buildBoneHierarchy(bipData.skeleton);
      hierarchies.push(hierarchy);
    }

    // 合并骨骼层级
    const unifiedHierarchy = this.mergeBoneHierarchies(hierarchies);

    // 创建统一骨骼映射
    const unifiedMapping = this.createUnifiedBoneMapping(Array.from(allBones));

    return {
      bones: Array.from(allBones),
      hierarchy: unifiedHierarchy,
      mapping: unifiedMapping
    };
  }

  /**
   * 构建骨骼层级
   * @param skeletonData 骨骼数据
   * @returns 骨骼层级
   */
  private buildBoneHierarchy(skeletonData: BIPSkeletonData): BoneHierarchy {
    const parentChild = new Map<string, string[]>();
    const childParent = new Map<string, string>();
    const depthMap = new Map<string, number>();

    let rootBone = '';

    // 构建父子关系
    for (const bone of skeletonData.bones) {
      if (bone.parentId === -1) {
        rootBone = bone.name;
        depthMap.set(bone.name, 0);
      } else {
        const parent = skeletonData.bones.find(b => b.id === bone.parentId);
        if (parent) {
          if (!parentChild.has(parent.name)) {
            parentChild.set(parent.name, []);
          }
          parentChild.get(parent.name)!.push(bone.name);
          childParent.set(bone.name, parent.name);
        }
      }
    }

    // 计算深度
    const calculateDepth = (boneName: string, depth: number) => {
      depthMap.set(boneName, depth);
      const children = parentChild.get(boneName);
      if (children) {
        for (const child of children) {
          calculateDepth(child, depth + 1);
        }
      }
    };

    if (rootBone) {
      calculateDepth(rootBone, 0);
    }

    return {
      root: rootBone,
      parentChild,
      childParent,
      depthMap
    };
  }

  /**
   * 合并骨骼层级
   * @param hierarchies 层级列表
   * @returns 合并后的层级
   */
  private mergeBoneHierarchies(hierarchies: BoneHierarchy[]): BoneHierarchy {
    // TODO: 实现智能层级合并算法
    // 这里返回第一个层级作为简化实现
    return hierarchies[0] || {
      root: '',
      parentChild: new Map(),
      childParent: new Map(),
      depthMap: new Map()
    };
  }

  /**
   * 创建统一骨骼映射
   * @param bones 骨骼列表
   * @returns 骨骼映射
   */
  private createUnifiedBoneMapping(bones: string[]): Map<string, string> {
    const mapping = new Map<string, string>();

    // TODO: 实现智能骨骼映射算法
    // 这里使用简单的一对一映射
    for (const bone of bones) {
      mapping.set(bone, bone);
    }

    return mapping;
  }

  /**
   * 重定向BIP动画
   * @param animations BIP动画列表
   * @param skeleton 统一骨骼系统
   * @returns 重定向后的动画
   */
  private async retargetBIPAnimations(
    animations: BIPAnimation[],
    skeleton: UnifiedSkeleton
  ): Promise<AnimationClip[]> {
    const retargetedAnimations: AnimationClip[] = [];

    for (const bipAnimation of animations) {
      // TODO: 实现动画重定向算法
      const animationClip: AnimationClip = {
        name: bipAnimation.name,
        duration: bipAnimation.duration,
        tracks: []
      };

      retargetedAnimations.push(animationClip);
    }

    return retargetedAnimations;
  }

  /**
   * 应用冲突解决方案
   * @param resolutions 解决方案列表
   */
  private async applyConflictResolutions(resolutions: ConflictResolution[]): Promise<void> {
    for (const resolution of resolutions) {
      try {
        await this.applyResolution(resolution);
      } catch (error) {
        if (this.config.debug) {
          console.error('[MultiActionFusionManager] 应用解决方案失败', resolution, error);
        }
      }
    }
  }

  /**
   * 应用单个解决方案
   * @param resolution 解决方案
   */
  private async applyResolution(resolution: ConflictResolution): Promise<void> {
    switch (resolution.type) {
      case 'rename':
        if (resolution.originalName && resolution.newName) {
          await this.renameAction(resolution.originalName, resolution.newName);
        }
        break;
      case 'bone_remapping':
        if (resolution.remapping) {
          await this.applyBoneRemapping(resolution.remapping);
        }
        break;
      // TODO: 实现其他解决方案类型
    }
  }

  /**
   * 重命名动作
   * @param oldName 旧名称
   * @param newName 新名称
   */
  private async renameAction(oldName: string, newName: string): Promise<void> {
    const animation = this.actionLibrary.get(oldName);
    const item = this.actionLibraryItems.get(oldName);

    if (animation && item) {
      // 更新名称
      animation.name = newName;
      item.name = newName;

      // 更新映射
      this.actionLibrary.delete(oldName);
      this.actionLibraryItems.delete(oldName);
      this.actionLibrary.set(newName, animation);
      this.actionLibraryItems.set(newName, item);

      if (this.config.debug) {
        console.log(`[MultiActionFusionManager] 重命名动作: ${oldName} -> ${newName}`);
      }
    }
  }

  /**
   * 应用骨骼重映射
   * @param remapping 重映射表
   */
  private async applyBoneRemapping(remapping: Map<string, string>): Promise<void> {
    // TODO: 实现骨骼重映射逻辑
    if (this.config.debug) {
      console.log('[MultiActionFusionManager] 应用骨骼重映射', remapping);
    }
  }

  /**
   * 构建动画状态机
   */
  private async buildAnimationStateMachine(): Promise<void> {
    // TODO: 实现状态机构建逻辑
    if (this.config.debug) {
      console.log('[MultiActionFusionManager] 构建动画状态机');
    }
  }

  /**
   * 获取动作库项目列表
   * @returns 动作库项目数组
   */
  public getActionLibraryItems(): ActionLibraryItem[] {
    return Array.from(this.actionLibraryItems.values());
  }

  /**
   * 播放指定动作
   * @param actionName 动作名称
   * @param options 播放选项
   */
  public async playAction(actionName: string, options?: PlayActionOptions): Promise<void> {
    if (!this.actionLibrary.has(actionName)) {
      throw new Error(`动作不存在: ${actionName}`);
    }

    await this.animationStateMachine.transitionTo(actionName, options);
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.actionLibrary.clear();
    this.actionLibraryItems.clear();
    this.processingQueue.length = 0;
    this.activeProcessing.clear();

    this.animationStateMachine.dispose();
    this.conflictResolver.dispose();
    this.bipParser.dispose();
    this.bipMapper.dispose();

    this.removeAllListeners();

    if (this.config.debug) {
      console.log('[MultiActionFusionManager] 管理器已销毁');
    }
  }
}
