import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisClientType } from 'redis';

@Injectable()
export class CacheService {
  private readonly defaultTTL: number;

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
    private readonly configService: ConfigService,
  ) {
    this.defaultTTL = this.configService.get('production.cache.ttl', 3600);
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    const expireTime = ttl || this.defaultTTL;
    
    await this.redisClient.setEx(key, expireTime, serializedValue);
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    const value = await this.redisClient.get(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Failed to parse cached value:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    await this.redisClient.del(key);
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<void> {
    const keys = await this.redisClient.keys(pattern);
    if (keys.length > 0) {
      await this.redisClient.del(keys);
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redisClient.exists(key);
    return result === 1;
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    await this.redisClient.expire(key, ttl);
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    return await this.redisClient.ttl(key);
  }

  /**
   * 原子性增加数值
   */
  async incr(key: string): Promise<number> {
    return await this.redisClient.incr(key);
  }

  /**
   * 原子性减少数值
   */
  async decr(key: string): Promise<number> {
    return await this.redisClient.decr(key);
  }

  /**
   * 设置哈希字段
   */
  async hSet(key: string, field: string, value: any): Promise<void> {
    const serializedValue = JSON.stringify(value);
    await this.redisClient.hSet(key, field, serializedValue);
  }

  /**
   * 获取哈希字段
   */
  async hGet<T>(key: string, field: string): Promise<T | null> {
    const value = await this.redisClient.hGet(key, field);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Failed to parse cached hash value:', error);
      return null;
    }
  }

  /**
   * 删除哈希字段
   */
  async hDel(key: string, field: string): Promise<void> {
    await this.redisClient.hDel(key, field);
  }

  /**
   * 获取所有哈希字段
   */
  async hGetAll<T>(key: string): Promise<Record<string, T>> {
    const values = await this.redisClient.hGetAll(key);
    const result: Record<string, T> = {};

    for (const [field, value] of Object.entries(values)) {
      try {
        result[field] = JSON.parse(value) as T;
      } catch (error) {
        console.error(`Failed to parse cached hash value for field ${field}:`, error);
      }
    }

    return result;
  }

  /**
   * 清空所有缓存
   */
  async flushAll(): Promise<void> {
    await this.redisClient.flushAll();
  }
}
