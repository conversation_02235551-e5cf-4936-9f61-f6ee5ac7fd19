import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { KnowledgeDocument } from './knowledge-document.entity';

@Entity('document_chunks')
export class DocumentChunk {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  documentId: string;

  @Column({ type: 'integer' })
  chunkIndex: number;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  contentHash: string;

  @Column({ type: 'integer', nullable: true })
  startOffset: number;

  @Column({ type: 'integer', nullable: true })
  endOffset: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @Column({ type: 'varchar', length: 255, nullable: true })
  vectorId: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => KnowledgeDocument, (document) => document.chunks)
  @JoinColumn({ name: 'documentId' })
  document: KnowledgeDocument;
}
