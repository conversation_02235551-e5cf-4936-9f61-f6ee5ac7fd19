import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { StorageService } from '../storage/storage.service';
import { CacheService } from '../cache/cache.service';
import { KnowledgeBase, KnowledgeDocument } from '../entities';

export interface DocumentMetadata {
  title: string;
  category: string;
  tags: string[];
  author?: string;
  language: string;
  description?: string;
}

export interface UploadResult {
  documentId: string;
  uploadId: string;
  status: string;
  filePath: string;
  message: string;
}

@Injectable()
export class UploadService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private readonly knowledgeBaseRepository: Repository<KnowledgeBase>,
    @InjectRepository(KnowledgeDocument)
    private readonly documentRepository: Repository<KnowledgeDocument>,
    private readonly storageService: StorageService,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * 上传知识库文档
   */
  async uploadKnowledgeDocument(
    knowledgeBaseId: string,
    file: Express.Multer.File,
    metadata: DocumentMetadata,
    userId: string,
  ): Promise<UploadResult> {
    const uploadId = uuidv4();

    try {
      // 1. 验证知识库存在
      await this.validateKnowledgeBase(knowledgeBaseId);

      // 2. 验证文件
      await this.validateFile(file);

      // 3. 计算文件哈希
      const fileHash = this.storageService.calculateFileHash(file.buffer);

      // 4. 检查重复文件
      const existingDoc = await this.checkDuplicateDocument(knowledgeBaseId, fileHash);
      if (existingDoc) {
        throw new Error('文档已存在');
      }

      // 5. 上传文件到对象存储
      const uploadResult = await this.storageService.uploadFile(file, {
        knowledgeBaseId,
        uploadId,
        metadata: {
          'X-User-ID': userId,
          'X-Document-Title': metadata.title,
          'X-Document-Category': metadata.category,
        },
      });

      // 6. 保存文档记录
      const documentId = await this.saveDocumentRecord(
        knowledgeBaseId,
        file,
        uploadResult.filePath,
        fileHash,
        metadata,
        userId,
      );

      // 7. 异步处理文档
      await this.queueDocumentProcessing(documentId);

      // 8. 清除相关缓存
      await this.clearKnowledgeBaseCache(knowledgeBaseId);

      return {
        documentId,
        uploadId,
        status: 'uploaded',
        filePath: uploadResult.filePath,
        message: '文档上传成功，正在处理中',
      };
    } catch (error) {
      // 清理失败的上传
      await this.cleanupFailedUpload(uploadId);
      throw error;
    }
  }

  /**
   * 验证知识库存在
   */
  private async validateKnowledgeBase(knowledgeBaseId: string): Promise<void> {
    const knowledgeBase = await this.knowledgeBaseRepository.findOne({
      where: { id: knowledgeBaseId, status: 'active' },
    });

    if (!knowledgeBase) {
      throw new Error('知识库不存在或已禁用');
    }
  }

  /**
   * 验证文件
   */
  private async validateFile(file: Express.Multer.File): Promise<void> {
    // 验证文件类型
    if (!this.storageService.validateFileType(file)) {
      throw new Error('不支持的文件类型');
    }

    // 验证文件大小
    if (!this.storageService.validateFileSize(file)) {
      throw new Error('文件大小超出限制');
    }

    // 验证文件内容（病毒扫描等）
    await this.validateFileContent(file);
  }

  /**
   * 验证文件内容
   */
  private async validateFileContent(file: Express.Multer.File): Promise<void> {
    // 这里可以集成病毒扫描服务
    // 例如 ClamAV 或云服务提供商的安全扫描API
    
    // 基本的文件头验证
    const fileSignatures = {
      'application/pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [0x50, 0x4B], // PK (ZIP)
      'text/plain': null, // 文本文件不需要特定签名
    };

    const signature = fileSignatures[file.mimetype];
    if (signature) {
      const fileHeader = Array.from(file.buffer.slice(0, signature.length));
      if (!signature.every((byte, index) => byte === fileHeader[index])) {
        throw new Error('文件格式与扩展名不匹配');
      }
    }

    // 检查文件是否为空
    if (file.buffer.length === 0) {
      throw new Error('文件内容为空');
    }
  }

  /**
   * 检查重复文档
   */
  private async checkDuplicateDocument(
    knowledgeBaseId: string,
    fileHash: string,
  ): Promise<KnowledgeDocument | null> {
    return await this.documentRepository.findOne({
      where: {
        knowledgeBaseId,
        contentHash: fileHash,
        processingStatus: 'completed',
      },
    });
  }

  /**
   * 保存文档记录
   */
  private async saveDocumentRecord(
    knowledgeBaseId: string,
    file: Express.Multer.File,
    filePath: string,
    fileHash: string,
    metadata: DocumentMetadata,
    userId: string,
  ): Promise<string> {
    const document = this.documentRepository.create({
      knowledgeBaseId,
      filename: filePath.split('/').pop(),
      originalFilename: file.originalname,
      filePath,
      fileSize: file.size,
      fileType: file.mimetype,
      contentHash: fileHash,
      metadata,
      uploadedBy: userId,
      processingStatus: 'pending',
    });

    const savedDocument = await this.documentRepository.save(document);
    return savedDocument.id;
  }

  /**
   * 队列文档处理
   */
  private async queueDocumentProcessing(documentId: string): Promise<void> {
    // 这里应该使用消息队列（如 Bull、RabbitMQ 等）
    // 暂时使用缓存来模拟队列
    const queueKey = 'document_processing_queue';
    const queueItem = {
      documentId,
      timestamp: new Date().toISOString(),
      retryCount: 0,
    };

    // 将任务添加到处理队列
    await this.cacheService.set(
      `${queueKey}:${documentId}`,
      queueItem,
      3600, // 1小时过期
    );

    console.log(`Document ${documentId} queued for processing`);
  }

  /**
   * 清理失败的上传
   */
  private async cleanupFailedUpload(uploadId: string): Promise<void> {
    try {
      // 删除可能已上传的文件
      // 这里需要根据uploadId查找文件路径
      console.log(`Cleaning up failed upload: ${uploadId}`);
    } catch (error) {
      console.error('Failed to cleanup upload:', error);
    }
  }

  /**
   * 清除知识库缓存
   */
  private async clearKnowledgeBaseCache(knowledgeBaseId: string): Promise<void> {
    const cacheKeys = [
      `knowledge_base:${knowledgeBaseId}`,
      `knowledge_base:${knowledgeBaseId}:documents`,
      `knowledge_base:${knowledgeBaseId}:stats`,
    ];

    for (const key of cacheKeys) {
      await this.cacheService.del(key);
    }
  }

  /**
   * 获取上传进度
   */
  async getUploadProgress(uploadId: string): Promise<any> {
    const progressKey = `upload_progress:${uploadId}`;
    return await this.cacheService.get(progressKey);
  }

  /**
   * 更新上传进度
   */
  async updateUploadProgress(
    uploadId: string,
    progress: number,
    status: string,
  ): Promise<void> {
    const progressKey = `upload_progress:${uploadId}`;
    const progressData = {
      uploadId,
      progress,
      status,
      timestamp: new Date().toISOString(),
    };

    await this.cacheService.set(progressKey, progressData, 3600);
  }

  /**
   * 批量上传文档
   */
  async batchUploadDocuments(
    knowledgeBaseId: string,
    files: Express.Multer.File[],
    metadata: DocumentMetadata[],
    userId: string,
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const errors: string[] = [];

    for (let i = 0; i < files.length; i++) {
      try {
        const result = await this.uploadKnowledgeDocument(
          knowledgeBaseId,
          files[i],
          metadata[i] || {
            title: files[i].originalname,
            category: 'default',
            tags: [],
            language: 'zh-CN',
          },
          userId,
        );
        results.push(result);
      } catch (error) {
        errors.push(`${files[i].originalname}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      console.warn('Batch upload errors:', errors);
    }

    return results;
  }
}
