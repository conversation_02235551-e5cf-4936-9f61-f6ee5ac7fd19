import { EventEmitter } from 'events';
import { ClothingSystem, ClothingItem, ClothingCategory, ClothingOutfit } from '../../avatar/clothing/ClothingSystem';
import { ClothingSlotType } from '../../avatar/components/DigitalHumanComponent';

/**
 * 服装编辑器配置
 */
export interface ClothingEditorConfig {
  /** 容器元素 */
  container: HTMLElement;
  /** 是否显示预览 */
  showPreview: boolean;
  /** 是否启用拖拽 */
  enableDragDrop: boolean;
  /** 主题 */
  theme: 'light' | 'dark';
  /** 语言 */
  language: 'zh' | 'en';
}

/**
 * 服装编辑器UI组件
 * 提供可视化的服装编辑界面
 */
export class ClothingEditor extends EventEmitter {
  /** 配置 */
  private config: ClothingEditorConfig;

  /** 服装系统 */
  private clothingSystem: ClothingSystem;

  /** 当前选中的数字人实体ID */
  private currentEntityId?: string;

  /** 当前选中的服装项 */
  private selectedClothingItem?: ClothingItem;

  /** 当前选中的服装组合 */
  private selectedOutfit?: ClothingOutfit;

  /** UI元素 */
  private elements: {
    container: HTMLElement;
    sidebar: HTMLElement;
    preview: HTMLElement;
    toolbar: HTMLElement;
    categoryTabs: HTMLElement;
    itemGrid: HTMLElement;
    outfitPanel: HTMLElement;
    propertiesPanel: HTMLElement;
  };

  /**
   * 构造函数
   * @param clothingSystem 服装系统
   * @param config 配置
   */
  constructor(clothingSystem: ClothingSystem, config: ClothingEditorConfig) {
    super();

    this.clothingSystem = clothingSystem;
    this.config = config;

    this.elements = {} as any;

    this.initialize();
  }

  /**
   * 初始化编辑器
   */
  private initialize(): void {
    this.createUI();
    this.bindEvents();
    this.loadClothingItems();
  }

  /**
   * 创建UI界面
   */
  private createUI(): void {
    const container = this.config.container;
    container.className = `clothing-editor ${this.config.theme}`;

    // 创建主布局
    container.innerHTML = `
      <div class="clothing-editor-layout">
        <div class="clothing-editor-toolbar">
          <div class="toolbar-section">
            <button class="btn btn-primary" data-action="new-outfit">新建组合</button>
            <button class="btn btn-secondary" data-action="save-outfit">保存组合</button>
            <button class="btn btn-secondary" data-action="load-outfit">加载组合</button>
          </div>
          <div class="toolbar-section">
            <button class="btn btn-icon" data-action="undo" title="撤销">↶</button>
            <button class="btn btn-icon" data-action="redo" title="重做">↷</button>
          </div>
        </div>
        
        <div class="clothing-editor-content">
          <div class="clothing-editor-sidebar">
            <div class="category-tabs">
              <div class="tab active" data-category="all">全部</div>
              <div class="tab" data-category="casual">休闲</div>
              <div class="tab" data-category="formal">正装</div>
              <div class="tab" data-category="sports">运动</div>
              <div class="tab" data-category="traditional">传统</div>
              <div class="tab" data-category="fantasy">奇幻</div>
            </div>
            
            <div class="search-bar">
              <input type="text" placeholder="搜索服装..." class="search-input">
              <button class="search-btn">🔍</button>
            </div>
            
            <div class="item-grid">
              <!-- 服装项将在这里动态生成 -->
            </div>
          </div>
          
          <div class="clothing-editor-main">
            <div class="slot-panel">
              <h3>服装插槽</h3>
              <div class="slots-grid">
                <div class="slot" data-slot="head">
                  <div class="slot-icon">👤</div>
                  <div class="slot-label">头部</div>
                  <div class="slot-item"></div>
                </div>
                <div class="slot" data-slot="hair">
                  <div class="slot-icon">💇</div>
                  <div class="slot-label">发型</div>
                  <div class="slot-item"></div>
                </div>
                <div class="slot" data-slot="upperBody">
                  <div class="slot-icon">👕</div>
                  <div class="slot-label">上身</div>
                  <div class="slot-item"></div>
                </div>
                <div class="slot" data-slot="lowerBody">
                  <div class="slot-icon">👖</div>
                  <div class="slot-label">下身</div>
                  <div class="slot-item"></div>
                </div>
                <div class="slot" data-slot="feet">
                  <div class="slot-icon">👟</div>
                  <div class="slot-label">鞋子</div>
                  <div class="slot-item"></div>
                </div>
                <div class="slot" data-slot="accessories">
                  <div class="slot-icon">👜</div>
                  <div class="slot-label">配饰</div>
                  <div class="slot-item"></div>
                </div>
              </div>
            </div>
            
            <div class="outfit-panel">
              <h3>服装组合</h3>
              <div class="outfit-list">
                <!-- 服装组合将在这里动态生成 -->
              </div>
            </div>
          </div>
          
          <div class="clothing-editor-properties">
            <h3>属性面板</h3>
            <div class="properties-content">
              <div class="property-group">
                <label>名称</label>
                <input type="text" class="property-input" data-property="name">
              </div>
              <div class="property-group">
                <label>类别</label>
                <select class="property-select" data-property="category">
                  <option value="casual">休闲</option>
                  <option value="formal">正装</option>
                  <option value="sports">运动</option>
                  <option value="traditional">传统</option>
                  <option value="fantasy">奇幻</option>
                </select>
              </div>
              <div class="property-group">
                <label>材质</label>
                <select class="property-select" data-property="material">
                  <option value="cotton">棉质</option>
                  <option value="silk">丝绸</option>
                  <option value="leather">皮革</option>
                  <option value="denim">牛仔</option>
                  <option value="wool">羊毛</option>
                </select>
              </div>
              <div class="property-group">
                <label>颜色</label>
                <input type="color" class="property-color" data-property="color">
              </div>
              <div class="property-group">
                <label>物理效果</label>
                <input type="checkbox" class="property-checkbox" data-property="physics">
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 获取UI元素引用
    this.elements.container = container;
    this.elements.toolbar = container.querySelector('.clothing-editor-toolbar') as HTMLElement;
    this.elements.sidebar = container.querySelector('.clothing-editor-sidebar') as HTMLElement;
    this.elements.categoryTabs = container.querySelector('.category-tabs') as HTMLElement;
    this.elements.itemGrid = container.querySelector('.item-grid') as HTMLElement;
    this.elements.outfitPanel = container.querySelector('.outfit-panel') as HTMLElement;
    this.elements.propertiesPanel = container.querySelector('.clothing-editor-properties') as HTMLElement;

    // 添加样式
    this.addStyles();
  }

  /**
   * 添加样式
   */
  private addStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .clothing-editor {
        width: 100%;
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
      }
      
      .clothing-editor.light {
        --bg-primary: #ffffff;
        --bg-secondary: #f5f5f5;
        --bg-tertiary: #e0e0e0;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #d0d0d0;
        --accent-color: #007bff;
      }
      
      .clothing-editor.dark {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --bg-tertiary: #404040;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #555555;
        --accent-color: #0d6efd;
      }
      
      .clothing-editor-layout {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      
      .clothing-editor-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
      }
      
      .toolbar-section {
        display: flex;
        gap: 8px;
      }
      
      .btn {
        padding: 6px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: var(--bg-primary);
        color: var(--text-primary);
        cursor: pointer;
        font-size: 12px;
      }
      
      .btn:hover {
        background: var(--bg-tertiary);
      }
      
      .btn.btn-primary {
        background: var(--accent-color);
        color: white;
        border-color: var(--accent-color);
      }
      
      .clothing-editor-content {
        display: flex;
        flex: 1;
        overflow: hidden;
      }
      
      .clothing-editor-sidebar {
        width: 300px;
        background: var(--bg-secondary);
        border-right: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
      }
      
      .category-tabs {
        display: flex;
        flex-wrap: wrap;
        padding: 8px;
        gap: 4px;
      }
      
      .tab {
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        background: var(--bg-tertiary);
      }
      
      .tab.active {
        background: var(--accent-color);
        color: white;
      }
      
      .search-bar {
        display: flex;
        padding: 8px;
        gap: 4px;
      }
      
      .search-input {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: var(--bg-primary);
        color: var(--text-primary);
      }
      
      .item-grid {
        flex: 1;
        overflow-y: auto;
        padding: 8px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
      }
      
      .clothing-item {
        aspect-ratio: 1;
        border: 2px solid var(--border-color);
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--bg-primary);
        font-size: 24px;
      }
      
      .clothing-item.selected {
        border-color: var(--accent-color);
      }
      
      .clothing-editor-main {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
      }
      
      .slot-panel h3,
      .outfit-panel h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      .slots-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-bottom: 32px;
      }
      
      .slot {
        border: 2px dashed var(--border-color);
        border-radius: 8px;
        padding: 16px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .slot:hover {
        border-color: var(--accent-color);
      }
      
      .slot.has-item {
        border-style: solid;
        background: var(--bg-secondary);
      }
      
      .slot-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      
      .slot-label {
        font-size: 12px;
        color: var(--text-secondary);
      }
      
      .clothing-editor-properties {
        width: 250px;
        background: var(--bg-secondary);
        border-left: 1px solid var(--border-color);
        padding: 16px;
      }
      
      .property-group {
        margin-bottom: 16px;
      }
      
      .property-group label {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .property-input,
      .property-select {
        width: 100%;
        padding: 4px 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: var(--bg-primary);
        color: var(--text-primary);
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 工具栏按钮事件
    this.elements.toolbar.addEventListener('click', this.handleToolbarClick.bind(this));

    // 类别标签事件
    this.elements.categoryTabs.addEventListener('click', this.handleCategoryClick.bind(this));

    // 搜索事件
    const searchInput = this.elements.sidebar.querySelector('.search-input') as HTMLInputElement;
    searchInput.addEventListener('input', this.handleSearch.bind(this));

    // 服装项网格事件
    this.elements.itemGrid.addEventListener('click', this.handleItemClick.bind(this));

    // 插槽事件
    const slotsGrid = this.elements.container.querySelector('.slots-grid') as HTMLElement;
    slotsGrid.addEventListener('click', this.handleSlotClick.bind(this));

    // 属性面板事件
    this.elements.propertiesPanel.addEventListener('change', this.handlePropertyChange.bind(this));

    // 拖拽事件
    if (this.config.enableDragDrop) {
      this.setupDragDrop();
    }
  }

  /**
   * 加载服装项
   */
  private loadClothingItems(): void {
    this.updateItemGrid();
    this.updateOutfitList();
  }

  /**
   * 更新服装项网格
   * @param category 类别过滤
   * @param searchTerm 搜索词
   */
  private updateItemGrid(category?: ClothingCategory, searchTerm?: string): void {
    const items = category 
      ? this.clothingSystem.getClothingItemsByCategory(category)
      : Array.from(this.clothingSystem['clothingLibrary'].values());

    let filteredItems = items;
    if (searchTerm) {
      filteredItems = items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    this.elements.itemGrid.innerHTML = '';
    
    filteredItems.forEach(item => {
      const itemElement = document.createElement('div');
      itemElement.className = 'clothing-item';
      itemElement.dataset.itemId = item.id;
      itemElement.innerHTML = this.getItemIcon(item);
      itemElement.title = item.name;
      
      this.elements.itemGrid.appendChild(itemElement);
    });
  }

  /**
   * 获取服装项图标
   * @param item 服装项
   * @returns 图标HTML
   */
  private getItemIcon(item: ClothingItem): string {
    const icons: Record<ClothingSlotType, string> = {
      [ClothingSlotType.HEAD]: '👤',
      [ClothingSlotType.HAIR]: '💇',
      [ClothingSlotType.FACE]: '😊',
      [ClothingSlotType.UPPER_BODY]: '👕',
      [ClothingSlotType.LOWER_BODY]: '👖',
      [ClothingSlotType.FEET]: '👟',
      [ClothingSlotType.HANDS]: '🧤',
      [ClothingSlotType.ACCESSORIES]: '👜',
      [ClothingSlotType.JEWELRY]: '💍'
    };

    return icons[item.slotType] || '👕';
  }

  /**
   * 更新服装组合列表
   */
  private updateOutfitList(): void {
    const outfits = this.clothingSystem.getAllOutfits();
    const outfitList = this.elements.outfitPanel.querySelector('.outfit-list') as HTMLElement;
    
    outfitList.innerHTML = '';
    
    outfits.forEach(outfit => {
      const outfitElement = document.createElement('div');
      outfitElement.className = 'outfit-item';
      outfitElement.dataset.outfitId = outfit.id;
      outfitElement.innerHTML = `
        <div class="outfit-name">${outfit.name}</div>
        <div class="outfit-actions">
          <button class="btn btn-sm" data-action="apply">应用</button>
          <button class="btn btn-sm" data-action="edit">编辑</button>
          <button class="btn btn-sm" data-action="delete">删除</button>
        </div>
      `;
      
      outfitList.appendChild(outfitElement);
    });
  }

  // ==================== 事件处理方法 ====================

  /**
   * 处理工具栏点击事件
   * @param event 点击事件
   */
  private handleToolbarClick(event: Event): void {
    const target = event.target as HTMLElement;
    const action = target.dataset.action;

    switch (action) {
      case 'new-outfit':
        this.createNewOutfit();
        break;
      case 'save-outfit':
        this.saveCurrentOutfit();
        break;
      case 'load-outfit':
        this.showOutfitSelector();
        break;
      case 'undo':
        this.undo();
        break;
      case 'redo':
        this.redo();
        break;
    }
  }

  /**
   * 处理类别点击事件
   * @param event 点击事件
   */
  private handleCategoryClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.classList.contains('tab')) return;

    // 更新活跃标签
    this.elements.categoryTabs.querySelectorAll('.tab').forEach(tab => {
      tab.classList.remove('active');
    });
    target.classList.add('active');

    // 更新服装项显示
    const category = target.dataset.category as ClothingCategory;
    this.updateItemGrid(category === 'all' ? undefined : category);
  }

  /**
   * 处理搜索事件
   * @param event 输入事件
   */
  private handleSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // 获取当前选中的类别
    const activeTab = this.elements.categoryTabs.querySelector('.tab.active') as HTMLElement;
    const category = activeTab?.dataset.category as ClothingCategory;

    this.updateItemGrid(category === 'all' ? undefined : category, searchTerm);
  }

  /**
   * 处理服装项点击事件
   * @param event 点击事件
   */
  private handleItemClick(event: Event): void {
    const target = event.target as HTMLElement;
    const itemElement = target.closest('.clothing-item') as HTMLElement;
    if (!itemElement) return;

    const itemId = itemElement.dataset.itemId;
    if (!itemId) return;

    // 更新选中状态
    this.elements.itemGrid.querySelectorAll('.clothing-item').forEach(item => {
      item.classList.remove('selected');
    });
    itemElement.classList.add('selected');

    // 获取服装项
    const clothingItem = this.clothingSystem.getClothingItem(itemId);
    if (clothingItem) {
      this.selectedClothingItem = clothingItem;
      this.updatePropertiesPanel(clothingItem);
      this.emit('itemSelected', clothingItem);
    }
  }

  /**
   * 处理插槽点击事件
   * @param event 点击事件
   */
  private handleSlotClick(event: Event): void {
    const target = event.target as HTMLElement;
    const slotElement = target.closest('.slot') as HTMLElement;
    if (!slotElement) return;

    const slotType = slotElement.dataset.slot as ClothingSlotType;

    if (this.selectedClothingItem && this.currentEntityId) {
      // 装备选中的服装到插槽
      this.equipItemToSlot(this.selectedClothingItem.id, slotType);
    } else {
      // 显示插槽菜单
      this.showSlotMenu(slotType, slotElement);
    }
  }

  /**
   * 处理属性变更事件
   * @param event 变更事件
   */
  private handlePropertyChange(event: Event): void {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    const property = target.dataset.property;
    const value = target.type === 'checkbox' ? (target as HTMLInputElement).checked : target.value;

    if (this.selectedClothingItem && property) {
      this.updateItemProperty(this.selectedClothingItem.id, property, value);
    }
  }

  /**
   * 设置拖拽功能
   */
  private setupDragDrop(): void {
    // 服装项拖拽
    this.elements.itemGrid.addEventListener('dragstart', (event) => {
      const target = event.target as HTMLElement;
      const itemElement = target.closest('.clothing-item') as HTMLElement;
      if (itemElement) {
        event.dataTransfer?.setData('text/plain', itemElement.dataset.itemId || '');
      }
    });

    // 插槽放置
    const slotsGrid = this.elements.container.querySelector('.slots-grid') as HTMLElement;
    slotsGrid.addEventListener('dragover', (event) => {
      event.preventDefault();
    });

    slotsGrid.addEventListener('drop', (event) => {
      event.preventDefault();
      const itemId = event.dataTransfer?.getData('text/plain');
      const target = event.target as HTMLElement;
      const slotElement = target.closest('.slot') as HTMLElement;

      if (itemId && slotElement) {
        const slotType = slotElement.dataset.slot as ClothingSlotType;
        this.equipItemToSlot(itemId, slotType);
      }
    });
  }

  // ==================== 公共方法 ====================

  /**
   * 设置当前数字人
   * @param entityId 实体ID
   */
  public setCurrentEntity(entityId: string): void {
    this.currentEntityId = entityId;
    this.updateSlotDisplay();
    this.emit('entityChanged', entityId);
  }

  /**
   * 装备服装到插槽
   * @param itemId 服装项ID
   * @param slotType 插槽类型
   */
  public async equipItemToSlot(itemId: string, slotType: ClothingSlotType): Promise<void> {
    if (!this.currentEntityId) {
      console.warn('没有选中的数字人实体');
      return;
    }

    try {
      const success = await this.clothingSystem.equipClothing(this.currentEntityId, itemId);
      if (success) {
        this.updateSlotDisplay();
        this.emit('itemEquipped', itemId, slotType);
      }
    } catch (error) {
      console.error('装备服装失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 卸下插槽中的服装
   * @param slotType 插槽类型
   */
  public unequipSlot(slotType: ClothingSlotType): void {
    if (!this.currentEntityId) return;

    const success = this.clothingSystem.unequipClothing(this.currentEntityId, slotType);
    if (success) {
      this.updateSlotDisplay();
      this.emit('itemUnequipped', slotType);
    }
  }

  /**
   * 应用服装组合
   * @param outfitId 组合ID
   */
  public async applyOutfit(outfitId: string): Promise<void> {
    if (!this.currentEntityId) return;

    try {
      const success = await this.clothingSystem.applyOutfit(this.currentEntityId, outfitId);
      if (success) {
        this.updateSlotDisplay();
        this.emit('outfitApplied', outfitId);
      }
    } catch (error) {
      console.error('应用服装组合失败:', error);
      this.emit('error', error);
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 更新插槽显示
   */
  private updateSlotDisplay(): void {
    if (!this.currentEntityId) return;

    const entityClothing = this.clothingSystem['equippedClothing'].get(this.currentEntityId);
    const slots = this.elements.container.querySelectorAll('.slot');

    slots.forEach(slotElement => {
      const slotType = (slotElement as HTMLElement).dataset.slot as ClothingSlotType;
      const slotItem = slotElement.querySelector('.slot-item') as HTMLElement;

      if (entityClothing?.has(slotType)) {
        const fittedClothing = entityClothing.get(slotType)!;
        slotElement.classList.add('has-item');
        slotItem.innerHTML = `
          <div class="equipped-item">
            <div class="item-icon">${this.getItemIcon(fittedClothing.item)}</div>
            <div class="item-name">${fittedClothing.item.name}</div>
          </div>
        `;
      } else {
        slotElement.classList.remove('has-item');
        slotItem.innerHTML = '';
      }
    });
  }

  /**
   * 更新属性面板
   * @param item 服装项
   */
  private updatePropertiesPanel(item: ClothingItem): void {
    const inputs = this.elements.propertiesPanel.querySelectorAll('[data-property]');

    inputs.forEach(input => {
      const property = (input as HTMLElement).dataset.property;
      const element = input as HTMLInputElement | HTMLSelectElement;

      switch (property) {
        case 'name':
          element.value = item.name;
          break;
        case 'category':
          element.value = item.category;
          break;
        case 'material':
          element.value = item.materialType;
          break;
        case 'color':
          element.value = item.materialProperties.color || '#ffffff';
          break;
        case 'physics':
          (element as HTMLInputElement).checked = item.physics.enabled;
          break;
      }
    });
  }

  /**
   * 更新服装项属性
   * @param itemId 服装项ID
   * @param property 属性名
   * @param value 属性值
   */
  private updateItemProperty(itemId: string, property: string, value: any): void {
    const item = this.clothingSystem.getClothingItem(itemId);
    if (!item) return;

    switch (property) {
      case 'name':
        item.name = value;
        break;
      case 'category':
        item.category = value;
        break;
      case 'material':
        item.materialType = value;
        break;
      case 'color':
        item.materialProperties.color = value;
        break;
      case 'physics':
        item.physics.enabled = value;
        break;
    }

    this.emit('itemPropertyChanged', itemId, property, value);
  }

  /**
   * 创建新服装组合
   */
  private createNewOutfit(): void {
    const name = prompt('请输入服装组合名称:');
    if (!name) return;

    if (!this.currentEntityId) {
      alert('请先选择一个数字人');
      return;
    }

    const entityClothing = this.clothingSystem['equippedClothing'].get(this.currentEntityId);
    if (!entityClothing || entityClothing.size === 0) {
      alert('当前数字人没有装备任何服装');
      return;
    }

    const items = new Map<ClothingSlotType, string>();
    for (const [slotType, fittedClothing] of entityClothing) {
      items.set(slotType, fittedClothing.item.id);
    }

    const outfit = this.clothingSystem.createOutfit(name, items);
    this.updateOutfitList();
    this.emit('outfitCreated', outfit);
  }

  /**
   * 保存当前服装组合
   */
  private saveCurrentOutfit(): void {
    if (this.selectedOutfit) {
      // 更新现有组合
      this.emit('outfitSaved', this.selectedOutfit);
    } else {
      // 创建新组合
      this.createNewOutfit();
    }
  }

  /**
   * 显示服装组合选择器
   */
  private showOutfitSelector(): void {
    // 这里可以实现一个模态对话框来选择服装组合
    this.emit('showOutfitSelector');
  }

  /**
   * 显示插槽菜单
   * @param slotType 插槽类型
   * @param slotElement 插槽元素
   */
  private showSlotMenu(slotType: ClothingSlotType, slotElement: HTMLElement): void {
    // 这里可以实现一个上下文菜单
    this.emit('showSlotMenu', slotType, slotElement);
  }

  /**
   * 撤销操作
   */
  private undo(): void {
    this.emit('undo');
  }

  /**
   * 重做操作
   */
  private redo(): void {
    this.emit('redo');
  }

  /**
   * 销毁编辑器
   */
  public dispose(): void {
    this.removeAllListeners();
    this.elements.container.innerHTML = '';
  }
}
