/**
 * 完整的语音增强RAG数字人交互系统
 * 集成语音识别、语音合成、RAG对话、数字人控制的完整演示
 */

import * as THREE from 'three';
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { Scene } from '../engine/src/core/Scene';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/components/Transform';

// RAG系统组件
import { KnowledgeSceneEditor } from '../engine/src/rag/scene/KnowledgeSceneEditor';
import { DigitalHumanPathEditor } from '../engine/src/rag/navigation/DigitalHumanPathEditor';
import { DigitalHumanNavigationComponent } from '../engine/src/rag/navigation/DigitalHumanNavigationComponent';
import { KnowledgeBaseService, KnowledgeBaseConfig } from '../engine/src/rag/knowledge/KnowledgeBaseService';
import { RAGRetrievalEngine, RetrievalConfig, RetrievalStrategy } from '../engine/src/rag/retrieval/RAGRetrievalEngine';
import { DialogueManager } from '../engine/src/rag/dialogue/DialogueManager';

// 语音系统组件
import { 
  SpeechRecognitionService, 
  SpeechRecognitionFactory,
  SpeechRecognitionConfig 
} from '../engine/src/rag/speech/SpeechRecognitionService';
import { 
  SpeechSynthesisService, 
  SpeechSynthesisFactory,
  SpeechSynthesisConfig 
} from '../engine/src/rag/speech/SpeechSynthesisService';

// 交互控制组件
import { 
  DigitalHumanInteractionController,
  DigitalHumanState,
  InteractionConfig 
} from '../engine/src/rag/interaction/DigitalHumanInteractionController';

/**
 * 完整语音增强RAG系统示例类
 */
export class CompleteVoiceEnabledRAGSystem {
  private engine: Engine;
  private world: World;
  private scene: Scene;
  private camera: THREE.Camera;
  
  // RAG系统核心组件
  private knowledgeBase: KnowledgeBaseService;
  private ragEngine: RAGRetrievalEngine;
  private dialogueManager: DialogueManager;
  
  // 语音系统组件
  private speechRecognition: SpeechRecognitionService;
  private speechSynthesis: SpeechSynthesisService;
  
  // 场景和交互组件
  private knowledgeSceneEditor: KnowledgeSceneEditor;
  private pathEditor: DigitalHumanPathEditor;
  private digitalHuman: Entity;
  private interactionController: DigitalHumanInteractionController;
  
  // 系统状态
  private currentSessionId: string = 'voice-demo-session';
  private isSystemReady: boolean = false;
  private isVoiceEnabled: boolean = false;

  constructor(canvas: HTMLCanvasElement) {
    this.engine = new Engine({
      canvas,
      autoStart: true,
      debug: true
    });

    this.world = this.engine.getWorld();
    
    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private async initializeSystem(): Promise<void> {
    console.log('初始化完整语音增强RAG系统...');

    try {
      // 检查浏览器支持
      await this.checkBrowserSupport();
      
      // 初始化RAG系统
      await this.initializeRAGSystem();
      
      // 初始化语音系统
      await this.initializeVoiceSystem();
      
      // 初始化场景
      this.initializeScene();
      
      // 初始化交互控制器
      this.initializeInteractionController();
      
      // 设置演示数据
      await this.setupDemoData();
      
      // 设置UI
      this.setupUI();
      
      this.isSystemReady = true;
      console.log('系统初始化完成');
      
    } catch (error) {
      console.error('系统初始化失败:', error);
      this.showError('系统初始化失败，请检查浏览器支持和权限设置');
    }
  }

  /**
   * 检查浏览器支持
   */
  private async checkBrowserSupport(): Promise<void> {
    const speechSupport = SpeechRecognitionFactory.checkBrowserSupport();
    
    if (!speechSupport.supported) {
      throw new Error(`浏览器不支持语音功能: ${speechSupport.recommendations.join(', ')}`);
    }
    
    console.log('浏览器支持检查通过');
  }

  /**
   * 初始化RAG系统
   */
  private async initializeRAGSystem(): Promise<void> {
    // 配置知识库
    const knowledgeBaseConfig: KnowledgeBaseConfig = {
      chunkSize: 500,
      chunkOverlap: 50,
      embeddingModel: 'simple-embedding',
      vectorStoreConfig: {},
      embeddingConfig: {},
      maxDocumentSize: 10 * 1024 * 1024,
      supportedFileTypes: ['text/plain', 'application/pdf', 'text/html']
    };

    this.knowledgeBase = new KnowledgeBaseService(knowledgeBaseConfig);

    // 配置RAG检索引擎
    const retrievalConfig: RetrievalConfig = {
      strategy: RetrievalStrategy.HYBRID,
      maxResults: 5,
      semanticWeight: 0.7,
      keywordWeight: 0.3,
      contextWeight: 0.2,
      diversityThreshold: 0.8,
      relevanceThreshold: 0.6,
      useReranking: true,
      enableContextualFiltering: true
    };

    this.ragEngine = new RAGRetrievalEngine(this.knowledgeBase, retrievalConfig);
    this.dialogueManager = new DialogueManager(this.ragEngine);
  }

  /**
   * 初始化语音系统
   */
  private async initializeVoiceSystem(): Promise<void> {
    // 配置语音识别
    const recognitionConfig: SpeechRecognitionConfig = SpeechRecognitionFactory.getRecommendedConfig('exhibition');
    this.speechRecognition = SpeechRecognitionFactory.createService(recognitionConfig);

    // 配置语音合成
    const synthesisConfig: SpeechSynthesisConfig = SpeechSynthesisFactory.getRecommendedConfig('exhibition');
    this.speechSynthesis = SpeechSynthesisFactory.createService(synthesisConfig);

    // 测试语音识别
    const recognitionTest = await this.speechRecognition.testRecognition();
    if (!recognitionTest) {
      console.warn('语音识别测试失败，可能需要用户授权');
    }

    console.log('语音系统初始化完成');
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建场景
    this.scene = new Scene(this.world, {
      name: '语音增强RAG系统演示场景'
    });
    
    this.world.addScene(this.scene);
    this.world.setActiveScene(this.scene);

    // 创建相机
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);
    camera.lookAt(0, 0, 0);
    this.camera = camera;

    // 创建环境
    this.createEnvironment();
    
    // 创建数字人
    this.createDigitalHuman();
    
    // 初始化场景编辑器
    this.knowledgeSceneEditor = new KnowledgeSceneEditor(
      this.world,
      this.scene.getThreeScene(),
      this.camera
    );
    
    // 初始化路径编辑器
    this.pathEditor = new DigitalHumanPathEditor(
      this.scene.getThreeScene(),
      {
        pathType: 'digital_human_navigation',
        allowCurves: true,
        showDirection: true,
        enableStops: true,
        segments: 50,
        debug: true
      }
    );
  }

  /**
   * 创建环境
   */
  private createEnvironment(): void {
    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    this.scene.getThreeScene().add(ground);

    // 创建展品
    const exhibits = [
      { name: '古代青铜器', position: new THREE.Vector3(-5, 1, -3), color: 0xcd7f32 },
      { name: '现代雕塑', position: new THREE.Vector3(0, 1, -5), color: 0x708090 },
      { name: '科技展示', position: new THREE.Vector3(5, 1, -3), color: 0x4169e1 },
      { name: '历史文物', position: new THREE.Vector3(-3, 1, 2), color: 0xdaa520 },
      { name: '艺术作品', position: new THREE.Vector3(3, 1, 2), color: 0x9370db }
    ];

    exhibits.forEach(exhibit => {
      const geometry = new THREE.BoxGeometry(1, 2, 1);
      const material = new THREE.MeshLambertMaterial({ color: exhibit.color });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(exhibit.position);
      mesh.userData = { name: exhibit.name };
      this.scene.getThreeScene().add(mesh);
    });

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.getThreeScene().add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    this.scene.getThreeScene().add(directionalLight);
  }

  /**
   * 创建数字人
   */
  private createDigitalHuman(): void {
    this.digitalHuman = this.world.createEntity('数字人');
    
    // 创建数字人模型
    const geometry = new THREE.CapsuleGeometry(0.3, 1.5, 4, 8);
    const material = new THREE.MeshLambertMaterial({ color: 0x8e44ad });
    const mesh = new THREE.Mesh(geometry, material);
    
    this.digitalHuman.addComponent(new Transform(this.digitalHuman, {
      position: new THREE.Vector3(0, 1, 0)
    }));
    
    // 添加导航组件
    const navigationComponent = new DigitalHumanNavigationComponent(
      this.digitalHuman,
      {
        moveSpeed: 2.0,
        rotationSpeed: 5.0,
        stopThreshold: 0.5,
        smoothRotation: true,
        autoStart: false,
        debug: true
      }
    );
    
    this.digitalHuman.addComponent(navigationComponent);
    
    // 将mesh添加到场景
    this.scene.getThreeScene().add(mesh);
  }

  /**
   * 初始化交互控制器
   */
  private initializeInteractionController(): void {
    const interactionConfig: InteractionConfig = {
      enableVoiceInput: true,
      enableVoiceOutput: true,
      enableGestures: true,
      enableFacialExpressions: true,
      enableLipSync: true,
      responseDelay: 500,
      idleTimeout: 30000, // 30秒
      maxConversationLength: 10,
      autoNavigation: true
    };

    this.interactionController = new DigitalHumanInteractionController(
      this.digitalHuman,
      this.speechRecognition,
      this.speechSynthesis,
      this.dialogueManager,
      interactionConfig,
      this.currentSessionId
    );

    this.digitalHuman.addComponent(this.interactionController);

    // 设置数字人模型
    const mesh = this.scene.getThreeScene().getObjectByName('数字人模型');
    if (mesh) {
      this.interactionController.setModel(mesh);
    }

    // 设置事件处理器
    this.setupInteractionEventHandlers();
  }

  /**
   * 设置交互事件处理器
   */
  private setupInteractionEventHandlers(): void {
    this.interactionController.onStateChanged = (state) => {
      this.updateStateDisplay(state);
    };

    this.interactionController.onResponse = (response) => {
      this.displayResponse(response);
    };

    this.interactionController.onInteractionEvent = (event) => {
      this.logInteractionEvent(event);
    };
  }

  /**
   * 设置演示数据
   */
  private async setupDemoData(): Promise<void> {
    console.log('设置演示数据...');

    // 创建示例文档
    const documents = [
      {
        content: '古代青铜器展区展示了商周时期的珍贵青铜器，包括鼎、爵、觚等礼器。这些青铜器工艺精湛，纹饰华美，体现了古代工匠的高超技艺和深厚的文化内涵。青铜器不仅是实用器具，更是权力和地位的象征。',
        metadata: {
          title: '古代青铜器展区介绍',
          category: 'cultural',
          tags: ['古代', '青铜器', '商周', '礼器'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '古代青铜器展区的详细介绍'
        }
      },
      {
        content: '现代雕塑展区汇集了20世纪以来的优秀雕塑作品，展现了现代艺术的创新精神。这些作品运用了多种材料和技法，从传统的石雕、铜雕到现代的钢铁、玻璃等新材料，体现了艺术家对形式美和空间关系的探索。',
        metadata: {
          title: '现代雕塑展区介绍',
          category: 'art',
          tags: ['现代', '雕塑', '艺术', '创新'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '现代雕塑展区的详细介绍'
        }
      },
      {
        content: '科技展示区通过互动体验展示了人类科技发展的重要成果。从古代的四大发明到现代的人工智能、虚拟现实技术，参观者可以亲身体验科技的魅力。展区设有多个互动装置，让观众在游戏中学习科学知识。',
        metadata: {
          title: '科技展示区介绍',
          category: 'technology',
          tags: ['科技', '互动', '体验', '创新'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '科技展示区的详细介绍'
        }
      }
    ];

    // 上传文档到知识库
    for (const doc of documents) {
      const file = new File([doc.content], `${doc.metadata.title}.txt`, { type: 'text/plain' });
      await this.knowledgeBase.uploadDocument(file, doc.metadata);
    }

    // 添加知识点到场景
    const knowledgePoints = [
      {
        position: new THREE.Vector3(-5, 2.5, -3),
        knowledge: {
          id: 'kp1',
          title: '古代青铜器',
          content: '这里展示的是商周时期的珍贵青铜器，工艺精湛，文化内涵深厚。',
          type: 'text' as const,
          tags: ['古代', '青铜器'],
          category: 'cultural',
          priority: 2,
          relatedTopics: ['历史', '文化']
        }
      },
      {
        position: new THREE.Vector3(0, 2.5, -5),
        knowledge: {
          id: 'kp2',
          title: '现代雕塑',
          content: '现代雕塑展现了艺术家对形式美和空间关系的创新探索。',
          type: 'text' as const,
          tags: ['现代', '雕塑'],
          category: 'art',
          priority: 2,
          relatedTopics: ['艺术', '创新']
        }
      },
      {
        position: new THREE.Vector3(5, 2.5, -3),
        knowledge: {
          id: 'kp3',
          title: '科技展示',
          content: '通过互动体验展示人类科技发展成果，让观众在游戏中学习。',
          type: 'text' as const,
          tags: ['科技', '互动'],
          category: 'technology',
          priority: 2,
          relatedTopics: ['科技', '体验']
        }
      }
    ];

    // 添加知识点
    knowledgePoints.forEach(kp => {
      this.knowledgeSceneEditor.addKnowledgePoint(kp.position, kp.knowledge);
    });

    // 创建导览路径
    const pathPoints = [
      { position: new THREE.Vector3(0, 0.1, 5), type: 'normal' as const },
      { position: new THREE.Vector3(-5, 0.1, -1), type: 'stop' as const },
      { position: new THREE.Vector3(-2, 0.1, -5), type: 'waypoint' as const },
      { position: new THREE.Vector3(2, 0.1, -5), type: 'stop' as const },
      { position: new THREE.Vector3(5, 0.1, -1), type: 'stop' as const },
      { position: new THREE.Vector3(0, 0.1, 3), type: 'normal' as const }
    ];

    pathPoints.forEach(point => {
      this.pathEditor.addPathPoint(point.position, point.type, {
        waitTime: point.type === 'stop' ? 3000 : 0,
        actions: point.type === 'stop' ? ['介绍展品', '播放动画'] : []
      });
    });

    // 设置路径到导航组件
    const navigationComponent = this.digitalHuman.getComponent<DigitalHumanNavigationComponent>(
      DigitalHumanNavigationComponent.TYPE
    );
    if (navigationComponent) {
      navigationComponent.setPath(this.pathEditor);
    }

    console.log('演示数据设置完成');
  }

  /**
   * 启用语音功能
   */
  public async enableVoice(): Promise<boolean> {
    try {
      // 请求麦克风权限
      await navigator.mediaDevices.getUserMedia({ audio: true });

      this.isVoiceEnabled = true;
      this.interactionController.startListening();

      console.log('语音功能已启用');
      this.updateVoiceStatus('语音功能已启用，可以开始对话');

      // 播放欢迎语音
      await this.speechSynthesis.speak(
        '您好！欢迎来到虚拟展厅。我是您的数字导览员，您可以通过语音与我交流。请问有什么可以帮助您的吗？'
      );

      return true;
    } catch (error) {
      console.error('启用语音功能失败:', error);
      this.updateVoiceStatus('语音功能启用失败，请检查麦克风权限');
      return false;
    }
  }

  /**
   * 禁用语音功能
   */
  public disableVoice(): void {
    this.isVoiceEnabled = false;
    this.interactionController.stopListening();
    this.speechSynthesis.stop();

    console.log('语音功能已禁用');
    this.updateVoiceStatus('语音功能已禁用');
  }

  /**
   * 更新状态显示
   */
  private updateStateDisplay(state: DigitalHumanState): void {
    const stateDiv = document.getElementById('digital-human-state');
    if (stateDiv) {
      const stateNames: Record<DigitalHumanState, string> = {
        [DigitalHumanState.IDLE]: '待机',
        [DigitalHumanState.LISTENING]: '聆听中',
        [DigitalHumanState.THINKING]: '思考中',
        [DigitalHumanState.SPEAKING]: '说话中',
        [DigitalHumanState.MOVING]: '移动中',
        [DigitalHumanState.INTERACTING]: '交互中',
        [DigitalHumanState.ERROR]: '错误'
      };

      stateDiv.innerHTML = `
        <p><strong>数字人状态:</strong> ${stateNames[state]}</p>
        <p><strong>语音状态:</strong> ${this.isVoiceEnabled ? '已启用' : '已禁用'}</p>
        <p><strong>识别状态:</strong> ${this.speechRecognition.getState()}</p>
        <p><strong>合成状态:</strong> ${this.speechSynthesis.getState()}</p>
      `;
    }
  }

  /**
   * 显示响应
   */
  private displayResponse(response: any): void {
    const responseDiv = document.getElementById('response-display');
    if (responseDiv) {
      responseDiv.innerHTML = `
        <div class="response-item">
          <h4>数字人回复:</h4>
          <p>${response.text}</p>

          <div class="response-metadata">
            <p><strong>意图:</strong> ${response.intent.intent} (${(response.intent.confidence * 100).toFixed(1)}%)</p>
            <p><strong>情感:</strong> ${response.emotion.emotion} (${(response.emotion.intensity * 100).toFixed(1)}%)</p>
            <p><strong>动作数量:</strong> ${response.actions.actions.length}</p>
            <p><strong>处理时间:</strong> ${response.metadata.processingTime}ms</p>
          </div>

          ${response.followUpQuestions.length > 0 ? `
            <div class="follow-up-questions">
              <h5>您可能还想了解:</h5>
              <ul>
                ${response.followUpQuestions.map((q: string) => `<li>${q}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `;
    }
  }

  /**
   * 记录交互事件
   */
  private logInteractionEvent(event: any): void {
    const logDiv = document.getElementById('interaction-log');
    if (logDiv) {
      const logItem = document.createElement('div');
      logItem.className = 'log-item';
      logItem.innerHTML = `
        <span class="timestamp">${event.timestamp.toLocaleTimeString()}</span>
        <span class="event-type">[${event.type}]</span>
        <span class="event-source">(${event.source})</span>
        <span class="event-data">${JSON.stringify(event.data).substring(0, 100)}...</span>
      `;

      logDiv.appendChild(logItem);
      logDiv.scrollTop = logDiv.scrollHeight;

      // 限制日志条目数量
      while (logDiv.children.length > 50) {
        logDiv.removeChild(logDiv.firstChild!);
      }
    }
  }

  /**
   * 更新语音状态
   */
  private updateVoiceStatus(message: string): void {
    const statusDiv = document.getElementById('voice-status');
    if (statusDiv) {
      statusDiv.textContent = message;
    }
  }

  /**
   * 显示错误
   */
  private showError(message: string): void {
    const errorDiv = document.getElementById('error-display');
    if (errorDiv) {
      errorDiv.innerHTML = `
        <div class="error-message">
          <strong>错误:</strong> ${message}
        </div>
      `;
      errorDiv.style.display = 'block';
    }
  }

  /**
   * 设置UI界面
   */
  private setupUI(): void {
    // 创建主控制面板
    const controlPanel = document.createElement('div');
    controlPanel.id = 'voice-rag-control-panel';
    controlPanel.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      width: 380px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 20px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-height: 85vh;
      overflow-y: auto;
    `;

    controlPanel.innerHTML = `
      <h3 style="margin: 0 0 15px 0; color: #4CAF50;">语音增强RAG数字人系统</h3>

      <div class="voice-control" style="margin: 15px 0;">
        <h4>语音控制</h4>
        <button id="enable-voice" style="margin: 5px; padding: 8px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
          启用语音
        </button>
        <button id="disable-voice" style="margin: 5px; padding: 8px 15px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
          禁用语音
        </button>
        <div id="voice-status" style="margin-top: 10px; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">
          语音功能未启用
        </div>
      </div>

      <div class="system-status">
        <h4>系统状态</h4>
        <div id="digital-human-state">初始化中...</div>
      </div>

      <div class="text-input" style="margin: 15px 0;">
        <h4>文字输入</h4>
        <input type="text" id="text-query" placeholder="您也可以输入文字问题..."
               style="width: 100%; padding: 8px; border: none; border-radius: 4px; margin-bottom: 10px;">
        <button id="submit-text" style="width: 100%; padding: 8px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
          发送文字
        </button>
      </div>

      <div class="quick-commands" style="margin: 15px 0;">
        <h4>快速命令</h4>
        <button class="voice-cmd-btn" data-cmd="你好">问候</button>
        <button class="voice-cmd-btn" data-cmd="开始导览">开始导览</button>
        <button class="voice-cmd-btn" data-cmd="介绍古代青铜器">青铜器</button>
        <button class="voice-cmd-btn" data-cmd="介绍现代雕塑">现代雕塑</button>
        <button class="voice-cmd-btn" data-cmd="介绍科技展示">科技展示</button>
        <button class="voice-cmd-btn" data-cmd="停止导览">停止导览</button>
      </div>

      <div class="error-display" id="error-display" style="display: none; margin: 15px 0; padding: 10px; background: #f44336; border-radius: 4px;">
      </div>
    `;

    document.body.appendChild(controlPanel);

    // 创建响应显示面板
    const responsePanel = document.createElement('div');
    responsePanel.id = 'voice-response-panel';
    responsePanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 450px;
      background: rgba(255, 255, 255, 0.95);
      color: black;
      padding: 20px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-height: 85vh;
      overflow-y: auto;
    `;

    responsePanel.innerHTML = `
      <h3 style="margin: 0 0 15px 0; color: #333;">系统响应</h3>
      <div id="response-display">等待用户输入...</div>

      <h4 style="margin: 20px 0 10px 0;">交互日志</h4>
      <div id="interaction-log" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px; font-size: 12px;">
        <p>交互日志将显示在这里...</p>
      </div>
    `;

    document.body.appendChild(responsePanel);

    // 设置事件监听器
    this.setupUIEventListeners();

    // 添加样式
    this.addUIStyles();
  }

  /**
   * 设置UI事件监听器
   */
  private setupUIEventListeners(): void {
    // 语音控制按钮
    const enableVoiceBtn = document.getElementById('enable-voice');
    const disableVoiceBtn = document.getElementById('disable-voice');

    if (enableVoiceBtn) {
      enableVoiceBtn.addEventListener('click', () => {
        this.enableVoice();
      });
    }

    if (disableVoiceBtn) {
      disableVoiceBtn.addEventListener('click', () => {
        this.disableVoice();
      });
    }

    // 文字输入
    const textInput = document.getElementById('text-query') as HTMLInputElement;
    const submitTextBtn = document.getElementById('submit-text');

    if (textInput && submitTextBtn) {
      const handleTextSubmit = async () => {
        const query = textInput.value.trim();
        if (query) {
          await this.processTextInput(query);
          textInput.value = '';
        }
      };

      submitTextBtn.addEventListener('click', handleTextSubmit);
      textInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          handleTextSubmit();
        }
      });
    }

    // 快速命令按钮
    document.querySelectorAll('.voice-cmd-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const cmd = (e.target as HTMLElement).getAttribute('data-cmd');
        if (cmd) {
          await this.processTextInput(cmd);
        }
      });
    });
  }

  /**
   * 处理文字输入
   */
  private async processTextInput(text: string): Promise<void> {
    if (!this.isSystemReady) {
      this.showError('系统尚未准备就绪');
      return;
    }

    console.log(`处理文字输入: ${text}`);

    try {
      const response = await this.dialogueManager.processUserInput(
        this.currentSessionId,
        text,
        {
          currentLocation: '展厅中央',
          userProfile: {
            id: 'demo-user',
            preferences: ['历史', '艺术'],
            interests: ['文化', '科技'],
            language: 'zh',
            expertiseLevel: 'intermediate',
            visitHistory: []
          }
        }
      );

      this.displayResponse(response);

      // 如果启用了语音输出，播放回复
      if (this.isVoiceEnabled) {
        await this.speechSynthesis.speak(
          response.text,
          response.emotion.emotion,
          response.emotion.intensity
        );
      }

      // 记录交互事件
      this.logInteractionEvent({
        type: 'text_input',
        data: { input: text, response: response.text },
        timestamp: new Date(),
        source: 'text'
      });

    } catch (error) {
      console.error('处理文字输入时出错:', error);
      this.showError('处理输入时出错，请稍后再试');
    }
  }

  /**
   * 添加UI样式
   */
  private addUIStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .voice-cmd-btn {
        margin: 3px;
        padding: 5px 10px;
        background: #607D8B;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }

      .voice-cmd-btn:hover {
        background: #455A64;
      }

      .response-item {
        margin-bottom: 15px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: #f9f9f9;
      }

      .response-metadata {
        font-size: 12px;
        color: #666;
        margin-top: 10px;
        padding: 8px;
        background: #e8e8e8;
        border-radius: 4px;
      }

      .follow-up-questions {
        margin-top: 10px;
        padding: 10px;
        background: #e3f2fd;
        border-radius: 4px;
      }

      .follow-up-questions ul {
        margin: 5px 0;
        padding-left: 20px;
      }

      .log-item {
        margin-bottom: 5px;
        padding: 5px;
        border-left: 3px solid #4CAF50;
        background: #f5f5f5;
        font-family: monospace;
        font-size: 11px;
      }

      .timestamp {
        color: #666;
        margin-right: 8px;
      }

      .event-type {
        color: #2196F3;
        font-weight: bold;
        margin-right: 8px;
      }

      .event-source {
        color: #FF9800;
        margin-right: 8px;
      }

      .event-data {
        color: #333;
      }

      .error-message {
        color: white;
        font-weight: bold;
      }

      #digital-human-state p {
        margin: 5px 0;
        padding: 5px;
        background: rgba(255,255,255,0.1);
        border-radius: 3px;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 启动系统
   */
  public async start(): Promise<void> {
    console.log('启动语音增强RAG系统...');

    // 等待系统初始化完成
    while (!this.isSystemReady) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 更新状态显示
    setInterval(() => {
      if (this.isSystemReady) {
        this.updateStateDisplay(this.interactionController.getState());
      }
    }, 1000);

    console.log('语音增强RAG系统启动完成！');

    // 显示启动提示
    this.updateVoiceStatus('系统已启动，点击"启用语音"开始语音交互');
  }

  /**
   * 获取系统统计信息
   */
  public async getSystemStats(): Promise<any> {
    const knowledgeStats = await this.knowledgeBase.getStats();
    const interactionStats = this.interactionController.getStatistics();

    return {
      system: {
        ready: this.isSystemReady,
        voiceEnabled: this.isVoiceEnabled
      },
      knowledgeBase: knowledgeStats,
      interaction: interactionStats,
      speech: {
        recognition: this.speechRecognition.getStatistics(),
        synthesis: this.speechSynthesis.getStatistics()
      },
      scene: {
        knowledgePoints: this.knowledgeSceneEditor.getAllKnowledgePoints().length,
        pathPoints: this.pathEditor.getPathPoints().length
      }
    };
  }

  /**
   * 测试语音功能
   */
  public async testVoiceFeatures(): Promise<{
    recognition: boolean;
    synthesis: boolean;
    overall: boolean;
  }> {
    console.log('测试语音功能...');

    const results = {
      recognition: false,
      synthesis: false,
      overall: false
    };

    try {
      // 测试语音识别
      results.recognition = await this.speechRecognition.testRecognition();

      // 测试语音合成
      try {
        await this.speechSynthesis.speak('语音合成测试');
        results.synthesis = true;
      } catch (error) {
        console.error('语音合成测试失败:', error);
      }

      results.overall = results.recognition && results.synthesis;

    } catch (error) {
      console.error('语音功能测试失败:', error);
    }

    return results;
  }

  /**
   * 重置系统
   */
  public reset(): void {
    console.log('重置系统...');

    // 停止语音功能
    this.disableVoice();

    // 重置对话管理器
    this.dialogueManager.clearSession(this.currentSessionId);

    // 重置交互控制器状态
    this.interactionController.updateConfig({
      enableVoiceInput: false,
      enableVoiceOutput: false
    });

    // 清理UI显示
    const responseDiv = document.getElementById('response-display');
    if (responseDiv) {
      responseDiv.innerHTML = '系统已重置，等待用户输入...';
    }

    const logDiv = document.getElementById('interaction-log');
    if (logDiv) {
      logDiv.innerHTML = '<p>交互日志已清空...</p>';
    }

    this.updateVoiceStatus('系统已重置');

    console.log('系统重置完成');
  }

  /**
   * 导出系统配置
   */
  public exportConfig(): any {
    return {
      speechRecognition: this.speechRecognition.getConfig(),
      speechSynthesis: this.speechSynthesis.getConfig(),
      interaction: this.interactionController.getConfig(),
      rag: this.ragEngine.getConfig(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 导入系统配置
   */
  public importConfig(config: any): void {
    try {
      if (config.speechRecognition) {
        this.speechRecognition.updateConfig(config.speechRecognition);
      }

      if (config.speechSynthesis) {
        this.speechSynthesis.updateConfig(config.speechSynthesis);
      }

      if (config.interaction) {
        this.interactionController.updateConfig(config.interaction);
      }

      if (config.rag) {
        this.ragEngine.updateConfig(config.rag);
      }

      console.log('配置导入成功');
    } catch (error) {
      console.error('配置导入失败:', error);
      this.showError('配置导入失败');
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    console.log('销毁语音增强RAG系统...');

    // 停止语音功能
    this.disableVoice();

    // 销毁组件
    this.interactionController.dispose();
    this.speechRecognition.dispose();
    this.speechSynthesis.dispose();
    this.knowledgeSceneEditor.dispose();
    this.pathEditor.dispose();

    // 清理知识库
    this.knowledgeBase.clearAll();

    // 销毁引擎
    this.engine.dispose();

    // 清理UI
    const controlPanel = document.getElementById('voice-rag-control-panel');
    const responsePanel = document.getElementById('voice-response-panel');

    if (controlPanel) controlPanel.remove();
    if (responsePanel) responsePanel.remove();

    console.log('系统销毁完成');
  }
}

// 导出示例类
export default CompleteVoiceEnabledRAGSystem;
