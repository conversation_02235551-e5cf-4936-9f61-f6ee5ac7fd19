#!/bin/bash

# M4数字人制作系统生产环境部署脚本
# 版本: M4.0.0
# 日期: 2025-07-09

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${1:-production}"
BACKUP_DIR="/backup/digital-human"
LOG_FILE="/var/log/digital-human-deploy.log"

# 检查参数
if [[ ! "$DEPLOYMENT_ENV" =~ ^(development|staging|production)$ ]]; then
    log_error "无效的部署环境: $DEPLOYMENT_ENV"
    log_info "使用方法: $0 [development|staging|production]"
    exit 1
fi

log_info "开始部署M4数字人制作系统到 $DEPLOYMENT_ENV 环境..."

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查磁盘空间 (至少需要50GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    required_space=52428800  # 50GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_error "磁盘空间不足，至少需要50GB可用空间"
        exit 1
    fi
    
    # 检查内存 (至少需要16GB)
    total_memory=$(free -m | awk 'NR==2{print $2}')
    required_memory=16384  # 16GB in MB
    
    if [ "$total_memory" -lt "$required_memory" ]; then
        log_warning "内存可能不足，建议至少16GB内存"
    fi
    
    # 检查GPU (如果可用)
    if command -v nvidia-smi &> /dev/null; then
        log_info "检测到NVIDIA GPU，将启用GPU加速"
        export GPU_ENABLED=true
    else
        log_warning "未检测到NVIDIA GPU，将使用CPU模式"
        export GPU_ENABLED=false
    fi
    
    log_success "系统要求检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "/var/log/digital-human"
        "/var/lib/digital-human"
        "$BACKUP_DIR"
        "/etc/digital-human"
        "/opt/digital-human/ssl"
        "/opt/digital-human/uploads"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            sudo mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    # 设置权限
    sudo chown -R $USER:$USER /var/log/digital-human
    sudo chown -R $USER:$USER /var/lib/digital-human
    sudo chown -R $USER:$USER /opt/digital-human
    
    log_success "目录创建完成"
}

# 备份现有数据
backup_data() {
    if [ "$DEPLOYMENT_ENV" = "production" ]; then
        log_info "备份现有数据..."
        
        timestamp=$(date +"%Y%m%d_%H%M%S")
        backup_path="$BACKUP_DIR/backup_$timestamp"
        
        mkdir -p "$backup_path"
        
        # 备份数据库
        if docker ps | grep -q "dh-database"; then
            log_info "备份数据库..."
            docker exec dh-database mysqldump -u root -p$DB_ROOT_PASSWORD digital_human_prod > "$backup_path/database.sql"
        fi
        
        # 备份MinIO数据
        if [ -d "/var/lib/digital-human/minio" ]; then
            log_info "备份存储数据..."
            tar -czf "$backup_path/minio_data.tar.gz" -C /var/lib/digital-human minio
        fi
        
        # 备份配置文件
        if [ -d "/etc/digital-human" ]; then
            log_info "备份配置文件..."
            tar -czf "$backup_path/config.tar.gz" -C /etc digital-human
        fi
        
        log_success "数据备份完成: $backup_path"
    fi
}

# 加载环境变量
load_environment() {
    log_info "加载环境变量..."
    
    env_file="$SCRIPT_DIR/.env.$DEPLOYMENT_ENV"
    
    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件不存在: $env_file"
        exit 1
    fi
    
    # 导出环境变量
    set -a
    source "$env_file"
    set +a
    
    log_success "环境变量加载完成"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -t digital-human-frontend:latest -f frontend/Dockerfile frontend/
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t digital-human-backend:latest -f backend/Dockerfile backend/
    
    # 构建AI服务镜像
    log_info "构建AI服务镜像..."
    docker build -t digital-human-ai:latest -f ai-service/Dockerfile ai-service/
    
    # 构建Worker镜像
    log_info "构建Worker镜像..."
    docker build -t digital-human-worker:latest -f worker/Dockerfile worker/
    
    # 构建健康检查镜像
    log_info "构建健康检查镜像..."
    docker build -t digital-human-healthcheck:latest -f healthcheck/Dockerfile healthcheck/
    
    log_success "Docker镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    cd "$SCRIPT_DIR"
    
    # 停止现有服务
    if [ -f "docker-compose.$DEPLOYMENT_ENV.yml" ]; then
        log_info "停止现有服务..."
        docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" down
    fi
    
    # 启动新服务
    log_info "启动新服务..."
    docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    log_success "服务部署完成"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    # 等待数据库启动
    max_attempts=30
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker exec dh-database mysql -u root -p$DB_ROOT_PASSWORD -e "SELECT 1" &> /dev/null; then
            break
        fi
        
        attempt=$((attempt + 1))
        log_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 10
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_error "数据库启动超时"
        exit 1
    fi
    
    # 执行迁移脚本
    if [ -f "$SCRIPT_DIR/database/migrations.sql" ]; then
        log_info "执行数据库迁移脚本..."
        docker exec -i dh-database mysql -u root -p$DB_ROOT_PASSWORD digital_human_prod < "$SCRIPT_DIR/database/migrations.sql"
    fi
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    services=(
        "frontend:80"
        "backend:3000"
        "ai-service:5000"
        "database:3306"
        "redis:6379"
        "minio:9000"
    )
    
    failed_services=()
    
    for service in "${services[@]}"; do
        service_name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if docker ps | grep -q "dh-$service_name"; then
            if nc -z localhost $port; then
                log_success "$service_name 服务健康"
            else
                log_error "$service_name 服务端口 $port 不可访问"
                failed_services+=("$service_name")
            fi
        else
            log_error "$service_name 容器未运行"
            failed_services+=("$service_name")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "所有服务健康检查通过"
    else
        log_error "以下服务健康检查失败: ${failed_services[*]}"
        exit 1
    fi
}

# 配置SSL证书
setup_ssl() {
    if [ "$DEPLOYMENT_ENV" = "production" ]; then
        log_info "配置SSL证书..."
        
        ssl_dir="/opt/digital-human/ssl"
        
        # 检查是否已有证书
        if [ ! -f "$ssl_dir/cert.pem" ] || [ ! -f "$ssl_dir/key.pem" ]; then
            log_warning "SSL证书不存在，请手动配置证书文件:"
            log_info "  证书文件: $ssl_dir/cert.pem"
            log_info "  私钥文件: $ssl_dir/key.pem"
            log_info "  或使用Let's Encrypt自动获取证书"
        else
            log_success "SSL证书配置完成"
        fi
    fi
}

# 配置监控
setup_monitoring() {
    log_info "配置监控系统..."
    
    # 等待Prometheus启动
    max_attempts=10
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:9090/-/healthy &> /dev/null; then
            break
        fi
        
        attempt=$((attempt + 1))
        log_info "等待Prometheus启动... ($attempt/$max_attempts)"
        sleep 10
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_warning "Prometheus启动超时，请手动检查"
    else
        log_success "Prometheus监控配置完成"
    fi
    
    # 配置Grafana仪表板
    if curl -s http://localhost:3001/api/health &> /dev/null; then
        log_success "Grafana仪表板配置完成"
    else
        log_warning "Grafana启动失败，请手动检查"
    fi
}

# 清理旧资源
cleanup() {
    log_info "清理旧资源..."
    
    # 清理未使用的Docker镜像
    docker image prune -f
    
    # 清理未使用的Docker卷
    docker volume prune -f
    
    # 清理旧的日志文件 (保留30天)
    find /var/log/digital-human -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    log_success "资源清理完成"
}

# 生成部署报告
generate_report() {
    log_info "生成部署报告..."
    
    report_file="/var/log/digital-human/deployment-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
M4数字人制作系统部署报告
========================

部署时间: $(date)
部署环境: $DEPLOYMENT_ENV
部署版本: M4.0.0

系统信息:
- 操作系统: $(uname -a)
- Docker版本: $(docker --version)
- Docker Compose版本: $(docker-compose --version)
- 可用内存: $(free -h | awk 'NR==2{print $7}')
- 可用磁盘: $(df -h / | awk 'NR==2{print $4}')

服务状态:
$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")

网络配置:
$(docker network ls | grep dh-)

数据卷:
$(docker volume ls | grep digital-human)

部署完成时间: $(date)
EOF
    
    log_success "部署报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始M4数字人制作系统部署流程..."
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 执行部署步骤
    check_requirements
    create_directories
    load_environment
    backup_data
    build_images
    deploy_services
    migrate_database
    setup_ssl
    setup_monitoring
    health_check
    cleanup
    generate_report
    
    # 计算部署时间
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_success "M4数字人制作系统部署完成！"
    log_info "部署耗时: ${duration}秒"
    log_info "访问地址:"
    
    if [ "$DEPLOYMENT_ENV" = "production" ]; then
        log_info "  前端: https://digitalhuman.com"
        log_info "  API: https://api.digitalhuman.com"
        log_info "  管理后台: https://admin.digitalhuman.com"
    else
        log_info "  前端: http://localhost"
        log_info "  API: http://localhost:3000"
        log_info "  Grafana: http://localhost:3001"
        log_info "  Kibana: http://localhost:5601"
    fi
    
    log_info "监控地址:"
    log_info "  Prometheus: http://localhost:9090"
    log_info "  Grafana: http://localhost:3001"
    log_info "  Kibana: http://localhost:5601"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
