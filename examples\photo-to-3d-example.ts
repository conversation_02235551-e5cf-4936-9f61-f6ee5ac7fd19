/**
 * 照片到3D转换示例
 * 演示如何使用照片到3D转换管道生成数字人
 */
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { StorageService } from '../engine/src/storage/StorageService';
import { PhotoTo3DPipeline } from '../engine/src/avatar/generation/PhotoTo3DPipeline';

/**
 * 照片到3D转换示例类
 */
class PhotoTo3DExample {
  private engine: Engine;
  private world: World;
  private storageService: StorageService;
  private pipeline: PhotoTo3DPipeline;

  constructor() {
    // 初始化引擎
    this.engine = new Engine();
    this.world = this.engine.getWorld();

    // 初始化存储服务
    this.storageService = new StorageService({
      provider: 'minio',
      config: {
        endpoint: 'localhost:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin',
        useSSL: false
      },
      debug: true
    });

    // 初始化照片到3D转换管道
    this.pipeline = new PhotoTo3DPipeline(this.world, this.storageService, {
      debug: true,
      quality: 'high',
      generateTextures: true,
      generateNormalMaps: true,
      optimizeMesh: true,
      maxProcessingTime: 600 // 10分钟
    });
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    console.log('初始化照片到3D转换示例...');

    // 初始化存储服务
    await this.storageService.initialize();

    // 设置事件监听器
    this.setupEventListeners();

    console.log('照片到3D转换管道已准备就绪');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听转换开始
    this.pipeline.on('conversionStarted', (conversionId: string, file: File) => {
      console.log(`转换开始 [${conversionId}]: ${file.name}`);
    });

    // 监听转换进度
    this.pipeline.on('conversionProgress', (conversionId: string, progress: any) => {
      const timeRemaining = progress.estimatedTimeRemaining 
        ? ` (剩余 ${Math.round(progress.estimatedTimeRemaining)}s)`
        : '';
      
      console.log(`转换进度 [${conversionId}]: ${progress.stage} - ${progress.progress}%${timeRemaining}`);
      console.log(`  状态: ${progress.message}`);
    });

    // 监听转换完成
    this.pipeline.on('conversionCompleted', (conversionId: string, result: any) => {
      console.log(`转换完成 [${conversionId}]:`, {
        success: result.success,
        digitalHumanId: result.digitalHuman?.id,
        processingTime: `${result.processingTime?.toFixed(2)}s`,
        assetCount: result.assetIds?.length || 0
      });
    });

    // 监听转换错误
    this.pipeline.on('conversionError', (conversionId: string, error: any) => {
      console.error(`转换失败 [${conversionId}]:`, error.message);
    });
  }

  /**
   * 演示照片转换
   */
  public async demonstratePhotoConversion(): Promise<void> {
    console.log('\n=== 照片到3D转换演示 ===');

    // 创建模拟照片文件
    const photoFile = await this.createMockPhotoFile();
    
    console.log(`开始转换照片: ${photoFile.name}`);
    console.log(`文件大小: ${(photoFile.size / 1024 / 1024).toFixed(2)} MB`);

    try {
      const result = await this.pipeline.generateDigitalHumanFromPhoto(photoFile, {
        generateThumbnail: true,
        autoOptimize: true
      });

      if (result.success) {
        console.log('\n✅ 转换成功!');
        console.log('数字人详情:');
        console.log(`  ID: ${result.digitalHuman?.id}`);
        console.log(`  名称: ${result.digitalHuman?.name}`);
        console.log(`  处理时间: ${result.processingTime?.toFixed(2)}s`);
        console.log(`  生成资产: ${result.assetIds?.length || 0} 个`);
        
        if (result.warnings && result.warnings.length > 0) {
          console.log('⚠️ 警告:');
          result.warnings.forEach(warning => console.log(`  - ${warning}`));
        }

        // 显示数字人信息
        await this.displayDigitalHumanInfo(result.digitalHuman!);

      } else {
        console.error('❌ 转换失败:', result.error);
      }

    } catch (error) {
      console.error('❌ 转换异常:', error.message);
    }
  }

  /**
   * 演示批量转换
   */
  public async demonstrateBatchConversion(): Promise<void> {
    console.log('\n=== 批量照片转换演示 ===');

    // 创建多个模拟照片文件
    const photoFiles = await this.createMultipleMockPhotoFiles();
    
    console.log(`准备批量转换 ${photoFiles.length} 张照片`);

    const results = [];

    for (let i = 0; i < photoFiles.length; i++) {
      const file = photoFiles[i];
      console.log(`\n转换第 ${i + 1}/${photoFiles.length} 张照片: ${file.name}`);

      try {
        const result = await this.pipeline.generateDigitalHumanFromPhoto(file, {
          quality: i === 0 ? 'high' : 'medium', // 第一张用高质量
          generateNormalMaps: i < 2 // 前两张生成法线贴图
        });

        results.push(result);

        if (result.success) {
          console.log(`✅ 转换成功 (${result.processingTime?.toFixed(2)}s)`);
        } else {
          console.log(`❌ 转换失败: ${result.error}`);
        }

      } catch (error) {
        console.error(`❌ 转换异常: ${error.message}`);
        results.push({ success: false, error: error.message });
      }
    }

    // 统计结果
    const successCount = results.filter(r => r.success).length;
    const totalTime = results.reduce((sum, r) => sum + (r.processingTime || 0), 0);

    console.log('\n📊 批量转换统计:');
    console.log(`  成功: ${successCount}/${photoFiles.length}`);
    console.log(`  总耗时: ${totalTime.toFixed(2)}s`);
    console.log(`  平均耗时: ${(totalTime / photoFiles.length).toFixed(2)}s`);
  }

  /**
   * 演示质量对比
   */
  public async demonstrateQualityComparison(): Promise<void> {
    console.log('\n=== 质量对比演示 ===');

    const photoFile = await this.createMockPhotoFile();
    const qualities = ['low', 'medium', 'high', 'ultra'] as const;

    for (const quality of qualities) {
      console.log(`\n测试 ${quality} 质量...`);

      // 创建不同质量配置的管道
      const testPipeline = new PhotoTo3DPipeline(this.world, this.storageService, {
        debug: false,
        quality,
        generateTextures: true,
        generateNormalMaps: quality !== 'low',
        optimizeMesh: true
      });

      try {
        const result = await testPipeline.generateDigitalHumanFromPhoto(photoFile);

        if (result.success) {
          const modelComponent = result.digitalHuman?.getComponent('ModelComponent');
          const mesh = modelComponent?.mesh;
          
          console.log(`✅ ${quality} 质量结果:`);
          console.log(`  处理时间: ${result.processingTime?.toFixed(2)}s`);
          console.log(`  顶点数: ${mesh?.geometry.attributes.position.count || 0}`);
          console.log(`  面数: ${mesh?.geometry.index ? mesh.geometry.index.count / 3 : 0}`);
          console.log(`  资产数: ${result.assetIds?.length || 0}`);
        } else {
          console.log(`❌ ${quality} 质量失败: ${result.error}`);
        }

        testPipeline.dispose();

      } catch (error) {
        console.error(`❌ ${quality} 质量异常: ${error.message}`);
      }
    }
  }

  /**
   * 创建模拟照片文件
   */
  private async createMockPhotoFile(): Promise<File> {
    // 创建一个模拟的人脸照片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    canvas.width = 512;
    canvas.height = 512;

    // 绘制简单的人脸轮廓
    ctx.fillStyle = '#fdbcb4'; // 肤色
    ctx.fillRect(0, 0, 512, 512);

    // 绘制面部特征
    this.drawFacialFeatures(ctx);

    // 转换为Blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png');
    });

    return new File([blob], 'mock-face.png', { type: 'image/png' });
  }

  /**
   * 创建多个模拟照片文件
   */
  private async createMultipleMockPhotoFiles(): Promise<File[]> {
    const files: File[] = [];
    const names = ['alice', 'bob', 'charlie'];

    for (let i = 0; i < names.length; i++) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      
      canvas.width = 512;
      canvas.height = 512;

      // 使用不同的肤色
      const skinColors = ['#fdbcb4', '#f1c27d', '#e0ac69'];
      ctx.fillStyle = skinColors[i % skinColors.length];
      ctx.fillRect(0, 0, 512, 512);

      // 绘制面部特征（稍有变化）
      this.drawFacialFeatures(ctx, i);

      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      files.push(new File([blob], `${names[i]}-face.png`, { type: 'image/png' }));
    }

    return files;
  }

  /**
   * 绘制面部特征
   */
  private drawFacialFeatures(ctx: CanvasRenderingContext2D, variation: number = 0): void {
    const centerX = 256;
    const centerY = 256;

    // 眼睛
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(centerX - 60 + variation * 5, centerY - 40, 8, 0, Math.PI * 2);
    ctx.arc(centerX + 60 - variation * 5, centerY - 40, 8, 0, Math.PI * 2);
    ctx.fill();

    // 鼻子
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - 10);
    ctx.lineTo(centerX - 5 + variation, centerY + 10);
    ctx.lineTo(centerX + 5 - variation, centerY + 10);
    ctx.stroke();

    // 嘴巴
    ctx.beginPath();
    ctx.arc(centerX, centerY + 40, 20 + variation * 2, 0, Math.PI);
    ctx.stroke();
  }

  /**
   * 显示数字人信息
   */
  private async displayDigitalHumanInfo(digitalHuman: any): Promise<void> {
    console.log('\n📋 数字人详细信息:');
    
    const digitalHumanComponent = digitalHuman.getComponent('DigitalHumanComponent');
    const modelComponent = digitalHuman.getComponent('ModelComponent');
    const transform = digitalHuman.getComponent('Transform');

    if (digitalHumanComponent) {
      console.log('  基本信息:');
      console.log(`    名称: ${digitalHumanComponent.name}`);
      console.log(`    来源: ${digitalHumanComponent.source}`);
      console.log(`    创建时间: ${digitalHumanComponent.sourceData?.generationTime || '未知'}`);
    }

    if (modelComponent && modelComponent.mesh) {
      const geometry = modelComponent.mesh.geometry;
      const material = modelComponent.mesh.material;
      
      console.log('  模型信息:');
      console.log(`    顶点数: ${geometry.attributes.position.count}`);
      console.log(`    面数: ${geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3}`);
      console.log(`    材质类型: ${material.type}`);
      
      if (material.map) {
        console.log(`    漫反射纹理: ${material.map.image.width}x${material.map.image.height}`);
      }
      if (material.normalMap) {
        console.log(`    法线贴图: 已生成`);
      }
      if (material.roughnessMap) {
        console.log(`    粗糙度贴图: 已生成`);
      }
    }

    if (transform) {
      console.log('  变换信息:');
      console.log(`    位置: (${transform.position.x}, ${transform.position.y}, ${transform.position.z})`);
      console.log(`    缩放: (${transform.scale.x}, ${transform.scale.y}, ${transform.scale.z})`);
    }
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    try {
      await this.initialize();
      
      await this.demonstratePhotoConversion();
      await this.demonstrateQualityComparison();
      await this.demonstrateBatchConversion();
      
      console.log('\n🎉 照片到3D转换示例运行完成！');
      
      // 启动引擎更新循环
      this.startUpdateLoop();
      
    } catch (error) {
      console.error('❌ 示例运行失败:', error);
    }
  }

  /**
   * 启动更新循环
   */
  private startUpdateLoop(): void {
    let lastTime = performance.now();
    
    const update = (currentTime: number) => {
      const deltaTime = (currentTime - lastTime) / 1000;
      lastTime = currentTime;
      
      // 更新引擎
      this.engine.update(deltaTime);
      
      // 继续下一帧
      requestAnimationFrame(update);
    };
    
    requestAnimationFrame(update);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.pipeline.dispose();
    this.storageService.dispose();
    this.engine.dispose();
    
    console.log('示例资源已清理');
  }
}

// 运行示例
const example = new PhotoTo3DExample();
example.run().catch(console.error);

// 导出示例类
export { PhotoTo3DExample };
