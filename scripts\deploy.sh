#!/bin/bash

# RAG数字人交互系统部署脚本
# 使用方法: ./deploy.sh [environment] [action]
# environment: dev, staging, production
# action: deploy, update, rollback, stop

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 [environment] [action]"
    log_info "environment: dev, staging, production"
    log_info "action: deploy, update, rollback, stop"
    exit 1
fi

ENVIRONMENT=$1
ACTION=$2

# 验证环境参数
case $ENVIRONMENT in
    dev|staging|production)
        log_info "部署环境: $ENVIRONMENT"
        ;;
    *)
        log_error "无效的环境参数: $ENVIRONMENT"
        exit 1
        ;;
esac

# 设置环境变量
case $ENVIRONMENT in
    dev)
        ENV_FILE=".env.development"
        COMPOSE_FILE="docker-compose.dev.yml"
        ;;
    staging)
        ENV_FILE=".env.staging"
        COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    production)
        ENV_FILE=".env.production"
        COMPOSE_FILE="docker-compose.production.yml"
        ;;
esac

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境配置文件不存在: $ENV_FILE"
        exit 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    log_success "文件检查完成"
}

# 检查Docker和Docker Compose
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
    
    log_success "Docker环境检查完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建知识库服务
    log_info "构建知识库服务镜像..."
    docker build -t rag-knowledge-service:latest ./server/knowledge-service/
    
    # 构建绑定服务
    log_info "构建绑定服务镜像..."
    docker build -t rag-binding-service:latest ./server/binding-service/
    
    # 构建RAG引擎
    log_info "构建RAG引擎镜像..."
    docker build -t rag-engine:latest ./server/rag-engine/
    
    # 构建API网关
    log_info "构建API网关镜像..."
    docker build -t rag-api-gateway:latest ./server/api-gateway/
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    # 加载环境变量
    export $(cat $ENV_FILE | grep -v '^#' | xargs)
    
    # 启动服务
    docker-compose -f $COMPOSE_FILE up -d
    
    log_success "服务部署完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待PostgreSQL就绪..."
    until docker-compose -f $COMPOSE_FILE exec postgres pg_isready -U postgres; do
        sleep 2
    done
    
    # 等待Redis
    log_info "等待Redis就绪..."
    until docker-compose -f $COMPOSE_FILE exec redis redis-cli ping; do
        sleep 2
    done
    
    # 等待MinIO
    log_info "等待MinIO就绪..."
    until curl -f http://localhost:9000/minio/health/live; do
        sleep 2
    done
    
    # 等待Chroma
    log_info "等待Chroma就绪..."
    until curl -f http://localhost:8000/api/v1/heartbeat; do
        sleep 2
    done
    
    # 等待应用服务
    log_info "等待应用服务就绪..."
    sleep 30
    
    # 检查服务健康状态
    check_health
    
    log_success "所有服务已就绪"
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    services=("knowledge-service:3000" "binding-service:3001" "rag-engine:3002" "api-gateway:3000")
    
    for service in "${services[@]}"; do
        service_name=$(echo $service | cut -d':' -f1)
        port=$(echo $service | cut -d':' -f2)
        
        if curl -f http://localhost:$port/health &> /dev/null; then
            log_success "$service_name 健康检查通过"
        else
            log_error "$service_name 健康检查失败"
            return 1
        fi
    done
}

# 更新服务
update_services() {
    log_info "更新服务..."
    
    # 重新构建镜像
    build_images
    
    # 滚动更新
    docker-compose -f $COMPOSE_FILE up -d --force-recreate
    
    # 等待服务就绪
    wait_for_services
    
    log_success "服务更新完成"
}

# 回滚服务
rollback_services() {
    log_info "回滚服务..."
    
    # 这里应该实现具体的回滚逻辑
    # 例如使用之前的镜像版本
    log_warning "回滚功能需要根据具体的版本管理策略实现"
    
    log_success "服务回滚完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    docker-compose -f $COMPOSE_FILE down
    
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    log_success "资源清理完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    log_info "服务日志:"
    docker-compose -f $COMPOSE_FILE logs --tail=20
}

# 主逻辑
main() {
    log_info "开始执行 $ACTION 操作 (环境: $ENVIRONMENT)"
    
    case $ACTION in
        deploy)
            check_files
            check_docker
            build_images
            deploy_services
            wait_for_services
            show_status
            log_success "部署完成! 访问地址: http://localhost:8080/api/docs"
            ;;
        update)
            check_files
            check_docker
            update_services
            show_status
            log_success "更新完成!"
            ;;
        rollback)
            check_files
            check_docker
            rollback_services
            show_status
            log_success "回滚完成!"
            ;;
        stop)
            stop_services
            cleanup
            log_success "停止完成!"
            ;;
        *)
            log_error "无效的操作: $ACTION"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主逻辑
main
