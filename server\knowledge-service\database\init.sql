-- 创建数据库
CREATE DATABASE IF NOT EXISTS digital_human_rag;

-- 使用数据库
\c digital_human_rag;

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 数字人表
CREATE TABLE IF NOT EXISTS digital_humans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    model_config JSON<PERSON>,
    voice_config JSON<PERSON>,
    animation_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    status VARCHAR(50) DEFAULT 'active'
);

-- 知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    language VARCHAR(10) DEFAULT 'zh-CN',
    vector_index_name VARCHAR(255),
    document_count INTEGER DEFAULT 0,
    total_chunks INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    status VARCHAR(50) DEFAULT 'active'
);

-- 知识库文档表
CREATE TABLE IF NOT EXISTS knowledge_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(100),
    content_hash VARCHAR(64),
    metadata JSONB,
    chunk_count INTEGER DEFAULT 0,
    processing_status VARCHAR(50) DEFAULT 'pending',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    uploaded_by UUID
);

-- 数字人知识库绑定表
CREATE TABLE IF NOT EXISTS digital_human_knowledge_bindings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    digital_human_id UUID REFERENCES digital_humans(id) ON DELETE CASCADE,
    knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    binding_type VARCHAR(50) DEFAULT 'primary',
    priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    binding_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    UNIQUE(digital_human_id, knowledge_base_id)
);

-- 文档块表
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID REFERENCES knowledge_documents(id) ON DELETE CASCADE,
    chunk_index INTEGER,
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    start_offset INTEGER,
    end_offset INTEGER,
    metadata JSONB,
    vector_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_knowledge_bindings_digital_human ON digital_human_knowledge_bindings(digital_human_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_bindings_knowledge_base ON digital_human_knowledge_bindings(knowledge_base_id);
CREATE INDEX IF NOT EXISTS idx_documents_knowledge_base ON knowledge_documents(knowledge_base_id);
CREATE INDEX IF NOT EXISTS idx_chunks_document ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON knowledge_documents(processing_status);
CREATE INDEX IF NOT EXISTS idx_knowledge_bases_category ON knowledge_bases(category);
CREATE INDEX IF NOT EXISTS idx_knowledge_bases_language ON knowledge_bases(language);

-- 创建专用用户和权限
CREATE USER IF NOT EXISTS knowledge_service WITH PASSWORD 'secure_password';
CREATE USER IF NOT EXISTS binding_service WITH PASSWORD 'secure_password';
CREATE USER IF NOT EXISTS readonly_user WITH PASSWORD 'secure_password';

-- 授予最小权限
GRANT SELECT, INSERT, UPDATE, DELETE ON knowledge_bases TO knowledge_service;
GRANT SELECT, INSERT, UPDATE, DELETE ON knowledge_documents TO knowledge_service;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_chunks TO knowledge_service;

GRANT SELECT, INSERT, UPDATE, DELETE ON digital_human_knowledge_bindings TO binding_service;
GRANT SELECT ON knowledge_bases TO binding_service;
GRANT SELECT ON digital_humans TO binding_service;

GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 启用行级安全
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_documents ENABLE ROW LEVEL SECURITY;

-- 创建函数获取当前用户ID（示例）
CREATE OR REPLACE FUNCTION current_user_id() RETURNS UUID AS $$
BEGIN
    -- 这里应该从JWT token或session中获取用户ID
    -- 暂时返回一个示例UUID
    RETURN '00000000-0000-0000-0000-000000000000'::UUID;
END;
$$ LANGUAGE plpgsql;

-- 创建安全策略
CREATE POLICY IF NOT EXISTS knowledge_base_access ON knowledge_bases
  FOR ALL TO knowledge_service
  USING (created_by = current_user_id() OR created_by IS NULL);

CREATE POLICY IF NOT EXISTS document_access ON knowledge_documents
  FOR ALL TO knowledge_service
  USING (knowledge_base_id IN (
    SELECT id FROM knowledge_bases WHERE created_by = current_user_id() OR created_by IS NULL
  ));
