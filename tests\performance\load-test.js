import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// 自定义指标
export let errorRate = new Rate('errors');

// 测试配置
export let options = {
  stages: [
    { duration: '2m', target: 10 },   // 预热阶段
    { duration: '5m', target: 50 },   // 负载增加
    { duration: '10m', target: 100 }, // 稳定负载
    { duration: '5m', target: 200 },  // 峰值负载
    { duration: '5m', target: 0 },    // 负载下降
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95%的请求响应时间小于2秒
    http_req_failed: ['rate<0.05'],    // 错误率小于5%
    errors: ['rate<0.1'],              // 自定义错误率小于10%
  },
};

// 测试数据
const BASE_URL = 'http://localhost:8080';
const AUTH_TOKEN = 'your_test_token'; // 需要替换为实际的测试token

const testQuestions = [
  '什么是人工智能？',
  '机器学习的主要算法有哪些？',
  '深度学习和传统机器学习的区别是什么？',
  '自然语言处理的应用场景有哪些？',
  '计算机视觉技术的发展趋势如何？',
  '强化学习的基本原理是什么？',
  '神经网络的结构是怎样的？',
  '大数据和人工智能的关系是什么？',
  '云计算在AI中的作用是什么？',
  '边缘计算对AI发展的影响如何？'
];

const digitalHumanIds = [
  'test-digital-human-1',
  'test-digital-human-2',
  'test-digital-human-3'
];

export default function() {
  // 随机选择测试数据
  const question = testQuestions[Math.floor(Math.random() * testQuestions.length)];
  const digitalHumanId = digitalHumanIds[Math.floor(Math.random() * digitalHumanIds.length)];

  // 测试场景1: RAG查询
  testRAGQuery(question, digitalHumanId);
  
  sleep(1);
  
  // 测试场景2: 向量搜索
  testVectorSearch(question, digitalHumanId);
  
  sleep(1);
  
  // 测试场景3: 知识库列表查询
  testKnowledgeBaseList();
  
  sleep(Math.random() * 2 + 1); // 随机等待1-3秒
}

function testRAGQuery(question, digitalHumanId) {
  const payload = JSON.stringify({
    question: question,
    digitalHumanId: digitalHumanId,
    maxResults: 5,
    temperature: 0.7,
    language: 'zh-CN'
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
    timeout: '30s',
  };

  const response = http.post(`${BASE_URL}/api/rag/query`, payload, params);
  
  const success = check(response, {
    'RAG查询状态码为200': (r) => r.status === 200,
    'RAG查询响应时间<5s': (r) => r.timings.duration < 5000,
    'RAG查询返回答案': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.answer && body.answer.length > 0;
      } catch (e) {
        return false;
      }
    },
    'RAG查询返回来源': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.sources && Array.isArray(body.sources);
      } catch (e) {
        return false;
      }
    },
  });

  if (!success) {
    errorRate.add(1);
    console.log(`RAG查询失败: ${response.status} - ${response.body}`);
  } else {
    errorRate.add(0);
  }
}

function testVectorSearch(question, digitalHumanId) {
  const payload = JSON.stringify({
    query: question,
    digitalHumanId: digitalHumanId,
    maxResults: 10,
    threshold: 0.7
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
    timeout: '10s',
  };

  const response = http.post(`${BASE_URL}/api/rag/search`, payload, params);
  
  const success = check(response, {
    '向量搜索状态码为200': (r) => r.status === 200,
    '向量搜索响应时间<3s': (r) => r.timings.duration < 3000,
    '向量搜索返回结果': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.results && Array.isArray(body.results);
      } catch (e) {
        return false;
      }
    },
  });

  if (!success) {
    errorRate.add(1);
  } else {
    errorRate.add(0);
  }
}

function testKnowledgeBaseList() {
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
    timeout: '5s',
  };

  const response = http.get(`${BASE_URL}/api/knowledge-bases?page=1&limit=10`, params);
  
  const success = check(response, {
    '知识库列表状态码为200': (r) => r.status === 200,
    '知识库列表响应时间<2s': (r) => r.timings.duration < 2000,
    '知识库列表返回数据': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.data && Array.isArray(body.data);
      } catch (e) {
        return false;
      }
    },
  });

  if (!success) {
    errorRate.add(1);
  } else {
    errorRate.add(0);
  }
}

// 测试结束时的清理函数
export function teardown(data) {
  console.log('性能测试完成');
  console.log(`总请求数: ${data.http_reqs}`);
  console.log(`平均响应时间: ${data.http_req_duration.avg}ms`);
  console.log(`95%响应时间: ${data.http_req_duration['p(95)']}ms`);
  console.log(`错误率: ${data.http_req_failed.rate * 100}%`);
}
