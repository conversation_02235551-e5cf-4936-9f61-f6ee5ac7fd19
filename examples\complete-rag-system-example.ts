/**
 * 完整的RAG数字人交互系统示例
 * 集成知识库管理、RAG检索引擎、对话管理系统
 */

import * as THREE from 'three';
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { Scene } from '../engine/src/core/Scene';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/components/Transform';

// RAG系统组件
import { KnowledgeSceneEditor } from '../engine/src/rag/scene/KnowledgeSceneEditor';
import { DigitalHumanPathEditor } from '../engine/src/rag/navigation/DigitalHumanPathEditor';
import { DigitalHumanNavigationComponent } from '../engine/src/rag/navigation/DigitalHumanNavigationComponent';
import { 
  KnowledgeBaseService, 
  KnowledgeBaseConfig,
  DocumentMetadata 
} from '../engine/src/rag/knowledge/KnowledgeBaseService';
import { 
  RAGRetrievalEngine, 
  RetrievalConfig, 
  RetrievalStrategy 
} from '../engine/src/rag/retrieval/RAGRetrievalEngine';
import { 
  DialogueManager,
  IntentType,
  EmotionType 
} from '../engine/src/rag/dialogue/DialogueManager';

/**
 * 完整RAG系统示例类
 */
export class CompleteRAGSystemExample {
  private engine: Engine;
  private world: World;
  private scene: Scene;
  private camera: THREE.Camera;
  
  // RAG系统核心组件
  private knowledgeBase: KnowledgeBaseService;
  private ragEngine: RAGRetrievalEngine;
  private dialogueManager: DialogueManager;
  
  // 场景组件
  private knowledgeSceneEditor: KnowledgeSceneEditor;
  private pathEditor: DigitalHumanPathEditor;
  private digitalHuman: Entity;
  private navigationComponent: DigitalHumanNavigationComponent;
  
  // 系统状态
  private currentSessionId: string = 'demo-session';
  private isSystemReady: boolean = false;

  constructor(canvas: HTMLCanvasElement) {
    this.engine = new Engine({
      canvas,
      autoStart: true,
      debug: true
    });

    this.world = this.engine.getWorld();
    
    this.initializeRAGSystem();
    this.initializeScene();
    this.setupDemoData();
    this.setupUI();
  }

  /**
   * 初始化RAG系统
   */
  private async initializeRAGSystem(): Promise<void> {
    console.log('初始化RAG系统...');

    // 配置知识库
    const knowledgeBaseConfig: KnowledgeBaseConfig = {
      chunkSize: 500,
      chunkOverlap: 50,
      embeddingModel: 'simple-embedding',
      vectorStoreConfig: {},
      embeddingConfig: {},
      maxDocumentSize: 10 * 1024 * 1024, // 10MB
      supportedFileTypes: ['text/plain', 'application/pdf', 'text/html']
    };

    // 初始化知识库服务
    this.knowledgeBase = new KnowledgeBaseService(knowledgeBaseConfig);

    // 配置RAG检索引擎
    const retrievalConfig: RetrievalConfig = {
      strategy: RetrievalStrategy.HYBRID,
      maxResults: 5,
      semanticWeight: 0.7,
      keywordWeight: 0.3,
      contextWeight: 0.2,
      diversityThreshold: 0.8,
      relevanceThreshold: 0.6,
      useReranking: true,
      enableContextualFiltering: true
    };

    // 初始化RAG检索引擎
    this.ragEngine = new RAGRetrievalEngine(this.knowledgeBase, retrievalConfig);

    // 初始化对话管理器
    this.dialogueManager = new DialogueManager(this.ragEngine);

    console.log('RAG系统初始化完成');
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    // 创建场景
    this.scene = new Scene(this.world, {
      name: '完整RAG系统演示场景'
    });
    
    this.world.addScene(this.scene);
    this.world.setActiveScene(this.scene);

    // 创建相机
    const cameraEntity = this.world.createEntity('主相机');
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);
    camera.lookAt(0, 0, 0);
    this.camera = camera;

    // 创建环境
    this.createEnvironment();
    
    // 创建数字人
    this.createDigitalHuman();
    
    // 初始化场景编辑器
    this.knowledgeSceneEditor = new KnowledgeSceneEditor(
      this.world,
      this.scene.getThreeScene(),
      this.camera
    );
    
    // 初始化路径编辑器
    this.pathEditor = new DigitalHumanPathEditor(
      this.scene.getThreeScene(),
      {
        pathType: 'digital_human_navigation',
        allowCurves: true,
        showDirection: true,
        enableStops: true,
        segments: 50,
        debug: true
      }
    );
    
    // 设置事件处理
    this.setupSceneEventHandlers();
  }

  /**
   * 创建环境
   */
  private createEnvironment(): void {
    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    this.scene.getThreeScene().add(ground);

    // 创建展品
    const exhibits = [
      { name: '古代文物', position: new THREE.Vector3(-5, 1, -3), color: 0xff6b6b },
      { name: '现代艺术', position: new THREE.Vector3(0, 1, -5), color: 0x4ecdc4 },
      { name: '科技展品', position: new THREE.Vector3(5, 1, -3), color: 0x45b7d1 },
      { name: '历史文献', position: new THREE.Vector3(-3, 1, 2), color: 0xf9ca24 },
      { name: '互动体验', position: new THREE.Vector3(3, 1, 2), color: 0x6c5ce7 }
    ];

    exhibits.forEach(exhibit => {
      const geometry = new THREE.BoxGeometry(1, 2, 1);
      const material = new THREE.MeshLambertMaterial({ color: exhibit.color });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(exhibit.position);
      mesh.userData = { name: exhibit.name };
      this.scene.getThreeScene().add(mesh);
    });

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.getThreeScene().add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    this.scene.getThreeScene().add(directionalLight);
  }

  /**
   * 创建数字人
   */
  private createDigitalHuman(): void {
    this.digitalHuman = this.world.createEntity('数字人');
    
    // 创建数字人模型
    const geometry = new THREE.CapsuleGeometry(0.3, 1.5, 4, 8);
    const material = new THREE.MeshLambertMaterial({ color: 0x8e44ad });
    const mesh = new THREE.Mesh(geometry, material);
    
    this.digitalHuman.addComponent(new Transform(this.digitalHuman, {
      position: new THREE.Vector3(0, 1, 0)
    }));
    
    // 添加导航组件
    this.navigationComponent = new DigitalHumanNavigationComponent(
      this.digitalHuman,
      {
        moveSpeed: 2.0,
        rotationSpeed: 5.0,
        stopThreshold: 0.5,
        smoothRotation: true,
        autoStart: false,
        debug: true
      }
    );
    
    this.digitalHuman.addComponent(this.navigationComponent);
    
    // 将mesh添加到场景
    this.scene.getThreeScene().add(mesh);
  }

  /**
   * 设置演示数据
   */
  private async setupDemoData(): Promise<void> {
    console.log('设置演示数据...');

    // 创建示例文档
    const documents = [
      {
        content: '古代文物展区展示了从新石器时代到明清时期的珍贵文物，包括青铜器、陶瓷、玉器等。这些文物反映了中华文明的悠久历史和灿烂文化。',
        metadata: {
          title: '古代文物展区介绍',
          category: 'cultural',
          tags: ['古代', '文物', '历史', '文化'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '古代文物展区的详细介绍'
        }
      },
      {
        content: '现代艺术展区汇集了20世纪以来的优秀艺术作品，包括绘画、雕塑、装置艺术等多种形式。这些作品体现了现代艺术的创新精神和多元化特征。',
        metadata: {
          title: '现代艺术展区介绍',
          category: 'art',
          tags: ['现代', '艺术', '绘画', '雕塑'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '现代艺术展区的详细介绍'
        }
      },
      {
        content: '科技展品区展示了人类科技发展的重要成果，从古代的指南针、火药到现代的计算机、人工智能。参观者可以通过互动体验了解科技的发展历程。',
        metadata: {
          title: '科技展品区介绍',
          category: 'technology',
          tags: ['科技', '发明', '创新', '互动'],
          author: '博物馆',
          createdAt: new Date(),
          language: 'zh',
          description: '科技展品区的详细介绍'
        }
      }
    ];

    // 上传文档到知识库
    for (const doc of documents) {
      const file = new File([doc.content], `${doc.metadata.title}.txt`, { type: 'text/plain' });
      await this.knowledgeBase.uploadDocument(file, doc.metadata);
    }

    // 添加知识点到场景
    const knowledgePoints = [
      {
        position: new THREE.Vector3(-5, 2.5, -3),
        knowledge: {
          id: 'kp1',
          title: '古代文物',
          content: '这里展示的是珍贵的古代文物，包括青铜器和陶瓷制品。',
          type: 'text' as const,
          tags: ['古代', '文物'],
          category: 'cultural',
          priority: 2,
          relatedTopics: ['历史', '文化']
        }
      },
      {
        position: new THREE.Vector3(0, 2.5, -5),
        knowledge: {
          id: 'kp2',
          title: '现代艺术',
          content: '这个区域展示了现代艺术的精品，体现了艺术的创新与发展。',
          type: 'text' as const,
          tags: ['现代', '艺术'],
          category: 'art',
          priority: 2,
          relatedTopics: ['艺术', '创新']
        }
      },
      {
        position: new THREE.Vector3(5, 2.5, -3),
        knowledge: {
          id: 'kp3',
          title: '科技展品',
          content: '科技展品展示了人类智慧的结晶，从古代发明到现代科技。',
          type: 'text' as const,
          tags: ['科技', '发明'],
          category: 'technology',
          priority: 2,
          relatedTopics: ['科技', '创新']
        }
      }
    ];

    // 添加知识点
    knowledgePoints.forEach(kp => {
      this.knowledgeSceneEditor.addKnowledgePoint(kp.position, kp.knowledge);
    });

    // 创建导览路径
    const pathPoints = [
      { position: new THREE.Vector3(0, 0.1, 5), type: 'normal' as const },
      { position: new THREE.Vector3(-5, 0.1, -1), type: 'stop' as const },
      { position: new THREE.Vector3(-2, 0.1, -5), type: 'waypoint' as const },
      { position: new THREE.Vector3(2, 0.1, -5), type: 'stop' as const },
      { position: new THREE.Vector3(5, 0.1, -1), type: 'stop' as const },
      { position: new THREE.Vector3(0, 0.1, 3), type: 'normal' as const }
    ];

    pathPoints.forEach(point => {
      this.pathEditor.addPathPoint(point.position, point.type, {
        waitTime: point.type === 'stop' ? 3000 : 0,
        actions: point.type === 'stop' ? ['介绍展品', '播放动画'] : []
      });
    });

    // 设置路径到导航组件
    this.navigationComponent.setPath(this.pathEditor);

    this.isSystemReady = true;
    console.log('演示数据设置完成');
  }

  /**
   * 设置场景事件处理器
   */
  private setupSceneEventHandlers(): void {
    // 知识点选择事件
    this.knowledgeSceneEditor.onKnowledgePointSelected = (knowledgePoint) => {
      if (knowledgePoint) {
        this.handleKnowledgePointInteraction(knowledgePoint.knowledge.title);
      }
    };

    // 导航事件
    this.navigationComponent.onReachedStopPoint = (stopPoint) => {
      this.handleStopPointReached(stopPoint);
    };
  }

  /**
   * 处理知识点交互
   */
  private async handleKnowledgePointInteraction(title: string): Promise<void> {
    const query = `介绍一下${title}`;
    await this.processUserQuery(query);
  }

  /**
   * 处理停留点到达
   */
  private async handleStopPointReached(stopPoint: any): Promise<void> {
    const query = '介绍一下这里的展品';
    await this.processUserQuery(query);
  }

  /**
   * 处理用户查询
   */
  public async processUserQuery(query: string): Promise<void> {
    if (!this.isSystemReady) {
      console.log('系统尚未准备就绪');
      return;
    }

    console.log(`用户查询: ${query}`);

    try {
      // 使用对话管理器处理查询
      const response = await this.dialogueManager.processUserInput(
        this.currentSessionId,
        query,
        {
          currentLocation: '展厅中央',
          userProfile: {
            id: 'demo-user',
            preferences: ['历史', '艺术'],
            interests: ['文化', '科技'],
            language: 'zh',
            expertiseLevel: 'intermediate',
            visitHistory: []
          }
        }
      );

      // 显示响应
      this.displayResponse(response);

      // 执行数字人动作
      this.executeDigitalHumanActions(response.actions);

    } catch (error) {
      console.error('处理查询时出错:', error);
    }
  }

  /**
   * 显示响应
   */
  private displayResponse(response: any): void {
    const responseDiv = document.getElementById('response-display');
    if (responseDiv) {
      responseDiv.innerHTML = `
        <div class="response-item">
          <h4>数字人回复:</h4>
          <p>${response.text}</p>

          <div class="response-metadata">
            <p><strong>意图:</strong> ${response.intent.intent} (置信度: ${(response.intent.confidence * 100).toFixed(1)}%)</p>
            <p><strong>情感:</strong> ${response.emotion.emotion} (强度: ${(response.emotion.intensity * 100).toFixed(1)}%)</p>
            <p><strong>处理时间:</strong> ${response.metadata.processingTime}ms</p>
            <p><strong>知识来源:</strong> ${response.sources.length} 个相关文档</p>
          </div>

          ${response.followUpQuestions.length > 0 ? `
            <div class="follow-up-questions">
              <h5>您可能还想了解:</h5>
              <ul>
                ${response.followUpQuestions.map(q => `<li>${q}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `;
    }

    // 更新对话历史
    this.updateConversationHistory(response);
  }

  /**
   * 执行数字人动作
   */
  private executeDigitalHumanActions(actionMapping: any): void {
    console.log('执行数字人动作:', actionMapping);

    // 这里可以集成实际的动画系统
    actionMapping.actions.forEach((action: any, index: number) => {
      setTimeout(() => {
        console.log(`执行动作: ${action.type}`, action.parameters);

        // 模拟动作执行
        this.simulateAction(action);
      }, action.delay);
    });
  }

  /**
   * 模拟动作执行
   */
  private simulateAction(action: any): void {
    const statusDiv = document.getElementById('action-status');
    if (statusDiv) {
      statusDiv.innerHTML = `
        <p><strong>当前动作:</strong> ${action.type}</p>
        <p><strong>参数:</strong> ${JSON.stringify(action.parameters)}</p>
        <p><strong>持续时间:</strong> ${action.duration}ms</p>
      `;

      // 清除状态显示
      setTimeout(() => {
        statusDiv.innerHTML = '<p>待机中...</p>';
      }, action.duration);
    }
  }

  /**
   * 更新对话历史
   */
  private updateConversationHistory(response: any): void {
    const historyDiv = document.getElementById('conversation-history');
    if (historyDiv) {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-item';
      historyItem.innerHTML = `
        <div class="user-message">
          <strong>用户:</strong> ${response.metadata.retrievalResults.queryAnalysis.keywords.join(' ')}
        </div>
        <div class="system-message">
          <strong>系统:</strong> ${response.text.substring(0, 100)}${response.text.length > 100 ? '...' : ''}
        </div>
        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
      `;

      historyDiv.appendChild(historyItem);
      historyDiv.scrollTop = historyDiv.scrollHeight;
    }
  }

  /**
   * 设置UI界面
   */
  private setupUI(): void {
    // 创建主控制面板
    const controlPanel = document.createElement('div');
    controlPanel.id = 'rag-control-panel';
    controlPanel.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      width: 350px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 20px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-height: 80vh;
      overflow-y: auto;
    `;

    controlPanel.innerHTML = `
      <h3 style="margin: 0 0 15px 0; color: #4CAF50;">RAG数字人交互系统</h3>

      <div class="system-status">
        <h4>系统状态</h4>
        <div id="system-status">初始化中...</div>
      </div>

      <div class="query-input" style="margin: 15px 0;">
        <h4>用户查询</h4>
        <input type="text" id="user-query" placeholder="请输入您的问题..."
               style="width: 100%; padding: 8px; border: none; border-radius: 4px; margin-bottom: 10px;">
        <button id="submit-query" style="width: 100%; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
          提交查询
        </button>
      </div>

      <div class="quick-queries" style="margin: 15px 0;">
        <h4>快速查询</h4>
        <button class="quick-btn" data-query="你好">问候</button>
        <button class="quick-btn" data-query="介绍一下古代文物">古代文物</button>
        <button class="quick-btn" data-query="现代艺术有什么特点">现代艺术</button>
        <button class="quick-btn" data-query="推荐参观路线">推荐路线</button>
      </div>

      <div class="navigation-control" style="margin: 15px 0;">
        <h4>导航控制</h4>
        <button id="start-tour" style="margin: 5px; padding: 5px 10px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
          开始导览
        </button>
        <button id="pause-tour" style="margin: 5px; padding: 5px 10px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
          暂停导览
        </button>
        <button id="reset-tour" style="margin: 5px; padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
          重置导览
        </button>
      </div>
    `;

    document.body.appendChild(controlPanel);

    // 创建响应显示面板
    const responsePanel = document.createElement('div');
    responsePanel.id = 'response-panel';
    responsePanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      background: rgba(255, 255, 255, 0.95);
      color: black;
      padding: 20px;
      border-radius: 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000;
      max-height: 80vh;
      overflow-y: auto;
    `;

    responsePanel.innerHTML = `
      <h3 style="margin: 0 0 15px 0; color: #333;">系统响应</h3>
      <div id="response-display">等待用户查询...</div>

      <h4 style="margin: 20px 0 10px 0;">动作状态</h4>
      <div id="action-status">待机中...</div>

      <h4 style="margin: 20px 0 10px 0;">对话历史</h4>
      <div id="conversation-history" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
        <p>对话历史将显示在这里...</p>
      </div>
    `;

    document.body.appendChild(responsePanel);

    // 设置事件监听器
    this.setupUIEventListeners();

    // 添加样式
    this.addUIStyles();
  }

  /**
   * 设置UI事件监听器
   */
  private setupUIEventListeners(): void {
    // 查询提交
    const submitBtn = document.getElementById('submit-query');
    const queryInput = document.getElementById('user-query') as HTMLInputElement;

    if (submitBtn && queryInput) {
      submitBtn.addEventListener('click', () => {
        const query = queryInput.value.trim();
        if (query) {
          this.processUserQuery(query);
          queryInput.value = '';
        }
      });

      queryInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          submitBtn.click();
        }
      });
    }

    // 快速查询按钮
    document.querySelectorAll('.quick-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const query = (e.target as HTMLElement).getAttribute('data-query');
        if (query) {
          this.processUserQuery(query);
        }
      });
    });

    // 导航控制
    const startTourBtn = document.getElementById('start-tour');
    const pauseTourBtn = document.getElementById('pause-tour');
    const resetTourBtn = document.getElementById('reset-tour');

    if (startTourBtn) {
      startTourBtn.addEventListener('click', () => {
        this.navigationComponent.startMoving();
      });
    }

    if (pauseTourBtn) {
      pauseTourBtn.addEventListener('click', () => {
        this.navigationComponent.pauseMoving();
      });
    }

    if (resetTourBtn) {
      resetTourBtn.addEventListener('click', () => {
        this.navigationComponent.reset();
      });
    }
  }

  /**
   * 添加UI样式
   */
  private addUIStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .quick-btn {
        margin: 3px;
        padding: 5px 10px;
        background: #607D8B;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }

      .quick-btn:hover {
        background: #455A64;
      }

      .response-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #f9f9f9;
      }

      .response-metadata {
        font-size: 12px;
        color: #666;
        margin-top: 10px;
      }

      .follow-up-questions {
        margin-top: 10px;
        padding: 10px;
        background: #e3f2fd;
        border-radius: 4px;
      }

      .follow-up-questions ul {
        margin: 5px 0;
        padding-left: 20px;
      }

      .history-item {
        margin-bottom: 10px;
        padding: 8px;
        border-left: 3px solid #4CAF50;
        background: #f5f5f5;
      }

      .user-message {
        color: #2196F3;
        margin-bottom: 5px;
      }

      .system-message {
        color: #4CAF50;
        margin-bottom: 5px;
      }

      .timestamp {
        font-size: 11px;
        color: #999;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 更新系统状态显示
   */
  private updateSystemStatus(): void {
    const statusDiv = document.getElementById('system-status');
    if (statusDiv) {
      const stats = {
        ready: this.isSystemReady,
        documents: this.knowledgeBase.getAllDocuments().length,
        knowledgePoints: this.knowledgeSceneEditor.getAllKnowledgePoints().length,
        pathPoints: this.pathEditor.getPathPoints().length,
        navigationState: this.navigationComponent.getState()
      };

      statusDiv.innerHTML = `
        <p><strong>状态:</strong> ${stats.ready ? '就绪' : '初始化中'}</p>
        <p><strong>知识文档:</strong> ${stats.documents} 个</p>
        <p><strong>知识点:</strong> ${stats.knowledgePoints} 个</p>
        <p><strong>路径点:</strong> ${stats.pathPoints} 个</p>
        <p><strong>导航状态:</strong> ${stats.navigationState}</p>
      `;
    }
  }

  /**
   * 启动系统
   */
  public async start(): Promise<void> {
    console.log('启动完整RAG系统...');

    // 等待系统初始化完成
    while (!this.isSystemReady) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 更新状态显示
    setInterval(() => {
      this.updateSystemStatus();
    }, 1000);

    console.log('RAG系统启动完成！');
  }

  /**
   * 获取系统统计信息
   */
  public async getSystemStats(): Promise<any> {
    const knowledgeStats = await this.knowledgeBase.getStats();
    const dialogueState = this.dialogueManager.getDialogueState(this.currentSessionId);

    return {
      knowledgeBase: knowledgeStats,
      dialogue: {
        sessionId: this.currentSessionId,
        conversationTurns: dialogueState?.conversationHistory.length || 0,
        lastIntent: dialogueState?.lastIntent,
        lastEmotion: dialogueState?.lastEmotion
      },
      navigation: this.navigationComponent.getNavigationInfo(),
      scene: {
        knowledgePoints: this.knowledgeSceneEditor.getAllKnowledgePoints().length,
        pathPoints: this.pathEditor.getPathPoints().length
      }
    };
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理RAG系统
    this.dialogueManager.clearSession(this.currentSessionId);
    this.knowledgeBase.clearAll();

    // 清理场景组件
    this.knowledgeSceneEditor.dispose();
    this.pathEditor.dispose();
    this.navigationComponent.dispose();

    // 清理引擎
    this.engine.dispose();

    // 清理UI
    const controlPanel = document.getElementById('rag-control-panel');
    const responsePanel = document.getElementById('response-panel');

    if (controlPanel) controlPanel.remove();
    if (responsePanel) responsePanel.remove();
  }
}

// 导出示例类
export default CompleteRAGSystemExample;
