/**
 * 场景生成系统测试套件
 * 全面测试文本语音场景生成系统的各个组件
 */
import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import {
  SceneGenerationAIManager,
  SceneUnderstandingModel,
  LayoutGenerationModel,
  AssetMatchingModel,
  VoiceSceneGenerationController,
  ConversationalSceneOptimizer,
  ScenePerformanceOptimizer
} from '../../../engine/src/ai/scene';

/**
 * 测试数据
 */
const testSceneDescriptions = [
  '创建一个现代办公室，包含一张大会议桌，周围放置8把椅子，墙上挂着一块白板',
  '设计一个温馨的客厅，有一套灰色布艺沙发，前面放着一张木质茶几，电视墙采用深色木纹',
  '构建一个简约的卧室，有一张双人床，床头两侧各有一个床头柜和台灯',
  '创建一个开放式厨房，有一个中央岛台，周围配置现代化的厨房设备'
];

const testFeedbacks = [
  '把桌子移动到房间中央',
  '添加一盆绿植在角落',
  '把沙发换成红色的',
  '删除那个柜子',
  '整体风格改成工业风'
];

/**
 * 场景理解模型测试
 */
describe('SceneUnderstandingModel', () => {
  let model: SceneUnderstandingModel;

  beforeEach(async () => {
    model = new SceneUnderstandingModel({
      modelType: 'transformer',
      language: 'zh-CN'
    }, { debug: true });
    await model.initialize();
  });

  afterEach(async () => {
    await model.destroy();
  });

  test('应该正确理解简单场景描述', async () => {
    const description = '创建一个办公室，有一张桌子和两把椅子';
    const understanding = await model.understand(description);

    expect(understanding).toBeDefined();
    expect(understanding.elements.length).toBeGreaterThan(0);
    expect(understanding.intent.sceneType).toBe('office');
    expect(understanding.confidence).toBeGreaterThan(0.5);
  });

  test('应该提取场景元素', async () => {
    const description = '客厅里有一张红色沙发和一个木质茶几';
    const understanding = await model.understand(description);

    const sofaElement = understanding.elements.find(e => e.name.includes('沙发'));
    const tableElement = understanding.elements.find(e => e.name.includes('茶几'));

    expect(sofaElement).toBeDefined();
    expect(tableElement).toBeDefined();
    expect(sofaElement?.attributes.color).toBe('红色');
    expect(tableElement?.attributes.material).toBe('木质');
  });

  test('应该识别空间关系', async () => {
    const description = '桌子在房间中央，椅子在桌子周围';
    const understanding = await model.understand(description);

    expect(understanding.spatialRelations.length).toBeGreaterThan(0);
  });

  test('应该处理复杂场景描述', async () => {
    for (const description of testSceneDescriptions) {
      const understanding = await model.understand(description);
      
      expect(understanding).toBeDefined();
      expect(understanding.elements.length).toBeGreaterThan(0);
      expect(understanding.confidence).toBeGreaterThan(0.3);
    }
  });
});

/**
 * 布局生成模型测试
 */
describe('LayoutGenerationModel', () => {
  let model: LayoutGenerationModel;
  let understandingModel: SceneUnderstandingModel;

  beforeEach(async () => {
    model = new LayoutGenerationModel({
      algorithm: 'constraint_satisfaction'
    }, { debug: true });
    
    understandingModel = new SceneUnderstandingModel({}, { debug: true });
    
    await Promise.all([
      model.initialize(),
      understandingModel.initialize()
    ]);
  });

  afterEach(async () => {
    await Promise.all([
      model.destroy(),
      understandingModel.destroy()
    ]);
  });

  test('应该生成合理的场景布局', async () => {
    const description = '办公室有一张桌子和四把椅子';
    const understanding = await understandingModel.understand(description);
    const layout = await model.generateLayout(understanding);

    expect(layout).toBeDefined();
    expect(layout.elements.length).toBe(understanding.elements.length);
    expect(layout.bounds).toBeDefined();
    expect(layout.environment).toBeDefined();
  });

  test('应该避免对象重叠', async () => {
    const description = '房间里有很多家具';
    const understanding = await understandingModel.understand(description);
    const layout = await model.generateLayout(understanding);

    // 检查是否有重叠
    for (let i = 0; i < layout.elements.length; i++) {
      for (let j = i + 1; j < layout.elements.length; j++) {
        const elementA = layout.elements[i];
        const elementB = layout.elements[j];
        const overlap = elementA.boundingBox.intersectsBox(elementB.boundingBox);
        expect(overlap).toBe(false);
      }
    }
  });

  test('应该生成不同算法的布局', async () => {
    const algorithms = ['constraint_satisfaction', 'genetic_algorithm', 'grid_based'];
    const description = '简单的办公室';
    const understanding = await understandingModel.understand(description);

    for (const algorithm of algorithms) {
      const testModel = new LayoutGenerationModel({ algorithm: algorithm as any }, { debug: true });
      await testModel.initialize();
      
      const layout = await testModel.generateLayout(understanding);
      expect(layout).toBeDefined();
      expect(layout.elements.length).toBeGreaterThan(0);
      
      await testModel.destroy();
    }
  });
});

/**
 * 资产匹配模型测试
 */
describe('AssetMatchingModel', () => {
  let model: AssetMatchingModel;
  let understandingModel: SceneUnderstandingModel;
  let layoutModel: LayoutGenerationModel;

  beforeEach(async () => {
    model = new AssetMatchingModel({
      similarityThreshold: 0.7
    }, { debug: true });
    
    understandingModel = new SceneUnderstandingModel({}, { debug: true });
    layoutModel = new LayoutGenerationModel({}, { debug: true });
    
    await Promise.all([
      model.initialize(),
      understandingModel.initialize(),
      layoutModel.initialize()
    ]);
  });

  afterEach(async () => {
    await Promise.all([
      model.destroy(),
      understandingModel.destroy(),
      layoutModel.destroy()
    ]);
  });

  test('应该匹配合适的资产', async () => {
    const description = '办公室有一张桌子';
    const understanding = await understandingModel.understand(description);
    const layout = await layoutModel.generateLayout(understanding);
    const assets = await model.matchAssets(understanding, layout);

    expect(assets.length).toBeGreaterThan(0);
    assets.forEach(asset => {
      expect(asset.confidence).toBeGreaterThan(0.3);
      expect(asset.asset).toBeDefined();
    });
  });

  test('应该为不同类别匹配不同资产', async () => {
    const description = '房间里有桌子、椅子和灯';
    const understanding = await understandingModel.understand(description);
    const layout = await layoutModel.generateLayout(understanding);
    const assets = await model.matchAssets(understanding, layout);

    const categories = new Set(assets.map(a => a.element.category));
    expect(categories.size).toBeGreaterThan(1);
  });
});

/**
 * 场景生成AI管理器测试
 */
describe('SceneGenerationAIManager', () => {
  let manager: SceneGenerationAIManager;

  beforeEach(async () => {
    manager = new SceneGenerationAIManager({
      debug: true,
      enableRealTimeGeneration: true
    });
  });

  afterEach(async () => {
    await manager.destroy();
  });

  test('应该完整生成场景', async () => {
    const description = '创建一个现代办公室';
    const result = await manager.generateScene(description);

    expect(result).toBeDefined();
    expect(result.scene).toBeDefined();
    expect(result.understanding).toBeDefined();
    expect(result.layout).toBeDefined();
    expect(result.assets).toBeDefined();
    expect(result.confidence).toBeGreaterThan(0);
  });

  test('应该支持流式生成', async () => {
    const description = '创建一个客厅';
    const generator = manager.generateSceneStream(description);
    
    let stepCount = 0;
    for await (const step of generator) {
      stepCount++;
      expect(step.progress).toBeGreaterThanOrEqual(0);
    }
    
    expect(stepCount).toBeGreaterThan(1);
  });

  test('应该验证场景描述', async () => {
    const validDescription = '创建一个包含桌子和椅子的办公室';
    const invalidDescription = '';

    const validResult = await manager.validateDescription(validDescription);
    const invalidResult = await manager.validateDescription(invalidDescription);

    expect(validResult.isValid).toBe(true);
    expect(invalidResult.isValid).toBe(false);
    expect(invalidResult.issues.length).toBeGreaterThan(0);
  });

  test('应该生成建议', async () => {
    const partialDescription = '办公室';
    const suggestions = await manager.getGenerationSuggestions(partialDescription);

    expect(suggestions.length).toBeGreaterThan(0);
    expect(suggestions.some(s => s.includes('桌子') || s.includes('椅子'))).toBe(true);
  });
});

/**
 * 语音场景生成控制器测试
 */
describe('VoiceSceneGenerationController', () => {
  let controller: VoiceSceneGenerationController;

  beforeEach(() => {
    controller = new VoiceSceneGenerationController();
  });

  afterEach(async () => {
    await controller.destroy();
  });

  test('应该启动语音会话', async () => {
    const result = await controller.startVoiceSession();
    
    expect(result.status).toBe('active');
    expect(result.sessionId).toBeDefined();
    
    await controller.stopVoiceSession(result.sessionId);
  });

  test('应该管理会话状态', async () => {
    const result = await controller.startVoiceSession();
    const sessionId = result.sessionId;
    
    const state = controller.getSessionState(sessionId);
    expect(state).toBeDefined();
    expect(state?.sessionId).toBe(sessionId);
    
    await controller.stopVoiceSession(sessionId);
  });
});

/**
 * 多轮对话优化器测试
 */
describe('ConversationalSceneOptimizer', () => {
  let optimizer: ConversationalSceneOptimizer;

  beforeEach(() => {
    optimizer = new ConversationalSceneOptimizer();
  });

  test('应该处理用户反馈', async () => {
    const mockScene = {
      id: 'test_scene',
      elements: ['桌子', '椅子'],
      confidence: 0.8
    };

    for (const feedback of testFeedbacks) {
      const result = await optimizer.processFeedback('test_session', feedback, mockScene);
      
      expect(result).toBeDefined();
      expect(result.optimizedScene).toBeDefined();
      expect(result.modifications).toBeDefined();
      expect(result.explanation).toBeDefined();
    }
  });

  test('应该维护对话历史', async () => {
    const sessionId = 'test_session';
    const mockScene = { id: 'test', elements: [] };
    
    await optimizer.processFeedback(sessionId, '添加一张桌子', mockScene);
    await optimizer.processFeedback(sessionId, '把桌子移动到中央', mockScene);
    
    const history = optimizer.getConversationHistory(sessionId);
    expect(history.length).toBeGreaterThan(0);
  });
});

/**
 * 性能优化器测试
 */
describe('ScenePerformanceOptimizer', () => {
  let optimizer: ScenePerformanceOptimizer;

  beforeEach(() => {
    optimizer = new ScenePerformanceOptimizer({
      targetFPS: 60,
      maxPolygons: 50000
    });
  });

  test('应该提供性能统计', () => {
    const stats = optimizer.getPerformanceStats();
    
    expect(stats).toBeDefined();
    expect(stats.currentFPS).toBeGreaterThanOrEqual(0);
    expect(stats.suggestions).toBeDefined();
  });

  test('应该更新配置', () => {
    optimizer.setConfig({ targetFPS: 30 });
    // 配置应该已更新，但没有直接的getter来验证
    expect(true).toBe(true);
  });
});

/**
 * 集成测试
 */
describe('场景生成系统集成测试', () => {
  let manager: SceneGenerationAIManager;
  let optimizer: ConversationalSceneOptimizer;

  beforeEach(async () => {
    manager = new SceneGenerationAIManager({ debug: true });
    optimizer = new ConversationalSceneOptimizer();
  });

  afterEach(async () => {
    await manager.destroy();
  });

  test('应该完成完整的场景生成和优化流程', async () => {
    // 1. 生成初始场景
    const description = '创建一个办公室，有桌子和椅子';
    const initialResult = await manager.generateScene(description);
    
    expect(initialResult).toBeDefined();
    expect(initialResult.confidence).toBeGreaterThan(0);

    // 2. 应用用户反馈
    const feedback = '把桌子移动到房间中央';
    const optimizedResult = await optimizer.processFeedback(
      'test_session',
      feedback,
      initialResult.scene
    );

    expect(optimizedResult).toBeDefined();
    expect(optimizedResult.modifications.length).toBeGreaterThan(0);

    // 3. 验证优化结果
    expect(optimizedResult.confidence).toBeGreaterThan(0);
    expect(optimizedResult.explanation).toBeDefined();
    expect(optimizedResult.suggestions.length).toBeGreaterThan(0);
  });

  test('应该处理多轮对话', async () => {
    const sessionId = 'integration_test';
    let currentScene = await manager.generateScene('创建一个客厅');

    const feedbacks = [
      '添加一张沙发',
      '把沙发换成红色的',
      '在沙发前面放一张茶几'
    ];

    for (const feedback of feedbacks) {
      const result = await optimizer.processFeedback(sessionId, feedback, currentScene.scene);
      currentScene.scene = result.optimizedScene;
      
      expect(result.modifications.length).toBeGreaterThan(0);
    }

    const history = optimizer.getConversationHistory(sessionId);
    expect(history.length).toBe(feedbacks.length * 2); // 用户消息 + 系统响应
  });
});

/**
 * 性能测试
 */
describe('性能测试', () => {
  test('场景生成应该在合理时间内完成', async () => {
    const manager = new SceneGenerationAIManager({ debug: false });
    
    const startTime = Date.now();
    const result = await manager.generateScene('简单的办公室');
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    expect(duration).toBeLessThan(10000); // 应该在10秒内完成
    expect(result).toBeDefined();
    
    await manager.destroy();
  });

  test('批量场景生成性能', async () => {
    const manager = new SceneGenerationAIManager({ debug: false });
    
    const descriptions = [
      '办公室',
      '客厅',
      '卧室',
      '厨房',
      '会议室'
    ];

    const startTime = Date.now();
    const promises = descriptions.map(desc => manager.generateScene(desc));
    const results = await Promise.all(promises);
    const endTime = Date.now();

    const duration = endTime - startTime;
    expect(duration).toBeLessThan(30000); // 批量生成应该在30秒内完成
    expect(results.length).toBe(descriptions.length);
    results.forEach(result => {
      expect(result).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
    });

    await manager.destroy();
  });
});

/**
 * 错误处理测试
 */
describe('错误处理测试', () => {
  test('应该处理无效输入', async () => {
    const manager = new SceneGenerationAIManager({ debug: true });
    
    // 测试空描述
    await expect(manager.generateScene('')).rejects.toThrow();
    
    // 测试无意义描述
    const result = await manager.generateScene('asdfghjkl');
    expect(result.confidence).toBeLessThan(0.5);
    
    await manager.destroy();
  });

  test('应该处理模型初始化失败', async () => {
    // 测试无效配置
    const manager = new SceneGenerationAIManager({
      sceneUnderstanding: { invalidConfig: true }
    });
    
    // 应该能够优雅地处理初始化错误
    expect(manager).toBeDefined();
  });
});
