import { System } from '../../core/System';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { Transform } from '../../core/Transform';
import { EventEmitter } from 'events';
import { DigitalHumanComponent } from '../components/DigitalHumanComponent';
import * as THREE from 'three';

/**
 * 交互类型
 */
export enum InteractionType {
  DRAG = 'drag',
  CLICK = 'click',
  HOVER = 'hover',
  GESTURE = 'gesture',
  VOICE = 'voice',
  COLLISION = 'collision',
  PROXIMITY = 'proximity'
}

/**
 * 交互状态
 */
export enum InteractionState {
  IDLE = 'idle',
  ACTIVE = 'active',
  DISABLED = 'disabled',
  PROCESSING = 'processing'
}

/**
 * 环境类型
 */
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  VIRTUAL = 'virtual',
  MIXED = 'mixed'
}

/**
 * 行为类型
 */
export enum BehaviorType {
  IDLE = 'idle',
  FOLLOW = 'follow',
  AVOID = 'avoid',
  INTERACT = 'interact',
  EXPLORE = 'explore',
  REACT = 'react'
}

/**
 * 交互区域
 */
export interface InteractionZone {
  /** 区域ID */
  id: string;
  /** 区域名称 */
  name: string;
  /** 区域类型 */
  type: 'sphere' | 'box' | 'plane' | 'custom';
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 是否启用 */
  enabled: boolean;
  /** 交互类型 */
  interactionTypes: InteractionType[];
  /** 触发条件 */
  triggerConditions: Map<string, any>;
  /** 响应行为 */
  responses: InteractionResponse[];
}

/**
 * 交互响应
 */
export interface InteractionResponse {
  /** 响应ID */
  id: string;
  /** 响应类型 */
  type: 'animation' | 'sound' | 'effect' | 'script';
  /** 响应数据 */
  data: any;
  /** 延迟时间 */
  delay: number;
  /** 持续时间 */
  duration: number;
  /** 优先级 */
  priority: number;
}

/**
 * 拖拽控制器
 */
export interface DragController {
  /** 是否启用 */
  enabled: boolean;
  /** 拖拽轴限制 */
  constraints: {
    x: boolean;
    y: boolean;
    z: boolean;
  };
  /** 拖拽范围 */
  bounds?: {
    min: THREE.Vector3;
    max: THREE.Vector3;
  };
  /** 拖拽速度 */
  speed: number;
  /** 阻尼 */
  damping: number;
  /** 吸附点 */
  snapPoints: THREE.Vector3[];
  /** 吸附距离 */
  snapDistance: number;
}

/**
 * 环境适应器
 */
export interface EnvironmentAdapter {
  /** 环境类型 */
  environmentType: EnvironmentType;
  /** 光照适应 */
  lightingAdaptation: {
    enabled: boolean;
    autoAdjust: boolean;
    brightness: number;
    contrast: number;
  };
  /** 物理适应 */
  physicsAdaptation: {
    enabled: boolean;
    gravity: THREE.Vector3;
    friction: number;
    airResistance: number;
  };
  /** 音频适应 */
  audioAdaptation: {
    enabled: boolean;
    reverb: number;
    echo: number;
    volume: number;
  };
}

/**
 * 智能行为
 */
export interface IntelligentBehavior {
  /** 行为ID */
  id: string;
  /** 行为类型 */
  type: BehaviorType;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
  /** 触发条件 */
  conditions: Array<{
    type: string;
    value: any;
    operator: 'equals' | 'greater' | 'less' | 'contains';
  }>;
  /** 行为参数 */
  parameters: Map<string, any>;
  /** 冷却时间 */
  cooldown: number;
  /** 最后执行时间 */
  lastExecuted: number;
}

/**
 * 场景交互系统配置
 */
export interface SceneInteractionConfig {
  /** 是否启用拖拽控制 */
  enableDragControl: boolean;
  /** 是否启用环境适应 */
  enableEnvironmentAdaptation: boolean;
  /** 是否启用智能行为 */
  enableIntelligentBehavior: boolean;
  /** 是否启用碰撞检测 */
  enableCollisionDetection: boolean;
  /** 是否启用物理模拟 */
  enablePhysicsSimulation: boolean;
  /** 交互检测频率 */
  interactionCheckFrequency: number;
  /** 最大交互距离 */
  maxInteractionDistance: number;
  /** 是否启用调试 */
  debug: boolean;
}

/**
 * 场景交互系统
 * 实现数字人与场景的高级交互，支持拖拽控制、环境适应和智能行为
 */
export class SceneInteractionSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'SceneInteractionSystem';

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 系统配置 */
  private config: SceneInteractionConfig;

  /** 交互区域 */
  private interactionZones: Map<string, InteractionZone>;

  /** 拖拽控制器 */
  private dragControllers: Map<string, DragController>;

  /** 环境适应器 */
  private environmentAdapters: Map<string, EnvironmentAdapter>;

  /** 智能行为 */
  private intelligentBehaviors: Map<string, IntelligentBehavior[]>;

  /** 当前交互状态 */
  private interactionStates: Map<string, InteractionState>;

  /** 拖拽状态 */
  private dragStates: Map<string, {
    isDragging: boolean;
    startPosition: THREE.Vector3;
    currentPosition: THREE.Vector3;
    offset: THREE.Vector3;
  }>;

  /** 鼠标/触摸状态 */
  private inputState: {
    isPressed: boolean;
    position: THREE.Vector2;
    startPosition: THREE.Vector2;
    target?: Entity;
  };

  /** 射线投射器 */
  private raycaster: THREE.Raycaster;

  /** 相机引用 */
  private camera?: THREE.Camera;

  /** 场景引用 */
  private scene?: THREE.Scene;

  /**
   * 构造函数
   * @param world 世界实例
   * @param config 系统配置
   */
  constructor(world: World, config: Partial<SceneInteractionConfig> = {}) {
    super(world);

    this.eventEmitter = new EventEmitter();
    this.config = {
      enableDragControl: true,
      enableEnvironmentAdaptation: true,
      enableIntelligentBehavior: true,
      enableCollisionDetection: true,
      enablePhysicsSimulation: false,
      interactionCheckFrequency: 60, // 60 FPS
      maxInteractionDistance: 10.0,
      debug: false,
      ...config
    };

    this.interactionZones = new Map();
    this.dragControllers = new Map();
    this.environmentAdapters = new Map();
    this.intelligentBehaviors = new Map();
    this.interactionStates = new Map();
    this.dragStates = new Map();

    this.inputState = {
      isPressed: false,
      position: new THREE.Vector2(),
      startPosition: new THREE.Vector2()
    };

    this.raycaster = new THREE.Raycaster();

    this.initializeDefaultBehaviors();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return SceneInteractionSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.config.debug) {
      console.log('SceneInteractionSystem: 初始化场景交互系统');
    }

    // 设置输入事件监听
    this.setupInputListeners();

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    // 更新拖拽控制
    if (this.config.enableDragControl) {
      this.updateDragControl(deltaTime);
    }

    // 更新智能行为
    if (this.config.enableIntelligentBehavior) {
      this.updateIntelligentBehaviors(deltaTime);
    }

    // 更新交互检测
    this.updateInteractionDetection(deltaTime);

    // 更新环境适应
    if (this.config.enableEnvironmentAdaptation) {
      this.updateEnvironmentAdaptation(deltaTime);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 移除输入事件监听
    this.removeInputListeners();

    // 清理数据
    this.interactionZones.clear();
    this.dragControllers.clear();
    this.environmentAdapters.clear();
    this.intelligentBehaviors.clear();
    this.interactionStates.clear();
    this.dragStates.clear();

    // 移除事件监听器
    this.world.off('entityAdded', this.onEntityAdded.bind(this));
    this.world.off('entityRemoved', this.onEntityRemoved.bind(this));

    if (this.config.debug) {
      console.log('SceneInteractionSystem: 系统已销毁');
    }
  }

  /**
   * 初始化默认行为
   */
  private initializeDefaultBehaviors(): void {
    // 创建默认的智能行为
    const defaultBehaviors: IntelligentBehavior[] = [
      {
        id: 'idle_behavior',
        type: BehaviorType.IDLE,
        enabled: true,
        priority: 1,
        conditions: [],
        parameters: new Map([
          ['idleAnimations', ['idle1', 'idle2', 'idle3']],
          ['switchInterval', 5000]
        ]),
        cooldown: 1000,
        lastExecuted: 0
      },
      {
        id: 'follow_behavior',
        type: BehaviorType.FOLLOW,
        enabled: false,
        priority: 3,
        conditions: [
          { type: 'distance', value: 5.0, operator: 'less' },
          { type: 'target', value: 'player', operator: 'equals' }
        ],
        parameters: new Map([
          ['followDistance', 2.0],
          ['followSpeed', 1.5]
        ]),
        cooldown: 100,
        lastExecuted: 0
      },
      {
        id: 'react_behavior',
        type: BehaviorType.REACT,
        enabled: true,
        priority: 5,
        conditions: [
          { type: 'interaction', value: 'click', operator: 'equals' }
        ],
        parameters: new Map([
          ['reactionAnimations', ['wave', 'nod', 'smile']],
          ['reactionDuration', 2000]
        ]),
        cooldown: 3000,
        lastExecuted: 0
      }
    ];

    // 这些行为将在实体添加时分配
    this.defaultBehaviors = defaultBehaviors;
  }

  // 其他方法将在下一部分实现...

  /**
   * 设置输入事件监听
   */
  private setupInputListeners(): void {
    if (typeof window !== 'undefined') {
      // 鼠标事件
      window.addEventListener('mousedown', this.onMouseDown.bind(this));
      window.addEventListener('mousemove', this.onMouseMove.bind(this));
      window.addEventListener('mouseup', this.onMouseUp.bind(this));
      window.addEventListener('click', this.onClick.bind(this));

      // 触摸事件
      window.addEventListener('touchstart', this.onTouchStart.bind(this));
      window.addEventListener('touchmove', this.onTouchMove.bind(this));
      window.addEventListener('touchend', this.onTouchEnd.bind(this));

      if (this.config.debug) {
        console.log('SceneInteractionSystem: 输入事件监听已设置');
      }
    }
  }

  /**
   * 移除输入事件监听
   */
  private removeInputListeners(): void {
    if (typeof window !== 'undefined') {
      window.removeEventListener('mousedown', this.onMouseDown.bind(this));
      window.removeEventListener('mousemove', this.onMouseMove.bind(this));
      window.removeEventListener('mouseup', this.onMouseUp.bind(this));
      window.removeEventListener('click', this.onClick.bind(this));
      window.removeEventListener('touchstart', this.onTouchStart.bind(this));
      window.removeEventListener('touchmove', this.onTouchMove.bind(this));
      window.removeEventListener('touchend', this.onTouchEnd.bind(this));
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (digitalHuman) {
      // 初始化交互状态
      this.interactionStates.set(entity.id, InteractionState.IDLE);

      // 初始化拖拽控制器
      if (this.config.enableDragControl) {
        this.initializeDragController(entity.id);
      }

      // 初始化环境适应器
      if (this.config.enableEnvironmentAdaptation) {
        this.initializeEnvironmentAdapter(entity.id);
      }

      // 初始化智能行为
      if (this.config.enableIntelligentBehavior) {
        this.initializeIntelligentBehaviors(entity.id);
      }

      if (this.config.debug) {
        console.log(`为数字人 ${entity.id} 初始化场景交互系统`);
      }
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    // 清理相关数据
    this.interactionStates.delete(entity.id);
    this.dragControllers.delete(entity.id);
    this.environmentAdapters.delete(entity.id);
    this.intelligentBehaviors.delete(entity.id);
    this.dragStates.delete(entity.id);

    if (this.config.debug) {
      console.log(`清理数字人 ${entity.id} 的场景交互数据`);
    }
  }

  // 存储默认行为
  private defaultBehaviors: IntelligentBehavior[] = [];

  // ==================== 拖拽控制 ====================

  /**
   * 初始化拖拽控制器
   * @param entityId 实体ID
   */
  private initializeDragController(entityId: string): void {
    const dragController: DragController = {
      enabled: true,
      constraints: { x: true, y: true, z: true },
      speed: 1.0,
      damping: 0.9,
      snapPoints: [],
      snapDistance: 0.5
    };

    this.dragControllers.set(entityId, dragController);

    // 初始化拖拽状态
    this.dragStates.set(entityId, {
      isDragging: false,
      startPosition: new THREE.Vector3(),
      currentPosition: new THREE.Vector3(),
      offset: new THREE.Vector3()
    });
  }

  /**
   * 更新拖拽控制
   * @param deltaTime 时间增量
   */
  private updateDragControl(deltaTime: number): void {
    for (const [entityId, dragState] of this.dragStates) {
      if (dragState.isDragging) {
        const entity = this.world.getEntity(entityId);
        if (!entity) continue;

        const transform = entity.getComponent<Transform>(Transform.TYPE);
        if (!transform) continue;

        const dragController = this.dragControllers.get(entityId);
        if (!dragController || !dragController.enabled) continue;

        // 应用拖拽移动
        this.applyDragMovement(entity, dragController, dragState, deltaTime);
      }
    }
  }

  /**
   * 应用拖拽移动
   * @param entity 实体
   * @param controller 拖拽控制器
   * @param dragState 拖拽状态
   * @param deltaTime 时间增量
   */
  private applyDragMovement(
    entity: Entity,
    controller: DragController,
    dragState: any,
    deltaTime: number
  ): void {
    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return;

    // 计算目标位置
    const targetPosition = dragState.currentPosition.clone();

    // 应用约束
    if (!controller.constraints.x) targetPosition.x = transform.position.x;
    if (!controller.constraints.y) targetPosition.y = transform.position.y;
    if (!controller.constraints.z) targetPosition.z = transform.position.z;

    // 应用边界限制
    if (controller.bounds) {
      targetPosition.clamp(controller.bounds.min, controller.bounds.max);
    }

    // 检查吸附点
    const snappedPosition = this.checkSnapPoints(targetPosition, controller.snapPoints, controller.snapDistance);

    // 平滑移动
    const currentPosition = transform.position;
    const newPosition = currentPosition.clone().lerp(snappedPosition, controller.speed * deltaTime);

    // 应用阻尼
    newPosition.lerp(currentPosition, controller.damping);

    // 更新位置
    transform.position.copy(newPosition);

    // 触发拖拽事件
    this.emit('entityDragged', entity.id, newPosition);
  }

  /**
   * 检查吸附点
   * @param position 当前位置
   * @param snapPoints 吸附点列表
   * @param snapDistance 吸附距离
   * @returns 吸附后的位置
   */
  private checkSnapPoints(
    position: THREE.Vector3,
    snapPoints: THREE.Vector3[],
    snapDistance: number
  ): THREE.Vector3 {
    for (const snapPoint of snapPoints) {
      const distance = position.distanceTo(snapPoint);
      if (distance <= snapDistance) {
        return snapPoint.clone();
      }
    }
    return position;
  }

  // ==================== 输入事件处理 ====================

  /**
   * 鼠标按下事件
   * @param event 鼠标事件
   */
  private onMouseDown(event: MouseEvent): void {
    this.inputState.isPressed = true;
    this.inputState.position.set(event.clientX, event.clientY);
    this.inputState.startPosition.copy(this.inputState.position);

    // 射线检测
    const intersectedEntity = this.performRaycast(this.inputState.position);
    if (intersectedEntity) {
      this.inputState.target = intersectedEntity;
      this.startDrag(intersectedEntity.id, this.inputState.position);
    }
  }

  /**
   * 鼠标移动事件
   * @param event 鼠标事件
   */
  private onMouseMove(event: MouseEvent): void {
    this.inputState.position.set(event.clientX, event.clientY);

    if (this.inputState.isPressed && this.inputState.target) {
      this.updateDrag(this.inputState.target.id, this.inputState.position);
    }
  }

  /**
   * 鼠标释放事件
   * @param event 鼠标事件
   */
  private onMouseUp(event: MouseEvent): void {
    if (this.inputState.target) {
      this.endDrag(this.inputState.target.id);
    }

    this.inputState.isPressed = false;
    this.inputState.target = undefined;
  }

  /**
   * 点击事件
   * @param event 鼠标事件
   */
  private onClick(event: MouseEvent): void {
    const position = new THREE.Vector2(event.clientX, event.clientY);
    const intersectedEntity = this.performRaycast(position);

    if (intersectedEntity) {
      this.triggerInteraction(intersectedEntity.id, InteractionType.CLICK, {
        position: position,
        button: event.button
      });
    }
  }

  /**
   * 触摸开始事件
   * @param event 触摸事件
   */
  private onTouchStart(event: TouchEvent): void {
    if (event.touches.length > 0) {
      const touch = event.touches[0];
      this.onMouseDown({ clientX: touch.clientX, clientY: touch.clientY } as MouseEvent);
    }
  }

  /**
   * 触摸移动事件
   * @param event 触摸事件
   */
  private onTouchMove(event: TouchEvent): void {
    if (event.touches.length > 0) {
      const touch = event.touches[0];
      this.onMouseMove({ clientX: touch.clientX, clientY: touch.clientY } as MouseEvent);
    }
  }

  /**
   * 触摸结束事件
   * @param event 触摸事件
   */
  private onTouchEnd(event: TouchEvent): void {
    this.onMouseUp({} as MouseEvent);
  }

  /**
   * 执行射线检测
   * @param screenPosition 屏幕位置
   * @returns 相交的实体
   */
  private performRaycast(screenPosition: THREE.Vector2): Entity | null {
    if (!this.camera || !this.scene) return null;

    // 将屏幕坐标转换为标准化设备坐标
    const mouse = new THREE.Vector2();
    mouse.x = (screenPosition.x / window.innerWidth) * 2 - 1;
    mouse.y = -(screenPosition.y / window.innerHeight) * 2 + 1;

    // 设置射线
    this.raycaster.setFromCamera(mouse, this.camera);

    // 获取所有数字人实体的网格
    const meshes: THREE.Mesh[] = [];
    for (const entity of this.world.getEntities()) {
      const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
      if (digitalHuman && digitalHuman.mesh) {
        meshes.push(digitalHuman.mesh);
        // 存储实体引用
        (digitalHuman.mesh as any).userData = { entity };
      }
    }

    // 执行射线检测
    const intersects = this.raycaster.intersectObjects(meshes);
    if (intersects.length > 0) {
      const userData = (intersects[0].object as any).userData;
      return userData?.entity || null;
    }

    return null;
  }

  // ==================== 拖拽操作 ====================

  /**
   * 开始拖拽
   * @param entityId 实体ID
   * @param screenPosition 屏幕位置
   */
  private startDrag(entityId: string, screenPosition: THREE.Vector2): void {
    const dragController = this.dragControllers.get(entityId);
    if (!dragController || !dragController.enabled) return;

    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return;

    const dragState = this.dragStates.get(entityId);
    if (!dragState) return;

    // 设置拖拽状态
    dragState.isDragging = true;
    dragState.startPosition.copy(transform.position);
    dragState.currentPosition.copy(transform.position);

    // 计算鼠标到物体的偏移
    const worldPosition = this.screenToWorld(screenPosition);
    if (worldPosition) {
      dragState.offset.subVectors(transform.position, worldPosition);
    }

    // 更新交互状态
    this.interactionStates.set(entityId, InteractionState.ACTIVE);

    // 触发事件
    this.emit('dragStarted', entityId, transform.position);

    if (this.config.debug) {
      console.log(`开始拖拽实体: ${entityId}`);
    }
  }

  /**
   * 更新拖拽
   * @param entityId 实体ID
   * @param screenPosition 屏幕位置
   */
  private updateDrag(entityId: string, screenPosition: THREE.Vector2): void {
    const dragState = this.dragStates.get(entityId);
    if (!dragState || !dragState.isDragging) return;

    // 将屏幕位置转换为世界位置
    const worldPosition = this.screenToWorld(screenPosition);
    if (worldPosition) {
      dragState.currentPosition.addVectors(worldPosition, dragState.offset);
    }
  }

  /**
   * 结束拖拽
   * @param entityId 实体ID
   */
  private endDrag(entityId: string): void {
    const dragState = this.dragStates.get(entityId);
    if (!dragState || !dragState.isDragging) return;

    // 重置拖拽状态
    dragState.isDragging = false;

    // 更新交互状态
    this.interactionStates.set(entityId, InteractionState.IDLE);

    // 触发事件
    this.emit('dragEnded', entityId, dragState.currentPosition);

    if (this.config.debug) {
      console.log(`结束拖拽实体: ${entityId}`);
    }
  }

  /**
   * 屏幕坐标转世界坐标
   * @param screenPosition 屏幕位置
   * @returns 世界位置
   */
  private screenToWorld(screenPosition: THREE.Vector2): THREE.Vector3 | null {
    if (!this.camera) return null;

    // 简化的转换实现
    // 实际实现需要考虑相机类型、投影矩阵等
    const mouse = new THREE.Vector2();
    mouse.x = (screenPosition.x / window.innerWidth) * 2 - 1;
    mouse.y = -(screenPosition.y / window.innerHeight) * 2 + 1;

    const vector = new THREE.Vector3(mouse.x, mouse.y, 0.5);
    vector.unproject(this.camera);

    if (this.camera instanceof THREE.PerspectiveCamera) {
      const direction = vector.sub(this.camera.position).normalize();
      const distance = -this.camera.position.z / direction.z;
      return this.camera.position.clone().add(direction.multiplyScalar(distance));
    }

    return vector;
  }

  // ==================== 公共API方法 ====================

  /**
   * 设置相机引用
   * @param camera 相机
   */
  public setCamera(camera: THREE.Camera): void {
    this.camera = camera;
  }

  /**
   * 设置场景引用
   * @param scene 场景
   */
  public setScene(scene: THREE.Scene): void {
    this.scene = scene;
  }

  /**
   * 启用/禁用实体拖拽
   * @param entityId 实体ID
   * @param enabled 是否启用
   */
  public setDragEnabled(entityId: string, enabled: boolean): void {
    const dragController = this.dragControllers.get(entityId);
    if (dragController) {
      dragController.enabled = enabled;
    }
  }

  /**
   * 设置拖拽约束
   * @param entityId 实体ID
   * @param constraints 约束
   */
  public setDragConstraints(entityId: string, constraints: { x: boolean; y: boolean; z: boolean }): void {
    const dragController = this.dragControllers.get(entityId);
    if (dragController) {
      dragController.constraints = constraints;
    }
  }

  /**
   * 添加交互区域
   * @param zone 交互区域
   */
  public addInteractionZone(zone: InteractionZone): void {
    this.interactionZones.set(zone.id, zone);
    this.emit('interactionZoneAdded', zone);

    if (this.config.debug) {
      console.log(`添加交互区域: ${zone.name}`);
    }
  }

  /**
   * 移除交互区域
   * @param zoneId 区域ID
   */
  public removeInteractionZone(zoneId: string): boolean {
    const zone = this.interactionZones.get(zoneId);
    if (!zone) return false;

    this.interactionZones.delete(zoneId);
    this.emit('interactionZoneRemoved', zone);

    if (this.config.debug) {
      console.log(`移除交互区域: ${zone.name}`);
    }

    return true;
  }

  /**
   * 触发交互
   * @param entityId 实体ID
   * @param type 交互类型
   * @param data 交互数据
   */
  public triggerInteraction(entityId: string, type: InteractionType, data: any = {}): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    // 更新交互状态
    this.interactionStates.set(entityId, InteractionState.PROCESSING);

    // 触发智能行为
    this.triggerIntelligentBehavior(entityId, type, data);

    // 检查交互区域
    this.checkInteractionZones(entityId, type, data);

    // 触发事件
    this.emit('interactionTriggered', entityId, type, data);

    if (this.config.debug) {
      console.log(`触发交互: ${type} for 实体 ${entityId}`);
    }

    // 恢复空闲状态
    setTimeout(() => {
      this.interactionStates.set(entityId, InteractionState.IDLE);
    }, 100);
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param args 参数
   */
  private emit(event: string, ...args: any[]): void {
    this.eventEmitter.emit(event, ...args);
  }

  // ==================== 智能行为系统 ====================

  /**
   * 初始化智能行为
   * @param entityId 实体ID
   */
  private initializeIntelligentBehaviors(entityId: string): void {
    // 复制默认行为
    const behaviors = this.defaultBehaviors.map(behavior => ({
      ...behavior,
      id: `${entityId}_${behavior.id}`,
      parameters: new Map(behavior.parameters),
      lastExecuted: 0
    }));

    this.intelligentBehaviors.set(entityId, behaviors);
  }

  /**
   * 更新智能行为
   * @param deltaTime 时间增量
   */
  private updateIntelligentBehaviors(deltaTime: number): void {
    const currentTime = Date.now();

    for (const [entityId, behaviors] of this.intelligentBehaviors) {
      const entity = this.world.getEntity(entityId);
      if (!entity) continue;

      // 按优先级排序
      const sortedBehaviors = behaviors
        .filter(b => b.enabled)
        .sort((a, b) => b.priority - a.priority);

      for (const behavior of sortedBehaviors) {
        // 检查冷却时间
        if (currentTime - behavior.lastExecuted < behavior.cooldown) {
          continue;
        }

        // 检查触发条件
        if (this.checkBehaviorConditions(entityId, behavior)) {
          this.executeBehavior(entityId, behavior);
          behavior.lastExecuted = currentTime;
          break; // 只执行一个行为
        }
      }
    }
  }

  /**
   * 检查行为条件
   * @param entityId 实体ID
   * @param behavior 行为
   * @returns 是否满足条件
   */
  private checkBehaviorConditions(entityId: string, behavior: IntelligentBehavior): boolean {
    if (behavior.conditions.length === 0) {
      return true; // 无条件行为
    }

    for (const condition of behavior.conditions) {
      if (!this.evaluateCondition(entityId, condition)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 评估条件
   * @param entityId 实体ID
   * @param condition 条件
   * @returns 是否满足
   */
  private evaluateCondition(entityId: string, condition: any): boolean {
    const entity = this.world.getEntity(entityId);
    if (!entity) return false;

    switch (condition.type) {
      case 'distance':
        // 检查与目标的距离
        return this.checkDistanceCondition(entityId, condition);
      case 'interaction':
        // 检查交互状态
        return this.checkInteractionCondition(entityId, condition);
      case 'time':
        // 检查时间条件
        return this.checkTimeCondition(condition);
      default:
        return false;
    }
  }

  /**
   * 检查距离条件
   * @param entityId 实体ID
   * @param condition 条件
   * @returns 是否满足
   */
  private checkDistanceCondition(entityId: string, condition: any): boolean {
    // 简化实现：检查与原点的距离
    const entity = this.world.getEntity(entityId);
    if (!entity) return false;

    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return false;

    const distance = transform.position.length();

    switch (condition.operator) {
      case 'less':
        return distance < condition.value;
      case 'greater':
        return distance > condition.value;
      case 'equals':
        return Math.abs(distance - condition.value) < 0.1;
      default:
        return false;
    }
  }

  /**
   * 检查交互条件
   * @param entityId 实体ID
   * @param condition 条件
   * @returns 是否满足
   */
  private checkInteractionCondition(entityId: string, condition: any): boolean {
    const state = this.interactionStates.get(entityId);
    return state === InteractionState.PROCESSING;
  }

  /**
   * 检查时间条件
   * @param condition 条件
   * @returns 是否满足
   */
  private checkTimeCondition(condition: any): boolean {
    const currentTime = Date.now();
    const timeValue = condition.value;

    switch (condition.operator) {
      case 'greater':
        return currentTime > timeValue;
      case 'less':
        return currentTime < timeValue;
      default:
        return false;
    }
  }

  /**
   * 执行行为
   * @param entityId 实体ID
   * @param behavior 行为
   */
  private executeBehavior(entityId: string, behavior: IntelligentBehavior): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    switch (behavior.type) {
      case BehaviorType.IDLE:
        this.executeIdleBehavior(entityId, behavior);
        break;
      case BehaviorType.FOLLOW:
        this.executeFollowBehavior(entityId, behavior);
        break;
      case BehaviorType.REACT:
        this.executeReactBehavior(entityId, behavior);
        break;
      case BehaviorType.EXPLORE:
        this.executeExploreBehavior(entityId, behavior);
        break;
      default:
        break;
    }

    this.emit('behaviorExecuted', entityId, behavior);

    if (this.config.debug) {
      console.log(`执行行为: ${behavior.type} for 实体 ${entityId}`);
    }
  }

  /**
   * 执行空闲行为
   * @param entityId 实体ID
   * @param behavior 行为
   */
  private executeIdleBehavior(entityId: string, behavior: IntelligentBehavior): void {
    const animations = behavior.parameters.get('idleAnimations') || ['idle'];
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)];

    // 这里应该触发动画播放
    this.emit('animationRequested', entityId, randomAnimation);
  }

  /**
   * 执行跟随行为
   * @param entityId 实体ID
   * @param behavior 行为
   */
  private executeFollowBehavior(entityId: string, behavior: IntelligentBehavior): void {
    const followDistance = behavior.parameters.get('followDistance') || 2.0;
    const followSpeed = behavior.parameters.get('followSpeed') || 1.0;

    // 简化的跟随逻辑
    this.emit('movementRequested', entityId, { type: 'follow', distance: followDistance, speed: followSpeed });
  }

  /**
   * 执行反应行为
   * @param entityId 实体ID
   * @param behavior 行为
   */
  private executeReactBehavior(entityId: string, behavior: IntelligentBehavior): void {
    const reactions = behavior.parameters.get('reactionAnimations') || ['wave'];
    const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
    const duration = behavior.parameters.get('reactionDuration') || 2000;

    this.emit('animationRequested', entityId, randomReaction, duration);
  }

  /**
   * 执行探索行为
   * @param entityId 实体ID
   * @param behavior 行为
   */
  private executeExploreBehavior(entityId: string, behavior: IntelligentBehavior): void {
    // 生成随机探索目标
    const randomTarget = new THREE.Vector3(
      (Math.random() - 0.5) * 10,
      0,
      (Math.random() - 0.5) * 10
    );

    this.emit('movementRequested', entityId, { type: 'moveTo', target: randomTarget });
  }

  /**
   * 触发智能行为
   * @param entityId 实体ID
   * @param interactionType 交互类型
   * @param data 交互数据
   */
  private triggerIntelligentBehavior(entityId: string, interactionType: InteractionType, data: any): void {
    const behaviors = this.intelligentBehaviors.get(entityId);
    if (!behaviors) return;

    // 查找匹配的反应行为
    const reactionBehaviors = behaviors.filter(b =>
      b.type === BehaviorType.REACT &&
      b.enabled &&
      b.conditions.some(c => c.type === 'interaction' && c.value === interactionType)
    );

    for (const behavior of reactionBehaviors) {
      this.executeBehavior(entityId, behavior);
    }
  }

  // ==================== 环境适应系统 ====================

  /**
   * 初始化环境适应器
   * @param entityId 实体ID
   */
  private initializeEnvironmentAdapter(entityId: string): void {
    const adapter: EnvironmentAdapter = {
      environmentType: EnvironmentType.VIRTUAL,
      lightingAdaptation: {
        enabled: true,
        autoAdjust: true,
        brightness: 1.0,
        contrast: 1.0
      },
      physicsAdaptation: {
        enabled: true,
        gravity: new THREE.Vector3(0, -9.81, 0),
        friction: 0.8,
        airResistance: 0.1
      },
      audioAdaptation: {
        enabled: true,
        reverb: 0.3,
        echo: 0.1,
        volume: 1.0
      }
    };

    this.environmentAdapters.set(entityId, adapter);
  }

  /**
   * 更新环境适应
   * @param deltaTime 时间增量
   */
  private updateEnvironmentAdaptation(deltaTime: number): void {
    for (const [entityId, adapter] of this.environmentAdapters) {
      const entity = this.world.getEntity(entityId);
      if (!entity) continue;

      // 更新光照适应
      if (adapter.lightingAdaptation.enabled) {
        this.updateLightingAdaptation(entityId, adapter);
      }

      // 更新物理适应
      if (adapter.physicsAdaptation.enabled) {
        this.updatePhysicsAdaptation(entityId, adapter, deltaTime);
      }

      // 更新音频适应
      if (adapter.audioAdaptation.enabled) {
        this.updateAudioAdaptation(entityId, adapter);
      }
    }
  }

  /**
   * 更新光照适应
   * @param entityId 实体ID
   * @param adapter 环境适应器
   */
  private updateLightingAdaptation(entityId: string, adapter: EnvironmentAdapter): void {
    if (!adapter.lightingAdaptation.autoAdjust) return;

    // 简化的光照适应逻辑
    // 实际实现需要分析场景光照条件
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
    if (!digitalHuman) return;

    // 调整材质亮度和对比度
    this.emit('lightingAdaptationRequested', entityId, {
      brightness: adapter.lightingAdaptation.brightness,
      contrast: adapter.lightingAdaptation.contrast
    });
  }

  /**
   * 更新物理适应
   * @param entityId 实体ID
   * @param adapter 环境适应器
   * @param deltaTime 时间增量
   */
  private updatePhysicsAdaptation(entityId: string, adapter: EnvironmentAdapter, deltaTime: number): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return;

    // 应用重力
    const gravity = adapter.physicsAdaptation.gravity;
    const gravityEffect = gravity.clone().multiplyScalar(deltaTime);

    // 这里应该有速度组件来处理物理运动
    // 简化实现：直接应用重力到位置
    if (transform.position.y > 0) {
      transform.position.add(gravityEffect);
    }

    // 应用摩擦力
    // 简化实现：减少水平移动
    const friction = adapter.physicsAdaptation.friction;
    transform.position.x *= (1 - friction * deltaTime);
    transform.position.z *= (1 - friction * deltaTime);
  }

  /**
   * 更新音频适应
   * @param entityId 实体ID
   * @param adapter 环境适应器
   */
  private updateAudioAdaptation(entityId: string, adapter: EnvironmentAdapter): void {
    // 简化的音频适应逻辑
    this.emit('audioAdaptationRequested', entityId, {
      reverb: adapter.audioAdaptation.reverb,
      echo: adapter.audioAdaptation.echo,
      volume: adapter.audioAdaptation.volume
    });
  }

  // ==================== 交互检测 ====================

  /**
   * 更新交互检测
   * @param deltaTime 时间增量
   */
  private updateInteractionDetection(deltaTime: number): void {
    // 检测实体之间的交互
    this.detectEntityInteractions();

    // 检测与交互区域的交互
    this.detectZoneInteractions();

    // 检测碰撞
    if (this.config.enableCollisionDetection) {
      this.detectCollisions();
    }
  }

  /**
   * 检测实体交互
   */
  private detectEntityInteractions(): void {
    const entities = this.world.getEntities();
    const digitalHumans = entities.filter(e =>
      e.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE)
    );

    // 检测数字人之间的接近交互
    for (let i = 0; i < digitalHumans.length; i++) {
      for (let j = i + 1; j < digitalHumans.length; j++) {
        const entity1 = digitalHumans[i];
        const entity2 = digitalHumans[j];

        const transform1 = entity1.getComponent<Transform>(Transform.TYPE);
        const transform2 = entity2.getComponent<Transform>(Transform.TYPE);

        if (transform1 && transform2) {
          const distance = transform1.position.distanceTo(transform2.position);

          if (distance <= this.config.maxInteractionDistance) {
            this.emit('proximityInteraction', entity1.id, entity2.id, distance);
          }
        }
      }
    }
  }

  /**
   * 检测区域交互
   */
  private detectZoneInteractions(): void {
    for (const zone of this.interactionZones.values()) {
      if (!zone.enabled) continue;

      for (const entity of this.world.getEntities()) {
        const digitalHuman = entity.getComponent<DigitalHumanComponent>(DigitalHumanComponent.TYPE);
        if (!digitalHuman) continue;

        const transform = entity.getComponent<Transform>(Transform.TYPE);
        if (!transform) continue;

        // 检查实体是否在交互区域内
        if (this.isEntityInZone(transform.position, zone)) {
          this.triggerZoneInteraction(entity.id, zone);
        }
      }
    }
  }

  /**
   * 检查实体是否在区域内
   * @param position 实体位置
   * @param zone 交互区域
   * @returns 是否在区域内
   */
  private isEntityInZone(position: THREE.Vector3, zone: InteractionZone): boolean {
    const relativePosition = position.clone().sub(zone.position);

    switch (zone.type) {
      case 'sphere':
        return relativePosition.length() <= zone.size.x;
      case 'box':
        return Math.abs(relativePosition.x) <= zone.size.x / 2 &&
               Math.abs(relativePosition.y) <= zone.size.y / 2 &&
               Math.abs(relativePosition.z) <= zone.size.z / 2;
      case 'plane':
        return Math.abs(relativePosition.y) <= 0.1 &&
               Math.abs(relativePosition.x) <= zone.size.x / 2 &&
               Math.abs(relativePosition.z) <= zone.size.z / 2;
      default:
        return false;
    }
  }

  /**
   * 触发区域交互
   * @param entityId 实体ID
   * @param zone 交互区域
   */
  private triggerZoneInteraction(entityId: string, zone: InteractionZone): void {
    // 执行区域响应
    for (const response of zone.responses) {
      setTimeout(() => {
        this.executeZoneResponse(entityId, zone, response);
      }, response.delay);
    }

    this.emit('zoneInteraction', entityId, zone);
  }

  /**
   * 执行区域响应
   * @param entityId 实体ID
   * @param zone 交互区域
   * @param response 响应
   */
  private executeZoneResponse(entityId: string, zone: InteractionZone, response: InteractionResponse): void {
    switch (response.type) {
      case 'animation':
        this.emit('animationRequested', entityId, response.data, response.duration);
        break;
      case 'sound':
        this.emit('soundRequested', entityId, response.data);
        break;
      case 'effect':
        this.emit('effectRequested', entityId, response.data);
        break;
      case 'script':
        this.emit('scriptRequested', entityId, response.data);
        break;
    }
  }

  /**
   * 检测碰撞
   */
  private detectCollisions(): void {
    // 简化的碰撞检测实现
    // 实际实现需要使用物理引擎或更复杂的碰撞检测算法
  }

  /**
   * 检查交互区域
   * @param entityId 实体ID
   * @param type 交互类型
   * @param data 交互数据
   */
  private checkInteractionZones(entityId: string, type: InteractionType, data: any): void {
    const entity = this.world.getEntity(entityId);
    if (!entity) return;

    const transform = entity.getComponent<Transform>(Transform.TYPE);
    if (!transform) return;

    for (const zone of this.interactionZones.values()) {
      if (!zone.enabled || !zone.interactionTypes.includes(type)) continue;

      if (this.isEntityInZone(transform.position, zone)) {
        this.triggerZoneInteraction(entityId, zone);
      }
    }
  }
}
