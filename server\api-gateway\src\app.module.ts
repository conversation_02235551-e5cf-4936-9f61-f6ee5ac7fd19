/**
 * API网关应用模块
 */
import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { ProjectsModule } from './projects/projects.module';
import { AssetsModule } from './assets/assets.module';
import { RenderModule } from './render/render.module';
import { KnowledgeModule } from './knowledge/knowledge.module';
import { RequestIdMiddleware } from './common/middleware/request-id.middleware';
// import { CircuitBreakerModule } from '@shared/circuit-breaker';
// import { RateLimiterModule } from '@shared/rate-limiter';
// import { CacheModule, CacheLevel } from '@shared/cache';
// import { CompressionModule, CompressionAlgorithm } from '@shared/compression';
// import { BatchModule } from '@shared/batch';
// import { RateLimiterType } from '@shared/rate-limiter/rate-limiter.interface';
import { ResilienceDemoController } from './common/controllers/resilience-demo.controller';
// import { PerformanceDemoController } from './common/controllers/performance-demo.controller';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
    }),

    // 文件上传模块
    MulterModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        storage: diskStorage({
          destination: (_req, file, cb) => {
            let uploadPath = './uploads';

            // 根据文件类型确定上传路径
            if (file.mimetype.startsWith('image/')) {
              uploadPath += '/images';
            } else if (file.mimetype.startsWith('audio/')) {
              uploadPath += '/audio';
            } else if (
              file.mimetype.startsWith('model/') ||
              file.originalname.endsWith('.gltf') ||
              file.originalname.endsWith('.glb') ||
              file.originalname.endsWith('.obj') ||
              file.originalname.endsWith('.fbx')
            ) {
              uploadPath += '/models';
            } else {
              uploadPath += '/other';
            }

            cb(null, uploadPath);
          },
          filename: (_req, file, cb) => {
            const uniqueSuffix = uuidv4();
            const ext = extname(file.originalname);
            cb(null, `${uniqueSuffix}${ext}`);
          },
        }),
        limits: {
          fileSize: configService.get<number>('MAX_FILE_SIZE', 100 * 1024 * 1024), // 默认100MB
        },
      }),
    }),

    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'PROJECT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('PROJECT_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('PROJECT_SERVICE_PORT', 3002),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'ASSET_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('ASSET_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('ASSET_SERVICE_PORT', 3003),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'RENDER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('RENDER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('RENDER_SERVICE_PORT', 3004),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),

    // 熔断器模块 - 暂时禁用
    // CircuitBreakerModule.register({
    //   isGlobal: true,
    //   defaultOptions: {
    //     failureThreshold: 5,
    //     successThreshold: 3,
    //     timeout: 10000,
    //     resetTimeout: 30000,
    //     enableFallback: true,
    //     enableMonitoring: true,
    //   },
    // }),

    // 限流器模块 - 暂时禁用
    // RateLimiterModule.register({
    //   isGlobal: true,
    //   defaultOptions: {
    //     type: RateLimiterType.TOKEN_BUCKET,
    //     windowMs: 60000, // 1分钟
    //     maxRequests: 100, // 每分钟100个请求
    //     distributed: false,
    //     enableMonitoring: true,
    //   },
    // }),

    // 缓存模块 - 暂时禁用
    // CacheModule.register({
    //   enabled: true,
    //   levels: [CacheLevel.MEMORY, CacheLevel.REDIS],
    //   memoryTtl: 60000, // 1分钟
    //   redisTtl: 300000, // 5分钟
    //   maxEntries: 10000,
    //   enableAdaptiveTtl: true,
    //   enableStats: true,
    //   redis: {
    //     host: process.env.REDIS_HOST || 'localhost',
    //     port: parseInt(process.env.REDIS_PORT || '6379'),
    //     password: process.env.REDIS_PASSWORD || '',
    //     db: parseInt(process.env.REDIS_DB || '0'),
    //     keyPrefix: 'cache:',
    //   },
    // }),

    // 压缩模块 - 暂时禁用
    // CompressionModule.register({
    //   algorithm: CompressionAlgorithm.DEFLATE,
    //   level: 6,
    //   minSize: 100,
    //   enableAdaptive: true,
    //   enableCache: true,
    //   cacheSize: 1000,
    //   collectStats: true,
    // }),

    // 批处理模块 - 暂时禁用
    // BatchModule.register({
    //   enabled: true,
    //   maxBatchSize: 100,
    //   maxWaitTime: 50,
    //   maxQueueSize: 1000,
    //   enableStats: true,
    // }),

    // 功能模块
    AuthModule,
    UsersModule,
    ProjectsModule,
    AssetsModule,
    RenderModule,
    KnowledgeModule,
  ],
  controllers: [AppController, ResilienceDemoController], // PerformanceDemoController - 暂时禁用
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes('*');
  }
}
