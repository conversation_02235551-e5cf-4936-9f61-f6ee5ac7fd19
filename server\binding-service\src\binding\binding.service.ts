import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CacheService } from '../cache/cache.service';
import { DigitalHuman, KnowledgeBase, DigitalHumanKnowledgeBinding } from '../entities';

export interface BindingConfig {
  type: 'primary' | 'secondary';
  priority: number;
  searchWeight?: number;
  enabledFeatures?: string[];
  customSettings?: any;
}

export interface BindingResult {
  bindingId: string;
  digitalHumanId: string;
  knowledgeBaseId: string;
  status: string;
  message: string;
}

export interface BatchBindingResult {
  successful: BindingResult[];
  failed: string[];
  totalCount: number;
  successCount: number;
  failureCount: number;
}

export interface KnowledgeBaseBinding {
  bindingId: string;
  bindingType: string;
  priority: number;
  config: any;
  knowledgeBase: {
    id: string;
    name: string;
    description: string;
    category: string;
    language: string;
    vectorIndexName: string;
    documentCount: number;
    totalChunks: number;
  };
}

@Injectable()
export class BindingService {
  constructor(
    @InjectRepository(DigitalHuman)
    private readonly digitalHumanRepository: Repository<DigitalHuman>,
    @InjectRepository(KnowledgeBase)
    private readonly knowledgeBaseRepository: Repository<KnowledgeBase>,
    @InjectRepository(DigitalHumanKnowledgeBinding)
    private readonly bindingRepository: Repository<DigitalHumanKnowledgeBinding>,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * 绑定知识库到数字人
   */
  async bindKnowledgeBase(
    digitalHumanId: string,
    knowledgeBaseId: string,
    bindingConfig: BindingConfig,
    userId: string,
  ): Promise<BindingResult> {
    try {
      // 1. 验证数字人和知识库存在
      await this.validateEntities(digitalHumanId, knowledgeBaseId);

      // 2. 检查绑定是否已存在
      const existingBinding = await this.getBinding(digitalHumanId, knowledgeBaseId);
      if (existingBinding) {
        throw new Error('绑定关系已存在');
      }

      // 3. 验证优先级唯一性
      await this.validatePriority(digitalHumanId, bindingConfig.priority);

      // 4. 创建绑定记录
      const bindingId = await this.createBinding(
        digitalHumanId,
        knowledgeBaseId,
        bindingConfig,
        userId,
      );

      // 5. 清除相关缓存
      await this.clearBindingCache(digitalHumanId);

      return {
        bindingId,
        digitalHumanId,
        knowledgeBaseId,
        status: 'active',
        message: '知识库绑定成功',
      };
    } catch (error) {
      throw new Error(`绑定失败: ${error.message}`);
    }
  }

  /**
   * 验证数字人和知识库存在
   */
  private async validateEntities(
    digitalHumanId: string,
    knowledgeBaseId: string,
  ): Promise<void> {
    const digitalHuman = await this.digitalHumanRepository.findOne({
      where: { id: digitalHumanId, status: 'active' },
    });

    if (!digitalHuman) {
      throw new Error('数字人不存在或已禁用');
    }

    const knowledgeBase = await this.knowledgeBaseRepository.findOne({
      where: { id: knowledgeBaseId, status: 'active' },
    });

    if (!knowledgeBase) {
      throw new Error('知识库不存在或已禁用');
    }
  }

  /**
   * 获取现有绑定
   */
  private async getBinding(
    digitalHumanId: string,
    knowledgeBaseId: string,
  ): Promise<DigitalHumanKnowledgeBinding | null> {
    return await this.bindingRepository.findOne({
      where: {
        digitalHumanId,
        knowledgeBaseId,
        isActive: true,
      },
    });
  }

  /**
   * 验证优先级唯一性
   */
  private async validatePriority(
    digitalHumanId: string,
    priority: number,
  ): Promise<void> {
    const existingBinding = await this.bindingRepository.findOne({
      where: {
        digitalHumanId,
        priority,
        isActive: true,
      },
    });

    if (existingBinding) {
      throw new Error(`优先级 ${priority} 已被使用`);
    }
  }

  /**
   * 创建绑定记录
   */
  private async createBinding(
    digitalHumanId: string,
    knowledgeBaseId: string,
    config: BindingConfig,
    userId: string,
  ): Promise<string> {
    const binding = this.bindingRepository.create({
      digitalHumanId,
      knowledgeBaseId,
      bindingType: config.type,
      priority: config.priority,
      bindingConfig: config,
      createdBy: userId,
      isActive: true,
    });

    const savedBinding = await this.bindingRepository.save(binding);
    return savedBinding.id;
  }

  /**
   * 获取数字人的所有知识库绑定
   */
  async getDigitalHumanKnowledgeBases(
    digitalHumanId: string,
  ): Promise<KnowledgeBaseBinding[]> {
    // 先尝试从缓存获取
    const cacheKey = `digital_human:${digitalHumanId}:knowledge_bases`;
    const cached = await this.cacheService.get<KnowledgeBaseBinding[]>(cacheKey);

    if (cached) {
      return cached;
    }

    // 从数据库查询
    const bindings = await this.bindingRepository
      .createQueryBuilder('binding')
      .leftJoinAndSelect('binding.knowledgeBase', 'kb')
      .where('binding.digitalHumanId = :digitalHumanId', { digitalHumanId })
      .andWhere('binding.isActive = :isActive', { isActive: true })
      .orderBy('binding.priority', 'ASC')
      .addOrderBy('binding.createdAt', 'ASC')
      .getMany();

    const result = bindings.map(binding => ({
      bindingId: binding.id,
      bindingType: binding.bindingType,
      priority: binding.priority,
      config: binding.bindingConfig,
      knowledgeBase: {
        id: binding.knowledgeBase.id,
        name: binding.knowledgeBase.name,
        description: binding.knowledgeBase.description,
        category: binding.knowledgeBase.category,
        language: binding.knowledgeBase.language,
        vectorIndexName: binding.knowledgeBase.vectorIndexName,
        documentCount: binding.knowledgeBase.documentCount,
        totalChunks: binding.knowledgeBase.totalChunks,
      },
    }));

    // 缓存结果
    await this.cacheService.set(cacheKey, result, 3600);

    return result;
  }

  /**
   * 解绑知识库
   */
  async unbindKnowledgeBase(
    digitalHumanId: string,
    knowledgeBaseId: string,
    userId: string,
  ): Promise<void> {
    const binding = await this.getBinding(digitalHumanId, knowledgeBaseId);
    
    if (!binding) {
      throw new Error('绑定关系不存在');
    }

    // 软删除：设置为非活跃状态
    await this.bindingRepository.update(binding.id, {
      isActive: false,
      updatedAt: new Date(),
    });

    // 清除缓存
    await this.clearBindingCache(digitalHumanId);
  }

  /**
   * 更新绑定配置
   */
  async updateBindingConfig(
    digitalHumanId: string,
    knowledgeBaseId: string,
    config: Partial<BindingConfig>,
    userId: string,
  ): Promise<void> {
    const binding = await this.getBinding(digitalHumanId, knowledgeBaseId);
    
    if (!binding) {
      throw new Error('绑定关系不存在');
    }

    // 如果更新优先级，需要验证唯一性
    if (config.priority && config.priority !== binding.priority) {
      await this.validatePriority(digitalHumanId, config.priority);
    }

    const updateData: any = {
      updatedAt: new Date(),
    };

    if (config.type) updateData.bindingType = config.type;
    if (config.priority) updateData.priority = config.priority;
    if (config) updateData.bindingConfig = { ...binding.bindingConfig, ...config };

    await this.bindingRepository.update(binding.id, updateData);

    // 清除缓存
    await this.clearBindingCache(digitalHumanId);
  }

  /**
   * 批量绑定知识库
   */
  async batchBindKnowledgeBases(
    digitalHumanId: string,
    knowledgeBaseIds: string[],
    userId: string,
  ): Promise<BatchBindingResult> {
    const results: BindingResult[] = [];
    const errors: string[] = [];

    // 获取当前最大优先级
    let maxPriority = await this.getMaxPriority(digitalHumanId);

    for (const knowledgeBaseId of knowledgeBaseIds) {
      try {
        maxPriority++;
        const result = await this.bindKnowledgeBase(
          digitalHumanId,
          knowledgeBaseId,
          { type: 'secondary', priority: maxPriority },
          userId,
        );
        results.push(result);
      } catch (error) {
        errors.push(`${knowledgeBaseId}: ${error.message}`);
      }
    }

    return {
      successful: results,
      failed: errors,
      totalCount: knowledgeBaseIds.length,
      successCount: results.length,
      failureCount: errors.length,
    };
  }

  /**
   * 获取最大优先级
   */
  private async getMaxPriority(digitalHumanId: string): Promise<number> {
    const result = await this.bindingRepository
      .createQueryBuilder('binding')
      .select('MAX(binding.priority)', 'maxPriority')
      .where('binding.digitalHumanId = :digitalHumanId', { digitalHumanId })
      .andWhere('binding.isActive = :isActive', { isActive: true })
      .getRawOne();

    return result?.maxPriority || 0;
  }

  /**
   * 重新排序绑定优先级
   */
  async reorderBindings(
    digitalHumanId: string,
    bindingOrders: { bindingId: string; priority: number }[],
    userId: string,
  ): Promise<void> {
    // 验证所有绑定都属于该数字人
    const bindings = await this.bindingRepository.find({
      where: {
        digitalHumanId,
        isActive: true,
      },
    });

    const bindingIds = bindings.map(b => b.id);
    const orderIds = bindingOrders.map(o => o.bindingId);

    if (!orderIds.every(id => bindingIds.includes(id))) {
      throw new Error('包含无效的绑定ID');
    }

    // 批量更新优先级
    for (const order of bindingOrders) {
      await this.bindingRepository.update(order.bindingId, {
        priority: order.priority,
        updatedAt: new Date(),
      });
    }

    // 清除缓存
    await this.clearBindingCache(digitalHumanId);
  }

  /**
   * 清除绑定缓存
   */
  private async clearBindingCache(digitalHumanId: string): Promise<void> {
    const cacheKeys = [
      `digital_human:${digitalHumanId}:knowledge_bases`,
      `digital_human:${digitalHumanId}:bindings`,
    ];

    for (const key of cacheKeys) {
      await this.cacheService.del(key);
    }
  }

  /**
   * 获取知识库的绑定统计
   */
  async getKnowledgeBaseBindingStats(knowledgeBaseId: string): Promise<any> {
    const stats = await this.bindingRepository
      .createQueryBuilder('binding')
      .select([
        'COUNT(*) as totalBindings',
        'COUNT(CASE WHEN binding.bindingType = \'primary\' THEN 1 END) as primaryBindings',
        'COUNT(CASE WHEN binding.bindingType = \'secondary\' THEN 1 END) as secondaryBindings',
      ])
      .where('binding.knowledgeBaseId = :knowledgeBaseId', { knowledgeBaseId })
      .andWhere('binding.isActive = :isActive', { isActive: true })
      .getRawOne();

    return {
      totalBindings: parseInt(stats.totalBindings),
      primaryBindings: parseInt(stats.primaryBindings),
      secondaryBindings: parseInt(stats.secondaryBindings),
    };
  }
}
