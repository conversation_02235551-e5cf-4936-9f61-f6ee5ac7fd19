# RAG数字人交互系统故障排除指南

本指南帮助您诊断和解决RAG数字人交互系统中的常见问题。

## 目录

1. [系统诊断工具](#系统诊断工具)
2. [服务启动问题](#服务启动问题)
3. [数据库问题](#数据库问题)
4. [文档处理问题](#文档处理问题)
5. [RAG查询问题](#rag查询问题)
6. [性能问题](#性能问题)
7. [网络和连接问题](#网络和连接问题)
8. [监控和日志](#监控和日志)

## 系统诊断工具

### 健康检查脚本

```bash
# 运行完整的系统健康检查
./scripts/health-check.sh

# 检查特定服务
curl -f http://localhost:8080/health
curl -f http://localhost:3000/health  # 知识库服务
curl -f http://localhost:3001/health  # 绑定服务
curl -f http://localhost:3002/health  # RAG引擎
```

### 服务状态检查

```bash
# 检查Docker容器状态
docker-compose -f docker-compose.production.yml ps

# 检查容器资源使用
docker stats

# 检查容器日志
docker-compose -f docker-compose.production.yml logs --tail=100 service-name
```

### 系统资源检查

```bash
# 检查内存使用
free -h
cat /proc/meminfo

# 检查磁盘空间
df -h
du -sh /var/lib/docker

# 检查CPU使用
top
htop
```

## 服务启动问题

### 问题1: 容器启动失败

**症状**: 容器无法启动或立即退出

**诊断步骤**:

```bash
# 查看容器退出状态
docker-compose -f docker-compose.production.yml ps

# 查看详细错误信息
docker-compose -f docker-compose.production.yml logs service-name

# 检查容器配置
docker-compose -f docker-compose.production.yml config
```

**常见原因和解决方案**:

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   lsof -i :3000
   
   # 解决方案：修改端口或停止占用进程
   sudo kill -9 PID
   ```

2. **环境变量缺失**
   ```bash
   # 检查环境变量文件
   cat .env.production
   
   # 确保必需的环境变量已设置
   grep -E "(POSTGRES_PASSWORD|JWT_SECRET|OPENAI_API_KEY)" .env.production
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la .env.production
   ls -la nginx/ssl/
   
   # 修复权限
   chmod 600 .env.production
   chmod 644 nginx/ssl/cert.pem
   chmod 600 nginx/ssl/key.pem
   ```

### 问题2: 服务依赖启动顺序

**症状**: 应用服务在依赖服务就绪前启动失败

**解决方案**:

```bash
# 手动按顺序启动服务
docker-compose -f docker-compose.production.yml up -d postgres redis minio chroma
sleep 30
docker-compose -f docker-compose.production.yml up -d knowledge-service binding-service
sleep 15
docker-compose -f docker-compose.production.yml up -d rag-engine api-gateway
```

## 数据库问题

### 问题1: PostgreSQL连接失败

**症状**: 应用无法连接到数据库

**诊断步骤**:

```bash
# 检查PostgreSQL状态
docker-compose -f docker-compose.production.yml exec postgres pg_isready -U postgres

# 测试连接
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "SELECT version();"

# 检查连接配置
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "SHOW listen_addresses;"
```

**解决方案**:

1. **重置数据库密码**
   ```bash
   docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "ALTER USER postgres PASSWORD 'newpassword';"
   ```

2. **检查数据库初始化**
   ```bash
   # 查看数据库列表
   docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -l
   
   # 重新初始化数据库
   docker-compose -f docker-compose.production.yml down -v
   docker-compose -f docker-compose.production.yml up -d postgres
   ```

### 问题2: 数据库性能问题

**症状**: 查询响应缓慢

**诊断步骤**:

```sql
-- 查看活跃连接
SELECT pid, usename, application_name, client_addr, state, query_start, query 
FROM pg_stat_activity 
WHERE state = 'active';

-- 查看慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 查看锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

**解决方案**:

```sql
-- 创建缺失的索引
CREATE INDEX CONCURRENTLY idx_documents_knowledge_base ON knowledge_documents(knowledge_base_id);
CREATE INDEX CONCURRENTLY idx_chunks_document ON document_chunks(document_id);

-- 更新统计信息
ANALYZE;

-- 清理无用数据
VACUUM ANALYZE;
```

## 文档处理问题

### 问题1: 文档上传失败

**症状**: 文档上传返回错误或超时

**诊断步骤**:

```bash
# 检查文件大小限制
grep MAX_FILE_SIZE .env.production

# 检查存储空间
df -h
docker exec rag-minio df -h

# 检查MinIO状态
curl -f http://localhost:9000/minio/health/live
```

**解决方案**:

1. **增加文件大小限制**
   ```bash
   # 修改环境变量
   echo "MAX_FILE_SIZE=209715200" >> .env.production  # 200MB
   
   # 重启服务
   docker-compose -f docker-compose.production.yml restart knowledge-service
   ```

2. **清理存储空间**
   ```bash
   # 清理Docker卷
   docker volume prune
   
   # 清理MinIO中的临时文件
   docker exec rag-minio find /data -name "*.tmp" -delete
   ```

### 问题2: 文档处理卡住

**症状**: 文档状态长时间停留在"processing"

**诊断步骤**:

```bash
# 检查处理队列
docker-compose -f docker-compose.production.yml logs knowledge-service | grep "processing"

# 检查向量数据库连接
curl -f http://localhost:8000/api/v1/heartbeat  # Chroma

# 检查OpenAI API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
```

**解决方案**:

```bash
# 重新处理失败的文档
curl -X POST "http://localhost:8080/api/knowledge-bases/{kb_id}/documents/{doc_id}/reprocess" \
  -H "Authorization: Bearer $TOKEN"

# 清理处理队列
docker-compose -f docker-compose.production.yml restart knowledge-service
```

## RAG查询问题

### 问题1: 查询返回空结果

**症状**: RAG查询没有找到相关内容

**诊断步骤**:

```bash
# 检查知识库绑定
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8080/api/digital-humans/{dh_id}/knowledge-bases"

# 检查向量数据库中的数据
curl "http://localhost:8000/api/v1/collections"

# 测试向量搜索
curl -X POST "http://localhost:8080/api/rag/search" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"query":"测试查询","digitalHumanId":"dh_id","maxResults":5}'
```

**解决方案**:

1. **检查文档处理状态**
   ```bash
   # 确保文档已完成处理
   curl -H "Authorization: Bearer $TOKEN" \
     "http://localhost:8080/api/knowledge-bases/{kb_id}/documents"
   ```

2. **调整搜索参数**
   ```json
   {
     "question": "你的问题",
     "digitalHumanId": "dh_id",
     "maxResults": 10,
     "threshold": 0.5
   }
   ```

### 问题2: 查询响应缓慢

**症状**: RAG查询响应时间过长

**诊断步骤**:

```bash
# 检查各服务响应时间
time curl -f http://localhost:8000/api/v1/heartbeat  # Chroma
time curl -f http://localhost:3002/health           # RAG引擎

# 检查系统负载
top
iostat -x 1
```

**解决方案**:

1. **优化向量数据库**
   ```bash
   # 重启Chroma
   docker-compose -f docker-compose.production.yml restart chroma
   
   # 检查Chroma内存使用
   docker stats rag-chroma
   ```

2. **调整查询参数**
   ```json
   {
     "maxResults": 5,
     "temperature": 0.7
   }
   ```

## 性能问题

### 问题1: 内存使用过高

**症状**: 系统内存不足，服务被OOM杀死

**诊断步骤**:

```bash
# 检查内存使用
free -h
docker stats

# 查看OOM日志
dmesg | grep -i "killed process"
journalctl -u docker | grep -i "oom"
```

**解决方案**:

1. **增加swap空间**
   ```bash
   sudo fallocate -l 4G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   echo '/swapfile none swap sw 0 0' >> /etc/fstab
   ```

2. **限制容器内存使用**
   ```yaml
   # docker-compose.production.yml
   services:
     knowledge-service:
       deploy:
         resources:
           limits:
             memory: 1G
   ```

### 问题2: CPU使用率过高

**症状**: 系统响应缓慢，CPU使用率持续高位

**诊断步骤**:

```bash
# 查看CPU使用情况
top -p $(docker inspect --format='{{.State.Pid}}' rag-knowledge-service)
htop

# 查看进程详情
ps aux | grep node
```

**解决方案**:

1. **优化并发处理**
   ```bash
   # 减少并发处理数量
   echo "MAX_CONCURRENT_PROCESSING=3" >> .env.production
   docker-compose -f docker-compose.production.yml restart knowledge-service
   ```

2. **扩展服务实例**
   ```yaml
   # docker-compose.production.yml
   services:
     knowledge-service:
       deploy:
         replicas: 3
   ```

## 网络和连接问题

### 问题1: 服务间通信失败

**症状**: 服务无法相互访问

**诊断步骤**:

```bash
# 检查网络连接
docker network ls
docker network inspect rag-network

# 测试服务间连接
docker-compose -f docker-compose.production.yml exec api-gateway ping knowledge-service
docker-compose -f docker-compose.production.yml exec rag-engine ping chroma
```

**解决方案**:

```bash
# 重建网络
docker-compose -f docker-compose.production.yml down
docker network prune
docker-compose -f docker-compose.production.yml up -d
```

### 问题2: 外部API访问失败

**症状**: 无法访问OpenAI API或其他外部服务

**诊断步骤**:

```bash
# 测试网络连接
curl -I https://api.openai.com
nslookup api.openai.com

# 检查防火墙设置
sudo ufw status
iptables -L
```

**解决方案**:

```bash
# 配置代理（如果需要）
export https_proxy=http://proxy.company.com:8080
export http_proxy=http://proxy.company.com:8080

# 或在Docker中配置代理
mkdir -p ~/.docker
cat > ~/.docker/config.json << EOF
{
  "proxies": {
    "default": {
      "httpProxy": "http://proxy.company.com:8080",
      "httpsProxy": "http://proxy.company.com:8080"
    }
  }
}
EOF
```

## 监控和日志

### 日志收集和分析

```bash
# 收集所有服务日志
docker-compose -f docker-compose.production.yml logs > system.log

# 分析错误日志
grep -i error system.log
grep -i exception system.log
grep -i failed system.log

# 查看特定时间段的日志
docker-compose -f docker-compose.production.yml logs --since="2025-07-09T10:00:00" --until="2025-07-09T11:00:00"
```

### 性能监控

```bash
# 查看Prometheus指标
curl http://localhost:9090/api/v1/query?query=up

# 查看Grafana仪表板
open http://localhost:3000

# 导出监控数据
curl -G http://localhost:9090/api/v1/query_range \
  --data-urlencode 'query=rate(http_requests_total[5m])' \
  --data-urlencode 'start=2025-07-09T10:00:00Z' \
  --data-urlencode 'end=2025-07-09T11:00:00Z' \
  --data-urlencode 'step=60s'
```

## 紧急恢复程序

### 系统完全故障

```bash
# 1. 停止所有服务
docker-compose -f docker-compose.production.yml down

# 2. 检查系统资源
df -h
free -h

# 3. 清理Docker资源
docker system prune -a -f

# 4. 从备份恢复
./scripts/restore.sh /backup/latest_backup.tar.gz

# 5. 重新启动系统
./scripts/deploy.sh production deploy
```

### 数据恢复

```bash
# 从最新备份恢复数据库
./scripts/restore-database.sh /backup/postgres_backup.sql.gz

# 恢复MinIO数据
./scripts/restore-minio.sh /backup/minio_backup.tar.gz

# 重建向量索引
curl -X POST "http://localhost:8080/api/admin/rebuild-vectors" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

## 获取帮助

如果以上方法无法解决问题，请：

1. **收集诊断信息**
   ```bash
   ./scripts/collect-diagnostics.sh
   ```

2. **联系技术支持**
   - 邮箱: <EMAIL>
   - 提供诊断信息和错误日志
   - 描述问题复现步骤

3. **查看文档和社区**
   - 官方文档: https://docs.yourdomain.com
   - GitHub Issues: https://github.com/your-org/rag-digital-human/issues
   - 社区论坛: https://community.yourdomain.com
