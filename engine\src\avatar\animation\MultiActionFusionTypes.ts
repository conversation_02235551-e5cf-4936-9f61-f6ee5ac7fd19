/**
 * 多动作融合系统类型定义
 */
import * as THREE from 'three';
import { BIPSkeletonData } from '../bip/BIPSkeletonParser';

/**
 * 动作冲突类型
 */
export enum ActionConflictType {
  NAME_COLLISION = 'name_collision',
  BONE_CONFLICT = 'bone_conflict',
  TIMING_CONFLICT = 'timing_conflict',
  KEYFRAME_CONFLICT = 'keyframe_conflict',
  TRANSFORM_CONFLICT = 'transform_conflict'
}

/**
 * 动作冲突
 */
export interface ActionConflict {
  /** 冲突类型 */
  type: ActionConflictType;
  /** 冲突的动画1 */
  animation1: AnimationClip;
  /** 冲突的动画2 */
  animation2: AnimationClip;
  /** 冲突的骨骼列表 */
  conflictBones?: string[];
  /** 冲突描述 */
  description: string;
  /** 严重程度 (1-10) */
  severity: number;
}

/**
 * 冲突解决方案类型
 */
export enum ConflictResolutionType {
  RENAME = 'rename',
  BONE_REMAPPING = 'bone_remapping',
  TIME_OFFSET = 'time_offset',
  PRIORITY_OVERRIDE = 'priority_override',
  BLEND_MERGE = 'blend_merge',
  IGNORE = 'ignore'
}

/**
 * 冲突解决方案
 */
export interface ConflictResolution {
  /** 解决方案类型 */
  type: ConflictResolutionType;
  /** 原始名称 */
  originalName?: string;
  /** 新名称 */
  newName?: string;
  /** 冲突的骨骼 */
  conflictBones?: string[];
  /** 骨骼重映射 */
  remapping?: Map<string, string>;
  /** 时间偏移 */
  timeOffset?: number;
  /** 优先级 */
  priority?: number;
  /** 解决原因 */
  reason: string;
}

/**
 * BIP导入结果
 */
export interface BIPImportResult {
  /** 文件名 */
  fileName: string;
  /** 是否成功 */
  success: boolean;
  /** 动画数量 */
  animationCount?: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 批量导入结果
 */
export interface BatchImportResult {
  /** 总文件数 */
  totalFiles: number;
  /** 成功数量 */
  successCount: number;
  /** 导入结果列表 */
  results: BIPImportResult[];
  /** 冲突列表 */
  conflicts: ActionConflict[];
  /** 总动作数量 */
  actionCount: number;
}

/**
 * 统一骨骼系统
 */
export interface UnifiedSkeleton {
  /** 所有骨骼名称 */
  bones: string[];
  /** 骨骼层级结构 */
  hierarchy: BoneHierarchy;
  /** 骨骼映射 */
  mapping: Map<string, string>;
}

/**
 * 骨骼层级结构
 */
export interface BoneHierarchy {
  /** 根骨骼 */
  root: string;
  /** 父子关系映射 */
  parentChild: Map<string, string[]>;
  /** 子父关系映射 */
  childParent: Map<string, string>;
  /** 深度映射 */
  depthMap: Map<string, number>;
}

/**
 * BIP数据
 */
export interface BIPData {
  /** 文件名 */
  fileName: string;
  /** 骨骼数据 */
  skeleton: BIPSkeletonData;
  /** 动画数据 */
  animations: BIPAnimation[];
}

/**
 * BIP动画数据
 */
export interface BIPAnimation {
  /** 动画名称 */
  name: string;
  /** 持续时间 */
  duration: number;
  /** 帧率 */
  frameRate: number;
  /** 位置轨道 */
  positionTracks: AnimationTrack[];
  /** 旋转轨道 */
  rotationTracks: AnimationTrack[];
  /** 缩放轨道 */
  scaleTracks: AnimationTrack[];
}

/**
 * 动画轨道
 */
export interface AnimationTrack {
  /** 骨骼名称 */
  boneName: string;
  /** 关键帧 */
  keyframes: Keyframe[];
  /** 插值类型 */
  interpolation: InterpolationType;
}

/**
 * 关键帧
 */
export interface Keyframe {
  /** 时间 */
  time: number;
  /** 值 */
  value: THREE.Vector3 | THREE.Quaternion | number;
}

/**
 * 插值类型
 */
export enum InterpolationType {
  LINEAR = 'linear',
  STEP = 'step',
  CUBIC = 'cubic'
}

/**
 * 动画状态
 */
export interface AnimationState {
  /** 状态名称 */
  name: string;
  /** 动画片段 */
  animation: AnimationClip;
  /** 是否循环 */
  isLooping: boolean;
  /** 混合模式 */
  blendMode: BlendMode;
  /** 权重 */
  weight: number;
  /** 速度 */
  speed: number;
}

/**
 * 状态转换
 */
export interface StateTransition {
  /** 源状态 */
  fromState: string;
  /** 目标状态 */
  toState: string;
  /** 转换时间 */
  duration: number;
  /** 转换条件 */
  conditions: TransitionCondition[];
}

/**
 * 转换条件
 */
export interface TransitionCondition {
  /** 参数名称 */
  parameter: string;
  /** 比较类型 */
  comparison: ComparisonType;
  /** 阈值 */
  threshold: number;
}

/**
 * 比较类型
 */
export enum ComparisonType {
  GREATER = 'greater',
  LESS = 'less',
  EQUAL = 'equal',
  NOT_EQUAL = 'not_equal'
}

/**
 * 混合模式
 */
export enum BlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply'
}

/**
 * 播放动作选项
 */
export interface PlayActionOptions {
  /** 淡入时间 */
  fadeTime?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 播放速度 */
  speed?: number;
}

/**
 * 动作序列
 */
export interface ActionSequence {
  /** 序列名称 */
  name: string;
  /** 动作步骤 */
  steps: ActionStep[];
}

/**
 * 动作步骤
 */
export interface ActionStep {
  /** 动作名称 */
  actionName: string;
  /** 淡入时间 */
  fadeTime: number;
  /** 等待时间 */
  waitTime: number;
  /** 是否循环 */
  loop: boolean;
  /** 播放速度 */
  speed: number;
}

/**
 * 动作混合配置
 */
export interface ActionBlendConfig {
  /** 混合动作列表 */
  actions: BlendAction[];
  /** 混合模式 */
  blendMode: BlendMode;
  /** 混合持续时间 */
  duration: number;
}

/**
 * 混合动作
 */
export interface BlendAction {
  /** 动作名称 */
  name: string;
  /** 权重 */
  weight: number;
}

/**
 * 动作库项目
 */
export interface ActionLibraryItem {
  /** 动作名称 */
  name: string;
  /** 持续时间 */
  duration: number;
  /** 来源文件 */
  sourceFile: string;
  /** 状态 */
  status: 'ready' | 'processing';
  /** 是否有冲突 */
  hasConflict: boolean;
  /** 动画片段 */
  clip: AnimationClip;
}

/**
 * 动画片段（简化版本，实际应该使用Three.js的AnimationClip）
 */
export interface AnimationClip {
  /** 名称 */
  name: string;
  /** 持续时间 */
  duration: number;
  /** 轨道 */
  tracks: AnimationTrack[];
}
