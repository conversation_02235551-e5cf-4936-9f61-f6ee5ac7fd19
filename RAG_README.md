# RAG数字人交互系统

基于RAG（Retrieval-Augmented Generation）技术的数字人交互系统，支持多知识库管理、文档上传处理、向量检索和智能问答。

## 🚀 功能特性

### 核心功能
- **知识库管理**: 支持创建、编辑、删除知识库，支持多种文档格式
- **文档处理**: 自动解析、分块、向量化处理上传的文档
- **数字人绑定**: 支持数字人与多个知识库的灵活绑定配置
- **智能问答**: 基于RAG技术的多知识库并行检索和回答生成
- **流式响应**: 支持实时流式回答输出

### 技术特性
- **微服务架构**: 模块化设计，易于扩展和维护
- **多向量数据库支持**: 支持Pinecone、Chroma、Milvus、Weaviate
- **多LLM支持**: 支持OpenAI、Azure OpenAI、Anthropic、本地模型
- **高可用性**: 支持负载均衡、健康检查、自动重启
- **监控告警**: 集成Prometheus和Grafana监控
- **安全性**: JWT认证、HTTPS、速率限制

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   Nginx代理     │
│                 │◄──►│                 │◄──►│                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ 知识库服务   │ │  绑定服务   │ │ RAG引擎    │
        │              │ │             │ │            │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼───────────────▼───────────────▼───────┐
        │                基础设施层                      │
        │  PostgreSQL │ Redis │ MinIO │ Chroma/Pinecone │
        └───────────────────────────────────────────────┘
```

## 📋 系统要求

### 最低配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps

### 推荐配置
- **CPU**: 8核心
- **内存**: 16GB RAM
- **存储**: 200GB SSD
- **网络**: 1Gbps

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (开发环境)
- PostgreSQL 15+
- Redis 7+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/rag-digital-human.git
cd rag-digital-human
```

### 2. 配置环境变量
```bash
cp .env.production.example .env.production
# 编辑 .env.production 文件，配置必要的环境变量
```

### 3. 部署系统
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 部署生产环境
./scripts/deploy.sh production deploy
```

### 4. 验证部署
```bash
# 运行健康检查
./scripts/health-check.sh

# 访问API文档
open http://localhost:8080/api/docs
```

## 📖 详细文档

### API文档
- **Swagger UI**: http://localhost:8080/api/docs
- **知识库管理**: `/api/knowledge-bases`
- **数字人管理**: `/api/digital-humans`
- **RAG查询**: `/api/rag`

### 服务端口
- **API网关**: 8080 (HTTP), 443 (HTTPS)
- **知识库服务**: 3000
- **绑定服务**: 3001
- **RAG引擎**: 3002
- **PostgreSQL**: 5432
- **Redis**: 6379
- **MinIO**: 9000 (API), 9001 (Console)
- **Chroma**: 8000
- **Prometheus**: 9090
- **Grafana**: 3000

## 🔧 配置说明

### 环境变量配置
详细的环境变量配置请参考 `.env.production` 文件。

### 知识库配置
```yaml
# 支持的文档格式
supported_formats:
  - PDF
  - DOCX
  - TXT
  - HTML
  - Markdown

# 文档处理配置
chunk_size: 1000        # 文档分块大小
chunk_overlap: 200      # 分块重叠大小
max_file_size: 100MB    # 最大文件大小
```

### 向量数据库配置
```yaml
# Chroma配置 (默认)
vector_db:
  type: chroma
  endpoint: http://chroma:8000
  dimension: 1536

# Pinecone配置
vector_db:
  type: pinecone
  api_key: your_pinecone_api_key
  environment: us-west1-gcp
  index_name: rag-index
```

## 🔍 监控和运维

### 监控面板
- **Grafana**: http://localhost:3000
  - 用户名: admin
  - 密码: 见环境变量 `GRAFANA_PASSWORD`

### 日志查看
```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f knowledge-service
```

### 备份和恢复
```bash
# 创建备份
./scripts/backup.sh

# 恢复备份
./scripts/restore.sh backup_file.tar.gz
```

## 🛠️ 开发指南

### 本地开发环境
```bash
# 安装依赖
cd server/knowledge-service && npm install
cd server/binding-service && npm install
cd server/rag-engine && npm install
cd server/api-gateway && npm install

# 启动开发环境
./scripts/deploy.sh dev deploy
```

### 代码结构
```
├── server/                 # 后端服务
│   ├── knowledge-service/  # 知识库管理服务
│   ├── binding-service/    # 数字人绑定服务
│   ├── rag-engine/        # RAG查询引擎
│   └── api-gateway/       # API网关
├── scripts/               # 部署和运维脚本
├── nginx/                 # Nginx配置
├── monitoring/            # 监控配置
├── k8s/                   # Kubernetes配置
└── docs/                  # 文档
```

## 🔒 安全说明

### 安全配置
- 使用强密码和密钥
- 启用HTTPS和SSL证书
- 配置防火墙规则
- 定期更新依赖包
- 启用访问日志和审计

### 数据保护
- 数据库加密存储
- 文件上传病毒扫描
- 敏感信息脱敏
- 定期数据备份

## 📞 支持和帮助

### 常见问题
请查看 [FAQ文档](docs/FAQ.md)

### 问题反馈
- **GitHub Issues**: https://github.com/your-org/rag-digital-human/issues
- **邮箱**: <EMAIL>

### 技术支持
- **文档**: https://docs.yourdomain.com
- **社区**: https://community.yourdomain.com

## 📄 许可证

本项目采用 MIT 许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目的支持：
- [NestJS](https://nestjs.com/)
- [PostgreSQL](https://www.postgresql.org/)
- [Redis](https://redis.io/)
- [MinIO](https://min.io/)
- [Chroma](https://www.trychroma.com/)
- [Docker](https://www.docker.com/)
- [Prometheus](https://prometheus.io/)
- [Grafana](https://grafana.com/)

---

**版本**: 1.0.0  
**更新时间**: 2025-07-09  
**维护者**: RAG开发团队
