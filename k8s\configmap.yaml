apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-config
  namespace: rag-system
data:
  # 应用配置
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # 数据库配置
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "digital_human_rag"
  DB_USERNAME: "postgres"
  DB_SSL: "true"
  
  # Redis配置
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # MinIO配置
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_BUCKET: "knowledge-bases"
  
  # 向量数据库配置
  VECTOR_DB_TYPE: "chroma"
  VECTOR_DB_ENDPOINT: "http://chroma-service:8000"
  VECTOR_DB_DIMENSION: "1536"
  
  # 文档处理配置
  CHUNK_SIZE: "1000"
  CHUNK_OVERLAP: "200"
  MAX_FILE_SIZE: "104857600"
  
  # 服务URL配置
  KNOWLEDGE_SERVICE_URL: "http://knowledge-service:3000"
  BINDING_SERVICE_URL: "http://binding-service:3001"
  RAG_ENGINE_URL: "http://rag-engine:3002"
  
  # 安全配置
  ALLOWED_ORIGINS: "https://yourdomain.com,https://www.yourdomain.com"
  RATE_LIMIT_MAX: "1000"
  RATE_LIMIT_WINDOW_MS: "900000"
  
  # 缓存配置
  CACHE_TTL: "3600"
  
  # 性能配置
  MAX_CONCURRENT_UPLOADS: "10"
  MAX_CONCURRENT_PROCESSING: "5"
  BATCH_SIZE: "100"

---
apiVersion: v1
kind: Secret
metadata:
  name: rag-secrets
  namespace: rag-system
type: Opaque
stringData:
  # 数据库密码
  POSTGRES_PASSWORD: "your_secure_postgres_password"
  
  # Redis密码
  REDIS_PASSWORD: "your_secure_redis_password"
  
  # MinIO密钥
  MINIO_ACCESS_KEY: "your_minio_access_key"
  MINIO_SECRET_KEY: "your_secure_minio_secret_key"
  
  # JWT密钥
  JWT_SECRET: "your_very_secure_jwt_secret_key_at_least_32_characters"
  
  # OpenAI API密钥
  OPENAI_API_KEY: "your_openai_api_key"
  
  # 监控密码
  GRAFANA_PASSWORD: "your_secure_grafana_password"
  
  # 邮件配置
  SMTP_PASSWORD: "your_smtp_password"
