/**
 * BIP动画加载器
 * 加载和解析BIP动画文件，转换为标准动画格式
 */
import * as THREE from 'three';
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPSkeletonData, StandardBoneType } from '../bip';
import { AnimationClip, AnimationTrack, AnimationKeyframe } from './AnimationBlendingSystem';

/**
 * BIP动画数据
 */
export interface BIPAnimationData {
  /** 动画名称 */
  name: string;
  /** 帧率 */
  frameRate: number;
  /** 总帧数 */
  frameCount: number;
  /** 持续时间 */
  duration: number;
  /** 骨骼动画数据 */
  boneAnimations: Map<string, BIPBoneAnimation>;
  /** 元数据 */
  metadata: {
    version?: string;
    creator?: string;
    description?: string;
    tags?: string[];
  };
}

/**
 * BIP骨骼动画
 */
export interface BIPBoneAnimation {
  /** 骨骼名称 */
  boneName: string;
  /** 位置关键帧 */
  positionKeys: BIPKeyframe<THREE.Vector3>[];
  /** 旋转关键帧 */
  rotationKeys: BIPKeyframe<THREE.Quaternion>[];
  /** 缩放关键帧 */
  scaleKeys: BIPKeyframe<THREE.Vector3>[];
}

/**
 * BIP关键帧
 */
export interface BIPKeyframe<T> {
  /** 时间 */
  time: number;
  /** 值 */
  value: T;
  /** 插值类型 */
  interpolation: 'linear' | 'cubic' | 'step';
  /** 切线（用于三次插值） */
  inTangent?: T;
  /** 切线（用于三次插值） */
  outTangent?: T;
}

/**
 * 动画重定向映射
 */
export interface AnimationRetargetMapping {
  /** 源骨骼到目标骨骼的映射 */
  boneMapping: Map<string, StandardBoneType>;
  /** 位置缩放因子 */
  positionScale: number;
  /** 旋转偏移 */
  rotationOffset: Map<StandardBoneType, THREE.Quaternion>;
  /** 是否保留根运动 */
  preserveRootMotion: boolean;
}

/**
 * BIP动画加载器配置
 */
export interface BIPAnimationLoaderConfig {
  /** 是否自动重定向 */
  autoRetarget?: boolean;
  /** 默认帧率 */
  defaultFrameRate?: number;
  /** 是否压缩动画 */
  compressAnimation?: boolean;
  /** 压缩精度 */
  compressionPrecision?: number;
  /** 是否优化关键帧 */
  optimizeKeyframes?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * BIP动画加载器
 */
export class BIPAnimationLoader extends EventEmitter {
  /** 配置 */
  private config: BIPAnimationLoaderConfig;

  /** 重定向映射缓存 */
  private retargetMappings: Map<string, AnimationRetargetMapping> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: BIPAnimationLoaderConfig = {}) {
    super();

    this.config = {
      autoRetarget: true,
      defaultFrameRate: 30,
      compressAnimation: true,
      compressionPrecision: 0.001,
      optimizeKeyframes: true,
      debug: false,
      ...config
    };
  }

  /**
   * 加载BIP动画文件
   * @param fileData 文件数据
   * @param fileName 文件名
   * @returns Promise<BIPAnimationData>
   */
  public async loadBIPAnimation(fileData: ArrayBuffer | string, fileName: string): Promise<BIPAnimationData> {
    try {
      if (this.config.debug) {
        console.log('[BIPAnimationLoader] 开始加载BIP动画文件', fileName);
      }

      this.emit('loadStarted', fileName);

      // 解析BIP动画文件
      const animationData = this.parseBIPAnimationFile(fileData, fileName);

      // 优化关键帧
      if (this.config.optimizeKeyframes) {
        this.optimizeKeyframes(animationData);
      }

      // 压缩动画
      if (this.config.compressAnimation) {
        this.compressAnimation(animationData);
      }

      this.emit('loadCompleted', animationData);

      if (this.config.debug) {
        console.log('[BIPAnimationLoader] BIP动画加载完成', animationData.name);
      }

      return animationData;
    } catch (error) {
      this.emit('loadError', error);
      throw error;
    }
  }

  /**
   * 转换为标准动画剪辑
   * @param bipAnimation BIP动画数据
   * @param skeletonData 骨骼数据
   * @param retargetMapping 重定向映射
   * @returns AnimationClip
   */
  public convertToAnimationClip(
    bipAnimation: BIPAnimationData,
    skeletonData: BIPSkeletonData,
    retargetMapping?: AnimationRetargetMapping
  ): AnimationClip {
    try {
      if (this.config.debug) {
        console.log('[BIPAnimationLoader] 开始转换动画剪辑', bipAnimation.name);
      }

      // 创建重定向映射（如果未提供）
      if (!retargetMapping && this.config.autoRetarget) {
        retargetMapping = this.createAutoRetargetMapping(skeletonData);
      }

      // 转换动画轨道
      const tracks: AnimationTrack[] = [];
      
      for (const [boneName, boneAnimation] of bipAnimation.boneAnimations) {
        const targetBone = retargetMapping?.boneMapping.get(boneName);
        if (!targetBone) {
          if (this.config.debug) {
            console.warn('[BIPAnimationLoader] 跳过未映射的骨骼', boneName);
          }
          continue;
        }

        // 转换关键帧
        const keyframes = this.convertKeyframes(
          boneAnimation,
          retargetMapping,
          targetBone
        );

        if (keyframes.length > 0) {
          const track: AnimationTrack = {
            id: `${bipAnimation.name}_${targetBone}`,
            name: `${bipAnimation.name}_${targetBone}`,
            targetBone,
            keyframes,
            interpolation: 'linear',
            loop: true
          };

          tracks.push(track);
        }
      }

      // 创建动画剪辑
      const clip: AnimationClip = {
        id: `clip_${bipAnimation.name}_${Date.now()}`,
        name: bipAnimation.name,
        duration: bipAnimation.duration,
        frameRate: bipAnimation.frameRate,
        tracks,
        type: this.determineAnimationType(bipAnimation.name),
        priority: 1,
        blendable: true,
        weight: 1,
        metadata: {
          source: 'BIP',
          ...bipAnimation.metadata
        }
      };

      if (this.config.debug) {
        console.log('[BIPAnimationLoader] 动画剪辑转换完成', clip.name, `${tracks.length} 轨道`);
      }

      return clip;
    } catch (error) {
      if (this.config.debug) {
        console.error('[BIPAnimationLoader] 动画剪辑转换失败', error);
      }
      throw error;
    }
  }

  /**
   * 解析BIP动画文件
   * @param fileData 文件数据
   * @param fileName 文件名
   * @returns BIP动画数据
   */
  private parseBIPAnimationFile(fileData: ArrayBuffer | string, fileName: string): BIPAnimationData {
    // TODO: 实现实际的BIP动画文件解析
    // 这里提供一个简化的实现

    if (typeof fileData === 'string') {
      return this.parseTextBIPAnimation(fileData, fileName);
    } else {
      return this.parseBinaryBIPAnimation(fileData, fileName);
    }
  }

  /**
   * 解析文本格式BIP动画
   * @param textData 文本数据
   * @param fileName 文件名
   * @returns BIP动画数据
   */
  private parseTextBIPAnimation(textData: string, fileName: string): BIPAnimationData {
    const lines = textData.split('\n');
    const boneAnimations = new Map<string, BIPBoneAnimation>();
    
    let currentBone: string | null = null;
    let frameRate = this.config.defaultFrameRate!;
    let frameCount = 0;

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('FRAMERATE')) {
        frameRate = parseInt(trimmed.split(' ')[1]) || frameRate;
      } else if (trimmed.startsWith('FRAMES')) {
        frameCount = parseInt(trimmed.split(' ')[1]) || 0;
      } else if (trimmed.startsWith('BONE')) {
        currentBone = trimmed.split(' ')[1];
        if (currentBone && !boneAnimations.has(currentBone)) {
          boneAnimations.set(currentBone, {
            boneName: currentBone,
            positionKeys: [],
            rotationKeys: [],
            scaleKeys: []
          });
        }
      } else if (trimmed.startsWith('FRAME') && currentBone) {
        // 解析帧数据
        const parts = trimmed.split(' ');
        const frameIndex = parseInt(parts[1]);
        const time = frameIndex / frameRate;
        
        // 简化的数据解析
        if (parts.length >= 8) {
          const boneAnim = boneAnimations.get(currentBone)!;
          
          // 位置
          boneAnim.positionKeys.push({
            time,
            value: new THREE.Vector3(
              parseFloat(parts[2]),
              parseFloat(parts[3]),
              parseFloat(parts[4])
            ),
            interpolation: 'linear'
          });
          
          // 旋转（假设为欧拉角）
          const euler = new THREE.Euler(
            parseFloat(parts[5]) * Math.PI / 180,
            parseFloat(parts[6]) * Math.PI / 180,
            parseFloat(parts[7]) * Math.PI / 180
          );
          boneAnim.rotationKeys.push({
            time,
            value: new THREE.Quaternion().setFromEuler(euler),
            interpolation: 'linear'
          });
          
          // 缩放
          boneAnim.scaleKeys.push({
            time,
            value: new THREE.Vector3(1, 1, 1),
            interpolation: 'linear'
          });
        }
      }
    }

    return {
      name: fileName.replace(/\.[^/.]+$/, ''),
      frameRate,
      frameCount,
      duration: frameCount / frameRate,
      boneAnimations,
      metadata: {
        version: '1.0',
        description: `从 ${fileName} 加载的BIP动画`
      }
    };
  }

  /**
   * 解析二进制格式BIP动画
   * @param binaryData 二进制数据
   * @param fileName 文件名
   * @returns BIP动画数据
   */
  private parseBinaryBIPAnimation(binaryData: ArrayBuffer, fileName: string): BIPAnimationData {
    // TODO: 实现二进制BIP动画文件解析
    // 这里返回一个模拟的结果
    return {
      name: fileName.replace(/\.[^/.]+$/, ''),
      frameRate: this.config.defaultFrameRate!,
      frameCount: 100,
      duration: 100 / this.config.defaultFrameRate!,
      boneAnimations: new Map(),
      metadata: {
        version: '1.0',
        description: `从 ${fileName} 加载的二进制BIP动画`
      }
    };
  }

  /**
   * 创建自动重定向映射
   * @param skeletonData 骨骼数据
   * @returns 重定向映射
   */
  private createAutoRetargetMapping(skeletonData: BIPSkeletonData): AnimationRetargetMapping {
    const boneMapping = new Map<string, StandardBoneType>();
    
    // 基本的BIP到标准骨骼映射
    const basicMapping: Record<string, StandardBoneType> = {
      'Bip01': StandardBoneType.ROOT,
      'Bip01 Pelvis': StandardBoneType.HIPS,
      'Bip01 Spine': StandardBoneType.SPINE,
      'Bip01 Spine1': StandardBoneType.CHEST,
      'Bip01 Neck': StandardBoneType.NECK,
      'Bip01 Head': StandardBoneType.HEAD,
      'Bip01 L Clavicle': StandardBoneType.LEFT_SHOULDER,
      'Bip01 L UpperArm': StandardBoneType.LEFT_UPPER_ARM,
      'Bip01 L Forearm': StandardBoneType.LEFT_LOWER_ARM,
      'Bip01 L Hand': StandardBoneType.LEFT_HAND,
      'Bip01 R Clavicle': StandardBoneType.RIGHT_SHOULDER,
      'Bip01 R UpperArm': StandardBoneType.RIGHT_UPPER_ARM,
      'Bip01 R Forearm': StandardBoneType.RIGHT_LOWER_ARM,
      'Bip01 R Hand': StandardBoneType.RIGHT_HAND,
      'Bip01 L Thigh': StandardBoneType.LEFT_UPPER_LEG,
      'Bip01 L Calf': StandardBoneType.LEFT_LOWER_LEG,
      'Bip01 L Foot': StandardBoneType.LEFT_FOOT,
      'Bip01 R Thigh': StandardBoneType.RIGHT_UPPER_LEG,
      'Bip01 R Calf': StandardBoneType.RIGHT_LOWER_LEG,
      'Bip01 R Foot': StandardBoneType.RIGHT_FOOT
    };

    for (const [bipName, standardType] of Object.entries(basicMapping)) {
      boneMapping.set(bipName, standardType);
    }

    return {
      boneMapping,
      positionScale: 1.0,
      rotationOffset: new Map(),
      preserveRootMotion: true
    };
  }

  /**
   * 转换关键帧
   * @param boneAnimation 骨骼动画
   * @param retargetMapping 重定向映射
   * @param targetBone 目标骨骼
   * @returns 关键帧数组
   */
  private convertKeyframes(
    boneAnimation: BIPBoneAnimation,
    retargetMapping: AnimationRetargetMapping,
    targetBone: StandardBoneType
  ): AnimationKeyframe[] {
    const keyframes: AnimationKeyframe[] = [];
    
    // 获取所有时间点
    const timeSet = new Set<number>();
    boneAnimation.positionKeys.forEach(key => timeSet.add(key.time));
    boneAnimation.rotationKeys.forEach(key => timeSet.add(key.time));
    boneAnimation.scaleKeys.forEach(key => timeSet.add(key.time));
    
    const times = Array.from(timeSet).sort((a, b) => a - b);
    
    for (const time of times) {
      // 插值获取变换
      const position = this.interpolatePosition(boneAnimation.positionKeys, time);
      const rotation = this.interpolateRotation(boneAnimation.rotationKeys, time);
      const scale = this.interpolateScale(boneAnimation.scaleKeys, time);
      
      // 应用重定向
      const retargetedPosition = position.clone().multiplyScalar(retargetMapping.positionScale);
      const retargetedRotation = rotation.clone();
      
      // 应用旋转偏移
      const rotationOffset = retargetMapping.rotationOffset.get(targetBone);
      if (rotationOffset) {
        retargetedRotation.multiply(rotationOffset);
      }
      
      const transforms = new Map();
      transforms.set(targetBone, {
        position: retargetedPosition,
        rotation: retargetedRotation,
        scale: scale.clone()
      });
      
      keyframes.push({
        time,
        transforms
      });
    }
    
    return keyframes;
  }

  /**
   * 插值位置
   * @param keys 位置关键帧
   * @param time 时间
   * @returns 插值后的位置
   */
  private interpolatePosition(keys: BIPKeyframe<THREE.Vector3>[], time: number): THREE.Vector3 {
    if (keys.length === 0) return new THREE.Vector3();
    if (keys.length === 1) return keys[0].value.clone();
    
    // 查找相邻的关键帧
    let prevKey = keys[0];
    let nextKey = keys[keys.length - 1];
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (time >= keys[i].time && time <= keys[i + 1].time) {
        prevKey = keys[i];
        nextKey = keys[i + 1];
        break;
      }
    }
    
    if (prevKey === nextKey) return prevKey.value.clone();
    
    // 线性插值
    const t = (time - prevKey.time) / (nextKey.time - prevKey.time);
    return prevKey.value.clone().lerp(nextKey.value, t);
  }

  /**
   * 插值旋转
   * @param keys 旋转关键帧
   * @param time 时间
   * @returns 插值后的旋转
   */
  private interpolateRotation(keys: BIPKeyframe<THREE.Quaternion>[], time: number): THREE.Quaternion {
    if (keys.length === 0) return new THREE.Quaternion();
    if (keys.length === 1) return keys[0].value.clone();
    
    // 查找相邻的关键帧
    let prevKey = keys[0];
    let nextKey = keys[keys.length - 1];
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (time >= keys[i].time && time <= keys[i + 1].time) {
        prevKey = keys[i];
        nextKey = keys[i + 1];
        break;
      }
    }
    
    if (prevKey === nextKey) return prevKey.value.clone();
    
    // 球面线性插值
    const t = (time - prevKey.time) / (nextKey.time - prevKey.time);
    return prevKey.value.clone().slerp(nextKey.value, t);
  }

  /**
   * 插值缩放
   * @param keys 缩放关键帧
   * @param time 时间
   * @returns 插值后的缩放
   */
  private interpolateScale(keys: BIPKeyframe<THREE.Vector3>[], time: number): THREE.Vector3 {
    if (keys.length === 0) return new THREE.Vector3(1, 1, 1);
    if (keys.length === 1) return keys[0].value.clone();
    
    // 查找相邻的关键帧
    let prevKey = keys[0];
    let nextKey = keys[keys.length - 1];
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (time >= keys[i].time && time <= keys[i + 1].time) {
        prevKey = keys[i];
        nextKey = keys[i + 1];
        break;
      }
    }
    
    if (prevKey === nextKey) return prevKey.value.clone();
    
    // 线性插值
    const t = (time - prevKey.time) / (nextKey.time - prevKey.time);
    return prevKey.value.clone().lerp(nextKey.value, t);
  }

  /**
   * 优化关键帧
   * @param animationData 动画数据
   */
  private optimizeKeyframes(animationData: BIPAnimationData): void {
    // TODO: 实现关键帧优化算法
    // 移除冗余关键帧，减少数据量
  }

  /**
   * 压缩动画
   * @param animationData 动画数据
   */
  private compressAnimation(animationData: BIPAnimationData): void {
    // TODO: 实现动画压缩算法
    // 量化关键帧数据，减少精度但保持质量
  }

  /**
   * 确定动画类型
   * @param animationName 动画名称
   * @returns 动画类型
   */
  private determineAnimationType(animationName: string): 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom' {
    const name = animationName.toLowerCase();
    
    if (name.includes('idle') || name.includes('stand')) return 'idle';
    if (name.includes('walk')) return 'walk';
    if (name.includes('run')) return 'run';
    if (name.includes('gesture') || name.includes('wave') || name.includes('point')) return 'gesture';
    if (name.includes('expression') || name.includes('smile') || name.includes('frown')) return 'expression';
    
    return 'custom';
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    this.retargetMappings.clear();
    this.removeAllListeners();
    
    if (this.config.debug) {
      console.log('[BIPAnimationLoader] 加载器已销毁');
    }
  }
}
