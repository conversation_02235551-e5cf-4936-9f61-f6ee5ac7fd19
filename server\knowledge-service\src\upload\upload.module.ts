import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KnowledgeBase, KnowledgeDocument } from '../entities';
import { StorageModule } from '../storage/storage.module';
import { CacheModule } from '../cache/cache.module';
import { UploadService } from './upload.service';
import { UploadController } from './upload.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeBase, KnowledgeDocument]),
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        limits: {
          fileSize: configService.get('production.documentProcessing.maxFileSize'),
          files: 10, // 最多同时上传10个文件
        },
        fileFilter: (req, file, callback) => {
          const allowedTypes = configService.get(
            'production.documentProcessing.allowedMimeTypes',
          );
          
          if (allowedTypes.includes(file.mimetype)) {
            callback(null, true);
          } else {
            callback(new Error('不支持的文件类型'), false);
          }
        },
      }),
      inject: [ConfigService],
    }),
    StorageModule,
    CacheModule,
  ],
  controllers: [UploadController],
  providers: [UploadService],
  exports: [UploadService],
})
export class UploadModule {}
