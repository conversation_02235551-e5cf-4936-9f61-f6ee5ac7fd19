/**
 * BIP骨骼上传面板
 * 用于上传和管理BIP骨骼文件
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { BIPIntegrationSystem, BIPImportResult } from '../../avatar';

/**
 * 面板配置
 */
export interface BIPUploadPanelConfig {
  /** 容器元素 */
  container: HTMLElement;
  /** 是否允许多文件上传 */
  allowMultiple?: boolean;
  /** 最大文件大小 (MB) */
  maxFileSize?: number;
  /** 是否显示预览 */
  showPreview?: boolean;
}

/**
 * 上传状态
 */
export enum UploadStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 文件上传项
 */
export interface UploadItem {
  /** 文件 */
  file: File;
  /** 状态 */
  status: UploadStatus;
  /** 进度 */
  progress: number;
  /** 结果 */
  result?: BIPImportResult;
  /** 错误信息 */
  error?: string;
}

/**
 * BIP骨骼上传面板
 */
export class BIPUploadPanel extends EventEmitter {
  /** 配置 */
  private config: BIPUploadPanelConfig;

  /** 容器元素 */
  private container: HTMLElement;

  /** BIP集成系统 */
  private bipSystem: BIPIntegrationSystem | null = null;

  /** 上传项列表 */
  private uploadItems: Map<string, UploadItem> = new Map();

  /** UI元素 */
  private elements: {
    dropZone?: HTMLElement;
    fileList?: HTMLElement;
    uploadButton?: HTMLElement;
    clearButton?: HTMLElement;
    progressBar?: HTMLElement;
  } = {};

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: BIPUploadPanelConfig) {
    super();

    this.config = {
      allowMultiple: true,
      maxFileSize: 50, // 50MB
      showPreview: true,
      ...config
    };

    this.container = config.container;
    this.initializeUI();
    this.setupEventListeners();
  }

  /**
   * 设置BIP集成系统
   * @param system BIP集成系统
   */
  public setBIPSystem(system: BIPIntegrationSystem): void {
    this.bipSystem = system;
    this.setupBIPSystemEvents();
  }

  /**
   * 初始化UI
   */
  private initializeUI(): void {
    this.container.className = 'bip-upload-panel';
    this.container.innerHTML = `
      <div class="panel-header">
        <h3>BIP骨骼上传</h3>
        <div class="upload-stats">
          <span class="file-count">0 个文件</span>
          <span class="total-size">0 MB</span>
        </div>
      </div>

      <div class="drop-zone" id="drop-zone">
        <div class="drop-zone-content">
          <i class="icon-upload-cloud"></i>
          <h4>拖拽BIP文件到此处</h4>
          <p>或点击选择文件上传</p>
          <button class="btn btn-primary" id="select-files-btn">
            <i class="icon-folder"></i>
            选择文件
          </button>
          <input type="file" id="file-input" 
                 ${this.config.allowMultiple ? 'multiple' : ''} 
                 accept=".bip" 
                 style="display: none;">
        </div>
        <div class="drop-zone-overlay" style="display: none;">
          <div class="overlay-content">
            <i class="icon-upload"></i>
            <p>释放文件开始上传</p>
          </div>
        </div>
      </div>

      <div class="file-list" id="file-list" style="display: none;">
        <div class="list-header">
          <h4>上传列表</h4>
          <div class="list-actions">
            <button class="btn btn-success" id="upload-btn" disabled>
              <i class="icon-upload"></i>
              开始上传
            </button>
            <button class="btn btn-secondary" id="clear-btn">
              <i class="icon-clear"></i>
              清空列表
            </button>
          </div>
        </div>
        <div class="list-content" id="list-content">
          <!-- 文件项将在这里动态生成 -->
        </div>
      </div>

      <div class="upload-progress" id="upload-progress" style="display: none;">
        <div class="progress-header">
          <span class="progress-title">正在上传...</span>
          <span class="progress-percentage">0%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-details">
          <span class="current-file">准备中...</span>
          <span class="file-progress">0/0</span>
        </div>
      </div>

      <div class="upload-results" id="upload-results" style="display: none;">
        <div class="results-header">
          <h4>上传结果</h4>
          <button class="btn btn-outline" id="close-results-btn">
            <i class="icon-close"></i>
            关闭
          </button>
        </div>
        <div class="results-content" id="results-content">
          <!-- 结果将在这里显示 -->
        </div>
      </div>
    `;

    // 获取UI元素引用
    this.elements.dropZone = this.container.querySelector('#drop-zone') as HTMLElement;
    this.elements.fileList = this.container.querySelector('#file-list') as HTMLElement;
    this.elements.uploadButton = this.container.querySelector('#upload-btn') as HTMLElement;
    this.elements.clearButton = this.container.querySelector('#clear-btn') as HTMLElement;
    this.elements.progressBar = this.container.querySelector('#upload-progress') as HTMLElement;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 文件选择
    const fileInput = this.container.querySelector('#file-input') as HTMLInputElement;
    const selectBtn = this.container.querySelector('#select-files-btn') as HTMLButtonElement;
    
    selectBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', (e) => this.handleFileSelection(e));

    // 拖拽上传
    const dropZone = this.elements.dropZone!;
    dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
    dropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
    dropZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
    dropZone.addEventListener('dragenter', (e) => this.handleDragEnter(e));

    // 上传按钮
    this.elements.uploadButton!.addEventListener('click', () => this.startUpload());

    // 清空按钮
    this.elements.clearButton!.addEventListener('click', () => this.clearFileList());

    // 关闭结果按钮
    const closeResultsBtn = this.container.querySelector('#close-results-btn') as HTMLButtonElement;
    closeResultsBtn.addEventListener('click', () => this.hideResults());
  }

  /**
   * 设置BIP系统事件
   */
  private setupBIPSystemEvents(): void {
    if (!this.bipSystem) return;

    // 监听导入完成
    this.bipSystem.on('bipImportCompleted', (entity: any, result: BIPImportResult) => {
      this.handleImportCompleted(result);
    });

    // 监听导入错误
    this.bipSystem.on('bipImportError', (entity: any, error: any) => {
      this.handleImportError(error);
    });
  }

  /**
   * 处理文件选择
   * @param event 文件选择事件
   */
  private handleFileSelection(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.addFiles(Array.from(input.files));
    }
  }

  /**
   * 处理拖拽进入
   * @param event 拖拽事件
   */
  private handleDragEnter(event: DragEvent): void {
    event.preventDefault();
    this.showDropOverlay();
  }

  /**
   * 处理拖拽悬停
   * @param event 拖拽事件
   */
  private handleDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  /**
   * 处理文件拖拽放下
   * @param event 拖拽事件
   */
  private handleFileDrop(event: DragEvent): void {
    event.preventDefault();
    this.hideDropOverlay();

    const files = Array.from(event.dataTransfer?.files || []);
    const bipFiles = files.filter(file => this.isValidBIPFile(file));

    if (bipFiles.length > 0) {
      this.addFiles(bipFiles);
    } else {
      this.showError('请选择有效的BIP文件');
    }
  }

  /**
   * 处理拖拽离开
   * @param event 拖拽事件
   */
  private handleDragLeave(event: DragEvent): void {
    // 只有当离开整个拖拽区域时才隐藏覆盖层
    if (!this.elements.dropZone!.contains(event.relatedTarget as Node)) {
      this.hideDropOverlay();
    }
  }

  /**
   * 验证BIP文件
   * @param file 文件
   * @returns 是否有效
   */
  private isValidBIPFile(file: File): boolean {
    // 检查文件扩展名
    if (!file.name.toLowerCase().endsWith('.bip')) {
      return false;
    }

    // 检查文件大小
    const maxSize = (this.config.maxFileSize || 50) * 1024 * 1024;
    if (file.size > maxSize) {
      return false;
    }

    return true;
  }

  /**
   * 添加文件到上传列表
   * @param files 文件列表
   */
  private addFiles(files: File[]): void {
    let addedCount = 0;

    for (const file of files) {
      if (!this.isValidBIPFile(file)) {
        this.showError(`文件 "${file.name}" 不是有效的BIP文件或超过大小限制`);
        continue;
      }

      // 检查是否已存在
      const fileId = this.generateFileId(file);
      if (this.uploadItems.has(fileId)) {
        continue;
      }

      // 添加到上传列表
      const uploadItem: UploadItem = {
        file,
        status: UploadStatus.IDLE,
        progress: 0
      };

      this.uploadItems.set(fileId, uploadItem);
      addedCount++;
    }

    if (addedCount > 0) {
      this.updateFileList();
      this.updateUploadStats();
      this.showFileList();
    }
  }

  /**
   * 生成文件ID
   * @param file 文件
   * @returns 文件ID
   */
  private generateFileId(file: File): string {
    return `${file.name}_${file.size}_${file.lastModified}`;
  }

  /**
   * 更新文件列表显示
   */
  private updateFileList(): void {
    const listContent = this.container.querySelector('#list-content') as HTMLElement;
    
    const html = Array.from(this.uploadItems.entries())
      .map(([fileId, item]) => this.renderFileItem(fileId, item))
      .join('');

    listContent.innerHTML = html;
    this.setupFileItemEvents();
  }

  /**
   * 渲染文件项
   * @param fileId 文件ID
   * @param item 上传项
   * @returns HTML字符串
   */
  private renderFileItem(fileId: string, item: UploadItem): string {
    const statusClass = item.status.toLowerCase();
    const sizeText = this.formatFileSize(item.file.size);
    const statusText = this.getStatusText(item.status);

    return `
      <div class="file-item ${statusClass}" data-file-id="${fileId}">
        <div class="file-icon">
          <i class="icon-file-bip"></i>
        </div>
        <div class="file-info">
          <div class="file-name">${item.file.name}</div>
          <div class="file-details">
            <span class="file-size">${sizeText}</span>
            <span class="file-status">${statusText}</span>
          </div>
          ${item.status === UploadStatus.UPLOADING || item.status === UploadStatus.PROCESSING ? 
            `<div class="file-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${item.progress}%"></div>
              </div>
              <span class="progress-text">${item.progress}%</span>
            </div>` : ''}
          ${item.error ? `<div class="file-error">${item.error}</div>` : ''}
        </div>
        <div class="file-actions">
          ${item.status === UploadStatus.IDLE ? 
            `<button class="btn-icon remove-btn" title="移除">
              <i class="icon-close"></i>
            </button>` : ''}
          ${item.status === UploadStatus.SUCCESS && item.result ? 
            `<button class="btn-icon info-btn" title="查看详情">
              <i class="icon-info"></i>
            </button>` : ''}
        </div>
      </div>
    `;
  }

  /**
   * 设置文件项事件
   */
  private setupFileItemEvents(): void {
    const fileItems = this.container.querySelectorAll('.file-item');

    fileItems.forEach(item => {
      const fileId = item.getAttribute('data-file-id')!;
      
      // 移除按钮
      const removeBtn = item.querySelector('.remove-btn') as HTMLButtonElement;
      if (removeBtn) {
        removeBtn.addEventListener('click', () => this.removeFile(fileId));
      }

      // 详情按钮
      const infoBtn = item.querySelector('.info-btn') as HTMLButtonElement;
      if (infoBtn) {
        infoBtn.addEventListener('click', () => this.showFileDetails(fileId));
      }
    });
  }

  /**
   * 移除文件
   * @param fileId 文件ID
   */
  private removeFile(fileId: string): void {
    this.uploadItems.delete(fileId);
    this.updateFileList();
    this.updateUploadStats();

    if (this.uploadItems.size === 0) {
      this.hideFileList();
    }
  }

  /**
   * 显示文件详情
   * @param fileId 文件ID
   */
  private showFileDetails(fileId: string): void {
    const item = this.uploadItems.get(fileId);
    if (!item || !item.result) return;

    // TODO: 实现详情对话框
    console.log('文件详情:', item.result);
  }

  /**
   * 开始上传
   */
  private async startUpload(): Promise<void> {
    if (!this.bipSystem) {
      this.showError('BIP集成系统未初始化');
      return;
    }

    const idleItems = Array.from(this.uploadItems.entries())
      .filter(([_, item]) => item.status === UploadStatus.IDLE);

    if (idleItems.length === 0) {
      return;
    }

    this.showProgress();
    this.elements.uploadButton!.disabled = true;

    let completedCount = 0;
    const totalCount = idleItems.length;

    for (const [fileId, item] of idleItems) {
      try {
        // 更新状态为上传中
        item.status = UploadStatus.UPLOADING;
        item.progress = 0;
        this.updateFileItemStatus(fileId, item);

        // 模拟上传进度
        await this.simulateUploadProgress(fileId, item);

        // 开始处理
        item.status = UploadStatus.PROCESSING;
        item.progress = 100;
        this.updateFileItemStatus(fileId, item);

        // 导入BIP文件
        const result = await this.bipSystem.importBIPSkeleton(item.file, null as any);
        
        item.result = result;
        item.status = result.success ? UploadStatus.SUCCESS : UploadStatus.ERROR;
        if (!result.success) {
          item.error = result.error;
        }

      } catch (error) {
        item.status = UploadStatus.ERROR;
        item.error = error.message;
      }

      this.updateFileItemStatus(fileId, item);
      completedCount++;
      this.updateOverallProgress(completedCount, totalCount);
    }

    this.hideProgress();
    this.elements.uploadButton!.disabled = false;
    this.showResults();
  }

  /**
   * 模拟上传进度
   * @param fileId 文件ID
   * @param item 上传项
   */
  private async simulateUploadProgress(fileId: string, item: UploadItem): Promise<void> {
    return new Promise(resolve => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          resolve();
        }
        
        item.progress = Math.round(progress);
        this.updateFileItemStatus(fileId, item);
      }, 100);
    });
  }

  /**
   * 更新文件项状态
   * @param fileId 文件ID
   * @param item 上传项
   */
  private updateFileItemStatus(fileId: string, item: UploadItem): void {
    const fileItem = this.container.querySelector(`[data-file-id="${fileId}"]`) as HTMLElement;
    if (!fileItem) return;

    // 更新状态类
    fileItem.className = `file-item ${item.status.toLowerCase()}`;

    // 更新状态文本
    const statusElement = fileItem.querySelector('.file-status') as HTMLElement;
    statusElement.textContent = this.getStatusText(item.status);

    // 更新进度条
    const progressFill = fileItem.querySelector('.progress-fill') as HTMLElement;
    const progressText = fileItem.querySelector('.progress-text') as HTMLElement;
    
    if (progressFill && progressText) {
      progressFill.style.width = `${item.progress}%`;
      progressText.textContent = `${item.progress}%`;
    }

    // 更新错误信息
    const errorElement = fileItem.querySelector('.file-error') as HTMLElement;
    if (errorElement && item.error) {
      errorElement.textContent = item.error;
    }
  }

  /**
   * 获取状态文本
   * @param status 状态
   * @returns 状态文本
   */
  private getStatusText(status: UploadStatus): string {
    switch (status) {
      case UploadStatus.IDLE:
        return '等待上传';
      case UploadStatus.UPLOADING:
        return '上传中';
      case UploadStatus.PROCESSING:
        return '处理中';
      case UploadStatus.SUCCESS:
        return '成功';
      case UploadStatus.ERROR:
        return '失败';
      default:
        return '未知';
    }
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化的大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 更新上传统计
   */
  private updateUploadStats(): void {
    const fileCount = this.uploadItems.size;
    const totalSize = Array.from(this.uploadItems.values())
      .reduce((sum, item) => sum + item.file.size, 0);

    const fileCountElement = this.container.querySelector('.file-count') as HTMLElement;
    const totalSizeElement = this.container.querySelector('.total-size') as HTMLElement;

    fileCountElement.textContent = `${fileCount} 个文件`;
    totalSizeElement.textContent = this.formatFileSize(totalSize);

    // 更新上传按钮状态
    const hasIdleFiles = Array.from(this.uploadItems.values())
      .some(item => item.status === UploadStatus.IDLE);
    
    this.elements.uploadButton!.disabled = !hasIdleFiles;
  }

  /**
   * 显示拖拽覆盖层
   */
  private showDropOverlay(): void {
    const overlay = this.elements.dropZone!.querySelector('.drop-zone-overlay') as HTMLElement;
    overlay.style.display = 'flex';
  }

  /**
   * 隐藏拖拽覆盖层
   */
  private hideDropOverlay(): void {
    const overlay = this.elements.dropZone!.querySelector('.drop-zone-overlay') as HTMLElement;
    overlay.style.display = 'none';
  }

  /**
   * 显示文件列表
   */
  private showFileList(): void {
    this.elements.fileList!.style.display = 'block';
  }

  /**
   * 隐藏文件列表
   */
  private hideFileList(): void {
    this.elements.fileList!.style.display = 'none';
  }

  /**
   * 清空文件列表
   */
  private clearFileList(): void {
    this.uploadItems.clear();
    this.updateFileList();
    this.updateUploadStats();
    this.hideFileList();
  }

  /**
   * 显示进度
   */
  private showProgress(): void {
    this.elements.progressBar!.style.display = 'block';
  }

  /**
   * 隐藏进度
   */
  private hideProgress(): void {
    this.elements.progressBar!.style.display = 'none';
  }

  /**
   * 更新整体进度
   * @param completed 已完成数量
   * @param total 总数量
   */
  private updateOverallProgress(completed: number, total: number): void {
    const percentage = Math.round((completed / total) * 100);
    
    const progressFill = this.container.querySelector('#progress-fill') as HTMLElement;
    const progressPercentage = this.container.querySelector('.progress-percentage') as HTMLElement;
    const fileProgress = this.container.querySelector('.file-progress') as HTMLElement;

    progressFill.style.width = `${percentage}%`;
    progressPercentage.textContent = `${percentage}%`;
    fileProgress.textContent = `${completed}/${total}`;
  }

  /**
   * 显示结果
   */
  private showResults(): void {
    const resultsPanel = this.container.querySelector('#upload-results') as HTMLElement;
    const resultsContent = this.container.querySelector('#results-content') as HTMLElement;

    const successCount = Array.from(this.uploadItems.values())
      .filter(item => item.status === UploadStatus.SUCCESS).length;
    const errorCount = Array.from(this.uploadItems.values())
      .filter(item => item.status === UploadStatus.ERROR).length;

    resultsContent.innerHTML = `
      <div class="results-summary">
        <div class="result-item success">
          <i class="icon-check"></i>
          <span>成功: ${successCount}</span>
        </div>
        <div class="result-item error">
          <i class="icon-error"></i>
          <span>失败: ${errorCount}</span>
        </div>
      </div>
    `;

    resultsPanel.style.display = 'block';
  }

  /**
   * 隐藏结果
   */
  private hideResults(): void {
    const resultsPanel = this.container.querySelector('#upload-results') as HTMLElement;
    resultsPanel.style.display = 'none';
  }

  /**
   * 显示错误
   * @param message 错误消息
   */
  private showError(message: string): void {
    // TODO: 实现错误提示
    console.error(message);
  }

  /**
   * 处理导入完成
   * @param result 导入结果
   */
  private handleImportCompleted(result: BIPImportResult): void {
    this.emit('importCompleted', result);
  }

  /**
   * 处理导入错误
   * @param error 错误
   */
  private handleImportError(error: any): void {
    this.emit('importError', error);
  }

  /**
   * 销毁面板
   */
  public dispose(): void {
    this.uploadItems.clear();
    this.removeAllListeners();
  }
}
