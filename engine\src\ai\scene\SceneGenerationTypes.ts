/**
 * 场景生成相关类型定义
 */
import * as THREE from 'three';

/**
 * 场景元素类型
 */
export enum SceneElementType {
  OBJECT = 'object',
  ENVIRONMENT = 'environment',
  LIGHTING = 'lighting',
  CAMERA = 'camera',
  EFFECT = 'effect'
}

/**
 * 场景元素
 */
export interface SceneElement {
  /** 元素ID */
  id?: string;
  /** 元素类型 */
  type: SceneElementType;
  /** 元素名称 */
  name: string;
  /** 元素分类 */
  category: string;
  /** 元素描述 */
  description?: string;
  /** 元素属性 */
  attributes: Record<string, any>;
  /** 位置信息 */
  position?: THREE.Vector3;
  /** 旋转信息 */
  rotation?: THREE.Euler;
  /** 缩放信息 */
  scale?: THREE.Vector3;
  /** 所需尺寸 */
  requiredSize?: THREE.Vector3;
  /** 材质提示 */
  materialHint?: string;
}

/**
 * 空间关系类型
 */
export enum SpatialRelationType {
  ABOVE = 'above',
  BELOW = 'below',
  LEFT = 'left',
  RIGHT = 'right',
  FRONT = 'front',
  BACK = 'back',
  INSIDE = 'inside',
  OUTSIDE = 'outside',
  NEAR = 'near',
  FAR = 'far',
  ADJACENT = 'adjacent',
  OPPOSITE = 'opposite'
}

/**
 * 空间关系
 */
export interface SpatialRelation {
  /** 关系类型 */
  type: SpatialRelationType;
  /** 源对象 */
  source: string;
  /** 目标对象 */
  target: string;
  /** 距离（可选） */
  distance?: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 场景意图
 */
export interface SceneIntent {
  /** 场景类型 */
  sceneType: string;
  /** 场景风格 */
  style: string;
  /** 功能需求 */
  functionality: string[];
  /** 情感色调 */
  mood?: string;
  /** 时间设定 */
  timeOfDay?: string;
  /** 天气设定 */
  weather?: string;
}

/**
 * 约束条件
 */
export interface SceneConstraint {
  /** 约束类型 */
  type: 'physical' | 'aesthetic' | 'functional' | 'spatial';
  /** 约束描述 */
  description: string;
  /** 约束参数 */
  parameters: Record<string, any>;
  /** 约束优先级 */
  priority: number;
}

/**
 * 场景理解结果
 */
export interface SceneUnderstanding {
  /** 场景元素 */
  elements: SceneElement[];
  /** 空间关系 */
  spatialRelations: SpatialRelation[];
  /** 场景意图 */
  intent: SceneIntent;
  /** 约束条件 */
  constraints: SceneConstraint[];
  /** 置信度 */
  confidence: number;
}

/**
 * 场景布局
 */
export interface SceneLayout {
  /** 布局ID */
  id: string;
  /** 场景边界 */
  bounds: THREE.Box3;
  /** 布局元素 */
  elements: LayoutElement[];
  /** 环境配置 */
  environment: EnvironmentConfig;
  /** 布局评分 */
  score?: number;
}

/**
 * 布局元素
 */
export interface LayoutElement {
  /** 元素引用 */
  element: SceneElement;
  /** 位置 */
  position: THREE.Vector3;
  /** 旋转 */
  rotation: THREE.Euler;
  /** 缩放 */
  scale: THREE.Vector3;
  /** 边界框 */
  boundingBox: THREE.Box3;
}

/**
 * 环境配置
 */
export interface EnvironmentConfig {
  /** 天空盒 */
  skybox?: string;
  /** 环境光颜色 */
  ambientColor: THREE.Color;
  /** 环境光强度 */
  ambientIntensity: number;
  /** 主光源配置 */
  mainLight?: {
    type: 'directional' | 'point' | 'spot';
    color: THREE.Color;
    intensity: number;
    position: THREE.Vector3;
    direction?: THREE.Vector3;
  };
  /** 后处理效果 */
  postProcessing?: Record<string, any>;
}

/**
 * 资产匹配结果
 */
export interface AssetMatchResult {
  /** 场景元素 */
  element: SceneElement;
  /** 匹配的资产 */
  asset: any; // 这里应该是具体的资产类型
  /** 匹配置信度 */
  confidence: number;
  /** 资产来源 */
  source: 'database' | 'generated' | 'procedural';
}

/**
 * 场景生成选项
 */
export interface SceneGenerationOptions {
  /** 实时预览 */
  realTimePreview?: boolean;
  /** 语音指导 */
  voiceGuidance?: boolean;
  /** 风格偏好 */
  stylePreference?: string;
  /** 复杂度级别 */
  complexityLevel?: 'simple' | 'medium' | 'complex';
  /** 性能优先级 */
  performancePriority?: 'quality' | 'speed' | 'balanced';
}

/**
 * 场景生成结果
 */
export interface SceneGenerationResult {
  /** 生成的场景 */
  scene: any; // 这里应该是具体的场景类型
  /** 场景理解 */
  understanding: SceneUnderstanding;
  /** 场景布局 */
  layout: SceneLayout;
  /** 资产匹配结果 */
  assets: AssetMatchResult[];
  /** 总体置信度 */
  confidence: number;
  /** 生成时间 */
  generationTime?: number;
}

/**
 * 场景需求分析
 */
export interface SceneRequirements {
  /** 场景类型 */
  sceneType: string;
  /** 场景元素 */
  elements: SceneElement[];
  /** 空间关系 */
  spatialRelations: SpatialRelation[];
  /** 约束条件 */
  constraints: SceneConstraint[];
  /** 风格要求 */
  style: string;
  /** 场景规模 */
  scale: 'small' | 'medium' | 'large';
}

/**
 * NLP处理结果
 */
export interface NLPResult {
  /** 分词结果 */
  tokens: string[];
  /** 词性标注 */
  posTags: Array<{ word: string; tag: string }>;
  /** 命名实体识别 */
  entities: Array<{ text: string; label: string; start: number; end: number }>;
  /** 依存关系 */
  dependencies?: Array<{ head: number; dep: string; tail: number }>;
  /** 情感分析 */
  sentiment?: { label: string; score: number };
}
