import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
  Sse,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { RAGService, RAGQuery, RAGResponse } from './rag.service';

@ApiTags('RAG查询引擎')
@Controller('api/rag')
export class RAGController {
  constructor(private readonly ragService: RAGService) {}

  @Post('query')
  @ApiOperation({ summary: '执行RAG查询' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        question: {
          type: 'string',
          description: '用户问题',
          example: '什么是人工智能？',
        },
        digitalHumanId: {
          type: 'string',
          description: '数字人ID',
        },
        sessionId: {
          type: 'string',
          description: '会话ID（可选）',
        },
        context: {
          type: 'string',
          description: '额外上下文（可选）',
        },
        maxResults: {
          type: 'number',
          description: '最大结果数量',
          default: 10,
        },
        temperature: {
          type: 'number',
          description: 'LLM温度参数',
          default: 0.7,
        },
        language: {
          type: 'string',
          description: '回答语言',
          default: 'zh-CN',
        },
      },
      required: ['question', 'digitalHumanId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'RAG查询结果',
    schema: {
      type: 'object',
      properties: {
        answer: {
          type: 'string',
          description: '生成的回答',
        },
        sources: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              score: { type: 'number' },
              source: {
                type: 'object',
                properties: {
                  documentId: { type: 'string' },
                  filename: { type: 'string' },
                  knowledgeBaseId: { type: 'string' },
                  knowledgeBaseName: { type: 'string' },
                  chunkIndex: { type: 'number' },
                },
              },
            },
          },
        },
        confidence: {
          type: 'number',
          description: '置信度',
        },
        responseTime: {
          type: 'number',
          description: '响应时间（毫秒）',
        },
        sessionId: {
          type: 'string',
          description: '会话ID',
        },
        metadata: {
          type: 'object',
          properties: {
            knowledgeBasesUsed: {
              type: 'array',
              items: { type: 'string' },
            },
            totalChunks: { type: 'number' },
            llmModel: { type: 'string' },
            processingSteps: {
              type: 'array',
              items: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async query(@Body() body: RAGQuery): Promise<RAGResponse> {
    try {
      // 验证必需参数
      if (!body.question || !body.digitalHumanId) {
        throw new HttpException(
          '问题和数字人ID是必需的',
          HttpStatus.BAD_REQUEST,
        );
      }

      // 验证问题长度
      if (body.question.length > 1000) {
        throw new HttpException(
          '问题长度不能超过1000字符',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.ragService.query(body);
    } catch (error) {
      throw new HttpException(
        error.message || 'RAG查询失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('query/stream')
  @ApiOperation({ summary: '流式RAG查询' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        question: { type: 'string' },
        digitalHumanId: { type: 'string' },
        sessionId: { type: 'string' },
        context: { type: 'string' },
        maxResults: { type: 'number' },
        temperature: { type: 'number' },
        language: { type: 'string' },
      },
      required: ['question', 'digitalHumanId'],
    },
  })
  @Sse('stream')
  async streamQuery(@Body() body: RAGQuery): Promise<Observable<any>> {
    try {
      if (!body.question || !body.digitalHumanId) {
        throw new HttpException(
          '问题和数字人ID是必需的',
          HttpStatus.BAD_REQUEST,
        );
      }

      return new Observable(observer => {
        this.ragService.queryStream(body)
          .then(async (stream) => {
            for await (const chunk of stream) {
              observer.next({
                data: JSON.stringify({
                  type: 'chunk',
                  content: chunk,
                }),
              });
            }
            observer.complete();
          })
          .catch(error => {
            observer.error(error);
          });
      });
    } catch (error) {
      throw new HttpException(
        error.message || '流式RAG查询失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('digital-humans/:id/knowledge-bases')
  @ApiOperation({ summary: '获取数字人绑定的知识库' })
  @ApiParam({ name: 'id', description: '数字人ID' })
  @ApiResponse({
    status: 200,
    description: '知识库列表',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          bindingId: { type: 'string' },
          bindingType: { type: 'string' },
          priority: { type: 'number' },
          knowledgeBase: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              language: { type: 'string' },
              documentCount: { type: 'number' },
              totalChunks: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getDigitalHumanKnowledgeBases(
    @Param('id') digitalHumanId: string,
  ): Promise<any> {
    try {
      return await this.ragService.getDigitalHumanKnowledgeBases(digitalHumanId);
    } catch (error) {
      throw new HttpException(
        error.message || '获取知识库列表失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('search')
  @ApiOperation({ summary: '向量搜索（不生成回答）' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        digitalHumanId: { type: 'string' },
        maxResults: { type: 'number' },
        threshold: { type: 'number' },
      },
      required: ['query', 'digitalHumanId'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '搜索结果',
    schema: {
      type: 'object',
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              score: { type: 'number' },
              source: {
                type: 'object',
                properties: {
                  documentId: { type: 'string' },
                  filename: { type: 'string' },
                  knowledgeBaseId: { type: 'string' },
                  knowledgeBaseName: { type: 'string' },
                  chunkIndex: { type: 'number' },
                },
              },
            },
          },
        },
        searchTime: { type: 'number' },
        totalResults: { type: 'number' },
      },
    },
  })
  async search(@Body() body: any): Promise<any> {
    try {
      if (!body.query || !body.digitalHumanId) {
        throw new HttpException(
          '查询和数字人ID是必需的',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.ragService.searchOnly({
        question: body.query,
        digitalHumanId: body.digitalHumanId,
        maxResults: body.maxResults || 10,
        threshold: body.threshold,
      });
    } catch (error) {
      throw new HttpException(
        error.message || '搜索失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('sessions/:sessionId')
  @ApiOperation({ summary: '获取会话历史' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({
    status: 200,
    description: '会话历史',
    schema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        history: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              question: { type: 'string' },
              answer: { type: 'string' },
              timestamp: { type: 'string' },
              confidence: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getSessionHistory(@Param('sessionId') sessionId: string): Promise<any> {
    try {
      return await this.ragService.getSessionHistory(sessionId);
    } catch (error) {
      throw new HttpException(
        error.message || '获取会话历史失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({
    status: 200,
    description: '服务状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        timestamp: { type: 'string' },
        services: {
          type: 'object',
          properties: {
            vectorDatabase: { type: 'string' },
            llm: { type: 'string' },
            cache: { type: 'string' },
          },
        },
      },
    },
  })
  async healthCheck(): Promise<any> {
    try {
      return await this.ragService.healthCheck();
    } catch (error) {
      throw new HttpException(
        '健康检查失败',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
