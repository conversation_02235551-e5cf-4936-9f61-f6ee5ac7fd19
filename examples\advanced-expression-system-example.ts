/**
 * 高级表情和动作系统使用示例
 * 展示如何使用高级表情控制和动作管理功能
 */

import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/core/Transform';
import { DigitalHumanComponent } from '../engine/src/avatar/components/DigitalHumanComponent';
import { FacialAnimationComponent, FacialExpressionType } from '../engine/src/avatar/components/FacialAnimationComponent';
import { 
  AdvancedExpressionSystem,
  ExpressionPresetType,
  ExpressionIntensity,
  ExpressionBlendMode,
  ExpressionPreset,
  ExpressionSequence,
  RealTimeControlParams
} from '../engine/src/avatar/animation/AdvancedExpressionSystem';

/**
 * 高级表情系统示例类
 */
export class AdvancedExpressionSystemExample {
  private world: World;
  private expressionSystem: AdvancedExpressionSystem;
  private digitalHumanEntity: Entity;

  constructor() {
    this.world = new World();
    this.initializeExample();
  }

  /**
   * 初始化示例
   */
  private async initializeExample(): Promise<void> {
    console.log('😊 初始化高级表情系统示例...');

    // 1. 创建高级表情系统
    this.expressionSystem = new AdvancedExpressionSystem(this.world, {
      enableMicroExpressions: true,
      enableAutoBlink: true,
      enableBreathing: true,
      enableEmotionAnalysis: false,
      enableCulturalAdaptation: true,
      defaultBlendSpeed: 2.0,
      debug: true
    });

    // 2. 添加系统到世界
    this.world.addSystem(this.expressionSystem);

    // 3. 创建数字人实体
    await this.createDigitalHuman();

    // 4. 演示表情功能
    await this.demonstrateExpressionFeatures();

    console.log('✅ 高级表情系统示例初始化完成');
  }

  /**
   * 创建数字人实体
   */
  private async createDigitalHuman(): Promise<void> {
    console.log('👤 创建数字人实体...');

    // 创建实体
    this.digitalHumanEntity = new Entity(this.world);
    this.digitalHumanEntity.name = 'ExpressionTestCharacter';

    // 添加Transform组件
    const transform = new Transform();
    this.digitalHumanEntity.addComponent(transform);

    // 添加数字人组件
    const digitalHumanComponent = new DigitalHumanComponent(this.digitalHumanEntity, {
      name: '表情测试角色',
      userId: 'expression-test',
      bodyMorphTargets: {
        height: 0.0,
        weight: 0.0,
        muscle: 0.0,
        chest: 0.0,
        waist: 0.0,
        hips: 0.0,
        shoulders: 0.0,
        custom: new Map()
      }
    });

    this.digitalHumanEntity.addComponent(digitalHumanComponent);

    // 添加面部动画组件
    const facialAnimationComponent = new FacialAnimationComponent(this.digitalHumanEntity);
    this.digitalHumanEntity.addComponent(facialAnimationComponent);

    // 添加到世界
    this.world.addEntity(this.digitalHumanEntity);

    console.log('✅ 数字人实体创建完成');
  }

  /**
   * 演示表情功能
   */
  private async demonstrateExpressionFeatures(): Promise<void> {
    console.log('🎭 演示高级表情功能...');

    // 1. 演示基础表情预设
    await this.demonstrateBasicExpressions();

    // 2. 演示复杂表情混合
    await this.demonstrateComplexExpressions();

    // 3. 演示文化特定表情
    await this.demonstrateCulturalExpressions();

    // 4. 演示表情序列
    await this.demonstrateExpressionSequences();

    // 5. 演示实时控制
    await this.demonstrateRealTimeControl();

    // 6. 演示自定义表情
    await this.demonstrateCustomExpressions();

    // 7. 演示微表情和自动行为
    await this.demonstrateMicroExpressions();

    console.log('✅ 表情功能演示完成');
  }

  /**
   * 演示基础表情预设
   */
  private async demonstrateBasicExpressions(): Promise<void> {
    console.log('1. 演示基础表情预设...');

    const basicExpressions = ['neutral', 'happy', 'sad', 'angry', 'surprised', 'fear', 'disgust'];

    for (const expression of basicExpressions) {
      console.log(`  应用表情: ${expression}`);
      this.expressionSystem.applyExpressionPreset(
        this.digitalHumanEntity.id,
        expression,
        1.0,
        ExpressionBlendMode.SMOOTH
      );

      // 等待1.5秒
      await this.wait(1500);
    }

    console.log('  ✅ 基础表情演示完成');
  }

  /**
   * 演示复杂表情混合
   */
  private async demonstrateComplexExpressions(): Promise<void> {
    console.log('2. 演示复杂表情混合...');

    const complexExpressions = ['bittersweet', 'nervous_smile', 'confused_anger'];

    for (const expression of complexExpressions) {
      console.log(`  应用复杂表情: ${expression}`);
      this.expressionSystem.applyExpressionPreset(
        this.digitalHumanEntity.id,
        expression,
        2.0,
        ExpressionBlendMode.SMOOTH
      );

      await this.wait(2500);
    }

    console.log('  ✅ 复杂表情演示完成');
  }

  /**
   * 演示文化特定表情
   */
  private async demonstrateCulturalExpressions(): Promise<void> {
    console.log('3. 演示文化特定表情...');

    const culturalExpressions = ['chinese_polite_smile', 'chinese_respect'];

    for (const expression of culturalExpressions) {
      console.log(`  应用文化表情: ${expression}`);
      this.expressionSystem.applyExpressionPreset(
        this.digitalHumanEntity.id,
        expression,
        2.0,
        ExpressionBlendMode.SMOOTH
      );

      await this.wait(2500);
    }

    console.log('  ✅ 文化表情演示完成');
  }

  /**
   * 演示表情序列
   */
  private async demonstrateExpressionSequences(): Promise<void> {
    console.log('4. 演示表情序列...');

    // 创建自定义表情序列
    const conversationSequence: ExpressionSequence = {
      id: 'conversation_demo',
      name: '对话演示',
      keyframes: [
        { time: 0.0, expression: FacialExpressionType.NEUTRAL, weight: 1.0 },
        { time: 0.5, expression: FacialExpressionType.HAPPY, weight: 0.6 },
        { time: 1.0, expression: FacialExpressionType.SURPRISED, weight: 0.4 },
        { time: 1.5, expression: FacialExpressionType.NEUTRAL, weight: 0.8 },
        { time: 2.0, expression: FacialExpressionType.HAPPY, weight: 0.8 },
        { time: 2.5, expression: FacialExpressionType.NEUTRAL, weight: 1.0 }
      ],
      duration: 3.0,
      loop: false,
      blendMode: ExpressionBlendMode.SMOOTH,
      tags: ['conversation', 'demo']
    };

    this.expressionSystem.createCustomExpressionSequence(conversationSequence);

    console.log('  播放对话表情序列...');
    this.expressionSystem.playExpressionSequence(this.digitalHumanEntity.id, 'conversation_demo');

    await this.wait(3500);

    // 播放思考序列
    console.log('  播放思考表情序列...');
    this.expressionSystem.playExpressionSequence(this.digitalHumanEntity.id, 'thinking_process');

    await this.wait(2500);

    console.log('  ✅ 表情序列演示完成');
  }

  /**
   * 演示实时控制
   */
  private async demonstrateRealTimeControl(): Promise<void> {
    console.log('5. 演示实时控制...');

    // 设置高强度参数
    console.log('  设置高强度实时控制参数...');
    const highIntensityParams: RealTimeControlParams = {
      intensity: 1.5,
      blendSpeed: 3.0,
      randomVariation: 0.3,
      microExpressionFrequency: 0.5,
      blinkFrequency: 0.4,
      breathingIntensity: 0.8
    };

    this.expressionSystem.setRealTimeControlParams(this.digitalHumanEntity.id, highIntensityParams);

    // 应用一些表情来观察效果
    this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'happy', 2.0);
    await this.wait(3000);

    // 设置低强度参数
    console.log('  设置低强度实时控制参数...');
    const lowIntensityParams: RealTimeControlParams = {
      intensity: 0.5,
      blendSpeed: 1.0,
      randomVariation: 0.05,
      microExpressionFrequency: 0.1,
      blinkFrequency: 0.2,
      breathingIntensity: 0.3
    };

    this.expressionSystem.setRealTimeControlParams(this.digitalHumanEntity.id, lowIntensityParams);

    this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'sad', 2.0);
    await this.wait(3000);

    console.log('  ✅ 实时控制演示完成');
  }

  /**
   * 演示自定义表情
   */
  private async demonstrateCustomExpressions(): Promise<void> {
    console.log('6. 演示自定义表情...');

    // 创建自定义表情预设
    const customPreset: ExpressionPreset = {
      id: 'custom_excitement',
      name: '自定义兴奋',
      type: ExpressionPresetType.COMPLEX,
      description: '兴奋和惊讶的混合表情',
      expressions: [
        { type: FacialExpressionType.HAPPY, weight: 0.8, delay: 0 },
        { type: FacialExpressionType.SURPRISED, weight: 0.6, delay: 0.2 }
      ],
      intensity: ExpressionIntensity.STRONG,
      tags: ['custom', 'excitement', 'positive']
    };

    this.expressionSystem.createCustomExpressionPreset(customPreset);

    console.log('  应用自定义表情预设...');
    this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'custom_excitement', 2.0);

    await this.wait(3000);

    // 创建另一个自定义预设
    const customPreset2: ExpressionPreset = {
      id: 'custom_contemplation',
      name: '自定义沉思',
      type: ExpressionPresetType.COMPLEX,
      description: '沉思的表情',
      expressions: [
        { type: FacialExpressionType.NEUTRAL, weight: 0.7 },
        { type: FacialExpressionType.SAD, weight: 0.2 },
        { type: FacialExpressionType.SURPRISED, weight: 0.1 }
      ],
      intensity: ExpressionIntensity.MILD,
      tags: ['custom', 'contemplation', 'thoughtful']
    };

    this.expressionSystem.createCustomExpressionPreset(customPreset2);

    console.log('  应用沉思表情预设...');
    this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'custom_contemplation', 2.0);

    await this.wait(3000);

    console.log('  ✅ 自定义表情演示完成');
  }

  /**
   * 演示微表情和自动行为
   */
  private async demonstrateMicroExpressions(): Promise<void> {
    console.log('7. 演示微表情和自动行为...');

    // 设置高频微表情
    console.log('  启用高频微表情和自动行为...');
    const microParams: RealTimeControlParams = {
      intensity: 1.0,
      blendSpeed: 2.0,
      randomVariation: 0.2,
      microExpressionFrequency: 1.0, // 每秒一次微表情
      blinkFrequency: 0.5, // 每2秒眨眼一次
      breathingIntensity: 0.6
    };

    this.expressionSystem.setRealTimeControlParams(this.digitalHumanEntity.id, microParams);

    // 保持中性表情，观察微表情和自动行为
    this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'neutral', 1.0);

    console.log('  观察微表情和自动行为（10秒）...');
    await this.wait(10000);

    console.log('  ✅ 微表情和自动行为演示完成');
  }

  /**
   * 搜索和过滤表情预设
   */
  private demonstrateSearchAndFilter(): void {
    console.log('8. 演示搜索和过滤功能...');

    // 搜索基础表情
    const basicPresets = this.expressionSystem.getExpressionPresets(ExpressionPresetType.BASIC);
    console.log(`  找到 ${basicPresets.length} 个基础表情预设`);

    // 搜索复杂表情
    const complexPresets = this.expressionSystem.getExpressionPresets(ExpressionPresetType.COMPLEX);
    console.log(`  找到 ${complexPresets.length} 个复杂表情预设`);

    // 按标签搜索
    const positivePresets = this.expressionSystem.searchExpressionPresets({
      tags: ['positive']
    });
    console.log(`  找到 ${positivePresets.length} 个积极表情预设`);

    // 按强度搜索
    const strongPresets = this.expressionSystem.searchExpressionPresets({
      intensity: ExpressionIntensity.STRONG
    });
    console.log(`  找到 ${strongPresets.length} 个强烈表情预设`);

    // 按文化搜索
    const chinesePresets = this.expressionSystem.searchExpressionPresets({
      culture: 'chinese'
    });
    console.log(`  找到 ${chinesePresets.length} 个中国文化表情预设`);

    console.log('  ✅ 搜索和过滤演示完成');
  }

  /**
   * 等待指定时间
   * @param ms 毫秒
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    console.log('🚀 高级表情系统示例正在运行...');

    // 监听表情系统事件
    this.expressionSystem.on('expressionPresetApplied', (entityId, preset) => {
      console.log(`📝 表情预设已应用: ${preset.name} -> 实体 ${entityId}`);
    });

    this.expressionSystem.on('expressionSequenceStarted', (entityId, sequence) => {
      console.log(`🎬 表情序列开始: ${sequence.name} -> 实体 ${entityId}`);
    });

    this.expressionSystem.on('expressionSequenceStopped', (entityId, sequence) => {
      console.log(`⏹️ 表情序列停止: ${sequence.name} -> 实体 ${entityId}`);
    });

    this.expressionSystem.on('customPresetCreated', (preset) => {
      console.log(`✨ 自定义预设创建: ${preset.name}`);
    });

    this.expressionSystem.on('customSequenceCreated', (sequence) => {
      console.log(`🎭 自定义序列创建: ${sequence.name}`);
    });

    // 演示搜索功能
    this.demonstrateSearchAndFilter();

    // 添加键盘控制
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case '1':
          this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'happy');
          break;
        case '2':
          this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'sad');
          break;
        case '3':
          this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'angry');
          break;
        case '4':
          this.expressionSystem.applyExpressionPreset(this.digitalHumanEntity.id, 'surprised');
          break;
        case '5':
          this.expressionSystem.playExpressionSequence(this.digitalHumanEntity.id, 'thinking_process');
          break;
        case 'Escape':
          this.dispose();
          break;
      }
    });

    console.log('💡 提示: 按 1-5 切换表情/序列，按 ESC 退出');
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('🧹 清理高级表情系统示例...');

    if (this.expressionSystem) {
      this.expressionSystem.dispose();
    }

    console.log('✅ 高级表情系统示例已清理');
  }
}

// 导出示例类
export default AdvancedExpressionSystemExample;

// 如果直接运行此文件，则启动示例
if (typeof window !== 'undefined') {
  window.addEventListener('load', async () => {
    const example = new AdvancedExpressionSystemExample();
    await example.run();
  });
}
