/**
 * 实时场景构建器
 * 负责将AI生成的场景布局和资产匹配结果转换为实际的3D场景
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { Scene } from '../../scene/Scene';
import { SceneManager } from '../../scene/SceneManager';
import { AssetLoader } from '../../assets/AssetLoader';
import { Entity } from '../../core/Entity';
import { Transform } from '../../components/Transform';
import { MeshRenderer } from '../../components/MeshRenderer';
import { Light } from '../../components/Light';
import {
  SceneLayout,
  AssetMatchResult,
  EnvironmentConfig,
  LayoutElement
} from './SceneGenerationTypes';

/**
 * 材质应用器
 */
export class MaterialApplicator {
  private materialCache: Map<string, THREE.Material> = new Map();

  /**
   * 应用材质
   */
  async applyMaterial(entity: Entity, materialHint?: string): Promise<void> {
    const meshRenderer = entity.getComponent(MeshRenderer);
    if (!meshRenderer) return;

    let material: THREE.Material;

    if (materialHint && this.materialCache.has(materialHint)) {
      material = this.materialCache.get(materialHint)!;
    } else {
      material = await this.createMaterial(materialHint);
      if (materialHint) {
        this.materialCache.set(materialHint, material);
      }
    }

    meshRenderer.setMaterial(material);
  }

  /**
   * 创建材质
   */
  private async createMaterial(hint?: string): Promise<THREE.Material> {
    if (!hint) {
      return new THREE.MeshStandardMaterial({ color: 0x888888 });
    }

    // 根据提示创建不同的材质
    if (hint.includes('木质') || hint.includes('wood')) {
      return new THREE.MeshStandardMaterial({
        color: 0x8B4513,
        roughness: 0.8,
        metalness: 0.1
      });
    }

    if (hint.includes('金属') || hint.includes('metal')) {
      return new THREE.MeshStandardMaterial({
        color: 0xC0C0C0,
        roughness: 0.2,
        metalness: 0.9
      });
    }

    if (hint.includes('玻璃') || hint.includes('glass')) {
      return new THREE.MeshStandardMaterial({
        color: 0xFFFFFF,
        transparent: true,
        opacity: 0.3,
        roughness: 0.1,
        metalness: 0.0
      });
    }

    if (hint.includes('布料') || hint.includes('fabric')) {
      return new THREE.MeshStandardMaterial({
        color: 0x4169E1,
        roughness: 0.9,
        metalness: 0.0
      });
    }

    // 默认材质
    return new THREE.MeshStandardMaterial({ color: 0x888888 });
  }
}

/**
 * 光照计算器
 */
export class LightingCalculator {
  /**
   * 计算场景光照
   */
  async calculateLighting(scene: Scene): Promise<void> {
    // 移除现有光源
    this.removeExistingLights(scene);

    // 添加环境光
    await this.addAmbientLight(scene);

    // 添加主光源
    await this.addMainLight(scene);

    // 添加补充光源
    await this.addFillLights(scene);
  }

  /**
   * 移除现有光源
   */
  private removeExistingLights(scene: Scene): void {
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const light = entity.getComponent(Light);
      if (light) {
        scene.removeEntity(entity);
      }
    });
  }

  /**
   * 添加环境光
   */
  private async addAmbientLight(scene: Scene): Promise<void> {
    const ambientLightEntity = scene.getWorld().createEntity('ambient_light');
    const light = new Light(ambientLightEntity, {
      type: 'ambient',
      color: new THREE.Color(0x404040),
      intensity: 0.4
    });
    ambientLightEntity.addComponent(light);
    scene.addEntity(ambientLightEntity);
  }

  /**
   * 添加主光源
   */
  private async addMainLight(scene: Scene): Promise<void> {
    const mainLightEntity = scene.getWorld().createEntity('main_light');
    
    const transform = new Transform(mainLightEntity, {
      position: new THREE.Vector3(10, 10, 5)
    });
    mainLightEntity.addComponent(transform);

    const light = new Light(mainLightEntity, {
      type: 'directional',
      color: new THREE.Color(0xffffff),
      intensity: 1.0,
      castShadow: true
    });
    mainLightEntity.addComponent(light);

    scene.addEntity(mainLightEntity);
  }

  /**
   * 添加补充光源
   */
  private async addFillLights(scene: Scene): Promise<void> {
    // 添加补充光源以减少阴影
    const fillLightEntity = scene.getWorld().createEntity('fill_light');
    
    const transform = new Transform(fillLightEntity, {
      position: new THREE.Vector3(-5, 8, -5)
    });
    fillLightEntity.addComponent(transform);

    const light = new Light(fillLightEntity, {
      type: 'directional',
      color: new THREE.Color(0x8888ff),
      intensity: 0.3
    });
    fillLightEntity.addComponent(light);

    scene.addEntity(fillLightEntity);
  }
}

/**
 * 实时场景构建器
 */
export class RealTimeSceneBuilder {
  private engine: Engine;
  private sceneManager: SceneManager;
  private assetLoader: AssetLoader;
  private materialApplicator: MaterialApplicator;
  private lightingCalculator: LightingCalculator;
  private buildProgress: number = 0;

  constructor(engine: Engine) {
    this.engine = engine;
    this.sceneManager = engine.getSceneManager();
    this.assetLoader = new AssetLoader();
    this.materialApplicator = new MaterialApplicator();
    this.lightingCalculator = new LightingCalculator();
  }

  /**
   * 构建场景
   */
  async buildScene(layout: SceneLayout, assets: AssetMatchResult[]): Promise<Scene> {
    this.buildProgress = 0;

    try {
      // 创建新场景
      const scene = this.sceneManager.createScene('Generated Scene');
      
      // 设置环境
      this.buildProgress = 10;
      await this.setupEnvironment(scene, layout.environment);
      
      // 放置资产
      this.buildProgress = 30;
      await this.placeAssets(scene, assets);
      
      // 计算光照
      this.buildProgress = 80;
      await this.lightingCalculator.calculateLighting(scene);
      
      // 优化性能
      this.buildProgress = 90;
      await this.optimizeScene(scene);
      
      this.buildProgress = 100;
      return scene;
    } catch (error) {
      console.error('场景构建失败:', error);
      throw error;
    }
  }

  /**
   * 流式构建场景
   */
  async *buildSceneStream(
    layout: SceneLayout, 
    assets: AssetMatchResult[]
  ): AsyncGenerator<{ progress: number; scene?: Scene }, Scene, unknown> {
    this.buildProgress = 0;

    // 创建新场景
    const scene = this.sceneManager.createScene('Generated Scene');
    yield { progress: 5, scene };

    // 设置环境
    await this.setupEnvironment(scene, layout.environment);
    this.buildProgress = 20;
    yield { progress: this.buildProgress, scene };

    // 逐个放置资产
    for (let i = 0; i < assets.length; i++) {
      await this.placeSingleAsset(scene, assets[i]);
      this.buildProgress = 20 + (i + 1) / assets.length * 50;
      yield { progress: this.buildProgress, scene };
    }

    // 计算光照
    await this.lightingCalculator.calculateLighting(scene);
    this.buildProgress = 80;
    yield { progress: this.buildProgress, scene };

    // 优化性能
    await this.optimizeScene(scene);
    this.buildProgress = 100;
    yield { progress: this.buildProgress, scene };

    return scene;
  }

  /**
   * 设置环境
   */
  private async setupEnvironment(scene: Scene, environment: EnvironmentConfig): Promise<void> {
    // 设置天空盒
    if (environment.skybox) {
      const skybox = await this.loadSkybox(environment.skybox);
      scene.setSkybox(skybox);
    }

    // 设置雾效
    scene.setFog(new THREE.Fog(0x888888, 50, 200));
  }

  /**
   * 放置资产
   */
  private async placeAssets(scene: Scene, assets: AssetMatchResult[]): Promise<void> {
    const promises = assets.map(assetMatch => this.placeSingleAsset(scene, assetMatch));
    await Promise.all(promises);
  }

  /**
   * 放置单个资产
   */
  private async placeSingleAsset(scene: Scene, assetMatch: AssetMatchResult): Promise<void> {
    try {
      // 加载资产
      const assetEntity = await this.loadAsset(assetMatch);
      
      // 设置变换
      this.setupTransform(assetEntity, assetMatch.element);
      
      // 应用材质
      await this.materialApplicator.applyMaterial(assetEntity, assetMatch.element.materialHint);
      
      // 添加到场景
      scene.addEntity(assetEntity);
    } catch (error) {
      console.warn(`放置资产失败: ${assetMatch.element.name}`, error);
      
      // 创建占位符
      const placeholder = await this.createPlaceholder(assetMatch.element);
      scene.addEntity(placeholder);
    }
  }

  /**
   * 加载资产
   */
  private async loadAsset(assetMatch: AssetMatchResult): Promise<Entity> {
    const asset = assetMatch.asset;
    
    if (asset.url.startsWith('primitive://')) {
      // 创建基础几何体
      return this.createPrimitive(asset.url, assetMatch.element.name);
    } else {
      // 加载3D模型
      return await this.assetLoader.loadModel(asset.url, assetMatch.element.name);
    }
  }

  /**
   * 创建基础几何体
   */
  private createPrimitive(primitiveUrl: string, name: string): Entity {
    const world = this.engine.getWorld();
    const entity = world.createEntity(name);
    
    let geometry: THREE.BufferGeometry;
    
    switch (primitiveUrl) {
      case 'primitive://box':
        geometry = new THREE.BoxGeometry(1, 1, 1);
        break;
      case 'primitive://sphere':
        geometry = new THREE.SphereGeometry(0.5, 32, 32);
        break;
      case 'primitive://cylinder':
        geometry = new THREE.CylinderGeometry(0.5, 0.5, 1, 32);
        break;
      default:
        geometry = new THREE.BoxGeometry(1, 1, 1);
    }
    
    const material = new THREE.MeshStandardMaterial({ color: 0x888888 });
    const mesh = new THREE.Mesh(geometry, material);
    
    const meshRenderer = new MeshRenderer(entity, { mesh });
    entity.addComponent(meshRenderer);
    
    return entity;
  }

  /**
   * 设置变换
   */
  private setupTransform(entity: Entity, element: any): void {
    const transform = entity.getComponent(Transform) || new Transform(entity);
    
    if (element.position) {
      transform.setPosition(element.position);
    }
    
    if (element.rotation) {
      transform.setRotation(element.rotation);
    }
    
    if (element.scale) {
      transform.setScale(element.scale);
    }
    
    if (!entity.getComponent(Transform)) {
      entity.addComponent(transform);
    }
  }

  /**
   * 创建占位符
   */
  private async createPlaceholder(element: any): Promise<Entity> {
    const world = this.engine.getWorld();
    const entity = world.createEntity(`placeholder_${element.name}`);
    
    // 创建简单的立方体作为占位符
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      color: 0xff0000, 
      wireframe: true 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    const meshRenderer = new MeshRenderer(entity, { mesh });
    entity.addComponent(meshRenderer);
    
    const transform = new Transform(entity, {
      position: element.position || new THREE.Vector3(0, 0, 0),
      rotation: element.rotation || new THREE.Euler(0, 0, 0),
      scale: element.scale || new THREE.Vector3(1, 1, 1)
    });
    entity.addComponent(transform);
    
    return entity;
  }

  /**
   * 加载天空盒
   */
  private async loadSkybox(skyboxUrl: string): Promise<THREE.CubeTexture> {
    // 简化的天空盒加载
    const loader = new THREE.CubeTextureLoader();
    return new Promise((resolve, reject) => {
      loader.load(
        [skyboxUrl, skyboxUrl, skyboxUrl, skyboxUrl, skyboxUrl, skyboxUrl],
        resolve,
        undefined,
        reject
      );
    });
  }

  /**
   * 优化场景
   */
  private async optimizeScene(scene: Scene): Promise<void> {
    // 合并几何体
    await this.mergeGeometries(scene);
    
    // 设置LOD
    await this.setupLOD(scene);
    
    // 优化材质
    await this.optimizeMaterials(scene);
  }

  /**
   * 合并几何体
   */
  private async mergeGeometries(scene: Scene): Promise<void> {
    // 实现几何体合并逻辑以提高性能
  }

  /**
   * 设置LOD
   */
  private async setupLOD(scene: Scene): Promise<void> {
    // 实现LOD设置逻辑
  }

  /**
   * 优化材质
   */
  private async optimizeMaterials(scene: Scene): Promise<void> {
    // 实现材质优化逻辑
  }

  /**
   * 获取构建进度
   */
  getBuildProgress(): number {
    return this.buildProgress;
  }

  /**
   * 更新场景
   */
  async updateScene(scene: Scene, newAssets: AssetMatchResult[]): Promise<void> {
    // 增量更新场景
    for (const assetMatch of newAssets) {
      await this.placeSingleAsset(scene, assetMatch);
    }
    
    // 重新计算光照
    await this.lightingCalculator.calculateLighting(scene);
  }

  /**
   * 移除场景元素
   */
  async removeSceneElement(scene: Scene, elementName: string): Promise<void> {
    const entities = scene.getEntities();
    const entityToRemove = entities.find(entity => entity.getName() === elementName);
    
    if (entityToRemove) {
      scene.removeEntity(entityToRemove);
    }
  }
}
