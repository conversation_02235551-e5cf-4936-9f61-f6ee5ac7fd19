# RAG数字人交互系统 API 使用示例

本文档提供了RAG数字人交互系统各个API的详细使用示例。

## 基础配置

```javascript
const API_BASE_URL = 'http://localhost:8080';
const AUTH_TOKEN = 'your_jwt_token_here';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${AUTH_TOKEN}`
};
```

## 1. 用户认证

### 登录获取Token

```javascript
// POST /api/auth/login
const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: '<EMAIL>',
    password: 'password123'
  })
});

const { access_token } = await loginResponse.json();
```

## 2. 知识库管理

### 创建知识库

```javascript
// POST /api/knowledge-bases
const createKBResponse = await fetch(`${API_BASE_URL}/api/knowledge-bases`, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    name: '企业知识库',
    description: '包含企业相关文档和政策',
    category: 'enterprise',
    language: 'zh-CN'
  })
});

const knowledgeBase = await createKBResponse.json();
console.log('知识库ID:', knowledgeBase.id);
```

### 获取知识库列表

```javascript
// GET /api/knowledge-bases
const listKBResponse = await fetch(
  `${API_BASE_URL}/api/knowledge-bases?page=1&limit=10&category=enterprise`,
  { headers }
);

const { data, total, page, limit } = await listKBResponse.json();
console.log(`共${total}个知识库，当前第${page}页`);
```

### 上传文档到知识库

```javascript
// POST /api/knowledge-bases/{id}/documents
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('title', '企业政策手册');
formData.append('category', 'policy');
formData.append('description', '最新的企业政策和规定');

const uploadResponse = await fetch(
  `${API_BASE_URL}/api/knowledge-bases/${knowledgeBaseId}/documents`,
  {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` },
    body: formData
  }
);

const document = await uploadResponse.json();
console.log('文档上传成功:', document.id);
```

### 批量上传文档

```javascript
// POST /api/knowledge-bases/{id}/documents/batch
const batchFormData = new FormData();
files.forEach(file => {
  batchFormData.append('files', file);
});
batchFormData.append('metadata', JSON.stringify({
  category: 'training',
  tags: ['新员工', '培训']
}));

const batchUploadResponse = await fetch(
  `${API_BASE_URL}/api/knowledge-bases/${knowledgeBaseId}/documents/batch`,
  {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` },
    body: batchFormData
  }
);
```

## 3. 数字人管理

### 创建数字人

```javascript
// POST /api/digital-humans
const createDHResponse = await fetch(`${API_BASE_URL}/api/digital-humans`, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    name: '客服小助手',
    description: '专业的客户服务数字人',
    modelConfig: {
      voice: 'female',
      language: 'zh-CN',
      personality: 'friendly'
    },
    voiceConfig: {
      speed: 1.0,
      pitch: 1.0,
      volume: 0.8
    }
  })
});

const digitalHuman = await createDHResponse.json();
console.log('数字人ID:', digitalHuman.id);
```

### 绑定知识库到数字人

```javascript
// POST /api/digital-humans/{id}/knowledge-bases
const bindResponse = await fetch(
  `${API_BASE_URL}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
  {
    method: 'POST',
    headers,
    body: JSON.stringify({
      knowledgeBaseIds: [knowledgeBaseId1, knowledgeBaseId2],
      bindingConfig: {
        type: 'primary',
        priority: 1,
        searchWeight: 1.0,
        enabledFeatures: ['search', 'qa', 'summary']
      }
    })
  }
);

const bindResult = await bindResponse.json();
console.log('绑定成功:', bindResult.bindingIds);
```

### 获取数字人绑定的知识库

```javascript
// GET /api/digital-humans/{id}/knowledge-bases
const bindingsResponse = await fetch(
  `${API_BASE_URL}/api/digital-humans/${digitalHumanId}/knowledge-bases`,
  { headers }
);

const bindings = await bindingsResponse.json();
bindings.forEach(binding => {
  console.log(`知识库: ${binding.knowledgeBase.name}, 优先级: ${binding.priority}`);
});
```

## 4. RAG查询

### 基础RAG查询

```javascript
// POST /api/rag/query
const ragResponse = await fetch(`${API_BASE_URL}/api/rag/query`, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    question: '公司的年假政策是什么？',
    digitalHumanId: digitalHumanId,
    maxResults: 5,
    temperature: 0.7,
    language: 'zh-CN'
  })
});

const ragResult = await ragResponse.json();
console.log('回答:', ragResult.answer);
console.log('置信度:', ragResult.confidence);
console.log('来源数量:', ragResult.sources.length);
```

### 流式RAG查询

```javascript
// POST /api/rag/query/stream
const streamResponse = await fetch(`${API_BASE_URL}/api/rag/query/stream`, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    question: '请详细介绍一下人工智能的发展历程',
    digitalHumanId: digitalHumanId,
    temperature: 0.8
  })
});

const reader = streamResponse.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;
      
      try {
        const parsed = JSON.parse(data);
        if (parsed.type === 'chunk') {
          process.stdout.write(parsed.content);
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
}
```

### 向量搜索

```javascript
// POST /api/rag/search
const searchResponse = await fetch(`${API_BASE_URL}/api/rag/search`, {
  method: 'POST',
  headers,
  body: JSON.stringify({
    query: '员工培训',
    digitalHumanId: digitalHumanId,
    maxResults: 10,
    threshold: 0.7
  })
});

const searchResult = await searchResponse.json();
searchResult.results.forEach((result, index) => {
  console.log(`${index + 1}. 相似度: ${result.score.toFixed(3)}`);
  console.log(`   内容: ${result.content.substring(0, 100)}...`);
  console.log(`   来源: ${result.source.filename}`);
});
```

## 5. 会话管理

### 获取会话历史

```javascript
// GET /api/rag/sessions/{sessionId}
const sessionResponse = await fetch(
  `${API_BASE_URL}/api/rag/sessions/${sessionId}`,
  { headers }
);

const session = await sessionResponse.json();
console.log('会话历史:', session.history);
```

### 评价回答质量

```javascript
// POST /api/rag/sessions/{sessionId}/rating
const ratingResponse = await fetch(
  `${API_BASE_URL}/api/rag/sessions/${sessionId}/rating`,
  {
    method: 'POST',
    headers,
    body: JSON.stringify({
      rating: 5,
      feedback: '回答很准确，很有帮助'
    })
  }
);
```

## 6. 系统监控

### 健康检查

```javascript
// GET /health
const healthResponse = await fetch(`${API_BASE_URL}/health`);
const health = await healthResponse.json();

console.log('系统状态:', health.status);
console.log('服务状态:', health.services);
```

### 获取统计信息

```javascript
// GET /api/rag/stats
const statsResponse = await fetch(`${API_BASE_URL}/api/rag/stats`, { headers });
const stats = await statsResponse.json();

console.log('查询统计:', stats.queryStats);
console.log('性能指标:', stats.performance);
```

## 7. 错误处理

```javascript
async function handleAPICall(apiCall) {
  try {
    const response = await apiCall();
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API错误 ${response.status}: ${error.message}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API调用失败:', error.message);
    
    // 根据错误类型进行处理
    if (error.message.includes('401')) {
      // Token过期，需要重新登录
      await refreshToken();
    } else if (error.message.includes('429')) {
      // 请求过于频繁，等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000));
      return handleAPICall(apiCall);
    }
    
    throw error;
  }
}
```

## 8. 完整示例：创建知识库并进行问答

```javascript
async function completeExample() {
  try {
    // 1. 登录获取Token
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: 'demo123'
      })
    });
    const { access_token } = await loginResponse.json();
    
    const authHeaders = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${access_token}`
    };

    // 2. 创建知识库
    const kbResponse = await fetch(`${API_BASE_URL}/api/knowledge-bases`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        name: 'Demo知识库',
        description: '演示用知识库',
        category: 'demo'
      })
    });
    const knowledgeBase = await kbResponse.json();

    // 3. 创建数字人
    const dhResponse = await fetch(`${API_BASE_URL}/api/digital-humans`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        name: 'Demo助手',
        description: '演示数字人'
      })
    });
    const digitalHuman = await dhResponse.json();

    // 4. 绑定知识库
    await fetch(
      `${API_BASE_URL}/api/digital-humans/${digitalHuman.id}/knowledge-bases`,
      {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          knowledgeBaseIds: [knowledgeBase.id],
          bindingConfig: { type: 'primary', priority: 1 }
        })
      }
    );

    // 5. 进行RAG查询
    const ragResponse = await fetch(`${API_BASE_URL}/api/rag/query`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        question: '你好，请介绍一下自己',
        digitalHumanId: digitalHuman.id
      })
    });
    const ragResult = await ragResponse.json();

    console.log('数字人回答:', ragResult.answer);
    
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 运行完整示例
completeExample();
```

## 注意事项

1. **认证**: 所有API调用都需要有效的JWT Token
2. **速率限制**: API有速率限制，请合理控制请求频率
3. **文件大小**: 文档上传有大小限制（默认100MB）
4. **超时设置**: 长时间操作（如文档处理、RAG查询）建议设置较长的超时时间
5. **错误重试**: 对于网络错误或临时性错误，建议实现重试机制
6. **数据验证**: 在发送请求前验证数据格式和必填字段
