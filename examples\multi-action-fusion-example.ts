/**
 * 多动作融合系统使用示例
 * 演示如何使用多动作融合管理器批量导入BIP文件并解决冲突
 */
import { Engine } from '../engine/src/core/Engine';
import { World } from '../engine/src/core/World';
import { Entity } from '../engine/src/core/Entity';
import { Transform } from '../engine/src/scene/Transform';
import {
  DigitalHumanSystem,
  DigitalHumanComponent,
  DigitalHumanSource,
  MultiActionFusionManager,
  ActionConflictType,
  ConflictResolutionType
} from '../engine/src/avatar';

/**
 * 多动作融合示例类
 */
class MultiActionFusionExample {
  private engine: Engine;
  private world: World;
  private digitalHumanSystem: DigitalHumanSystem;
  private digitalHuman: Entity | null = null;
  private fusionManager: MultiActionFusionManager | null = null;

  constructor() {
    // 初始化引擎
    this.engine = new Engine();
    this.world = this.engine.getWorld();

    // 添加数字人系统
    this.digitalHumanSystem = new DigitalHumanSystem(this.world, {
      debug: true,
      maxConcurrentGenerations: 1
    });
    this.world.addSystem(this.digitalHumanSystem);
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    console.log('初始化多动作融合示例...');

    // 创建数字人
    await this.createDigitalHuman();

    // 获取多动作融合管理器
    this.fusionManager = this.digitalHumanSystem.getMultiActionFusionManager(this.digitalHuman!);

    if (this.fusionManager) {
      this.setupEventListeners();
      console.log('多动作融合管理器已准备就绪');
    }
  }

  /**
   * 创建数字人
   */
  private async createDigitalHuman(): Promise<void> {
    const request = {
      id: 'example-digital-human',
      userId: 'user-001',
      source: DigitalHumanSource.MANUAL,
      sourceData: {},
      options: {
        name: '示例数字人',
        tags: ['示例', '测试'],
        licenseType: 'private'
      }
    };

    this.digitalHuman = await this.digitalHumanSystem.createDigitalHuman(request);
    console.log('数字人创建完成:', this.digitalHuman.id);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.fusionManager) return;

    // 监听批量导入开始
    this.fusionManager.on('batchImportStarted', (files: File[]) => {
      console.log(`开始批量导入 ${files.length} 个BIP文件`);
    });

    // 监听批量导入完成
    this.fusionManager.on('batchImportCompleted', (result: any) => {
      console.log('批量导入完成:', result);
      console.log(`成功导入: ${result.successCount}/${result.totalFiles}`);
      console.log(`总动作数量: ${result.actionCount}`);
      console.log(`检测到冲突: ${result.conflicts.length}`);
    });

    // 监听冲突解决
    this.fusionManager.on('conflictResolved', (conflict: any, resolution: any) => {
      console.log('冲突已解决:', {
        conflictType: conflict.type,
        resolutionType: resolution.type,
        reason: resolution.reason
      });
    });

    // 监听动作转换完成
    this.fusionManager.on('actionTransitionCompleted', (from: string, to: string) => {
      console.log(`动作转换完成: ${from} -> ${to}`);
    });
  }

  /**
   * 模拟批量导入BIP文件
   */
  public async simulateBatchImport(): Promise<void> {
    if (!this.fusionManager) {
      console.error('多动作融合管理器未初始化');
      return;
    }

    console.log('模拟批量导入BIP文件...');

    // 创建模拟的BIP文件
    const mockFiles = this.createMockBIPFiles();

    try {
      const result = await this.fusionManager.importMultipleBIPFiles(mockFiles);
      
      console.log('导入结果:', result);

      // 显示动作库
      this.displayActionLibrary();

      // 演示动作播放
      await this.demonstrateActionPlayback();

    } catch (error) {
      console.error('批量导入失败:', error);
    }
  }

  /**
   * 创建模拟的BIP文件
   */
  private createMockBIPFiles(): File[] {
    const mockFiles: File[] = [];

    // 模拟文件数据
    const fileNames = [
      'walk_cycle.bip',
      'run_cycle.bip',
      'idle_animation.bip',
      'jump_action.bip',
      'wave_gesture.bip'
    ];

    for (const fileName of fileNames) {
      // 创建模拟的BIP文件内容
      const mockContent = this.createMockBIPContent(fileName);
      const blob = new Blob([mockContent], { type: 'application/octet-stream' });
      const file = new File([blob], fileName, { type: 'application/octet-stream' });
      mockFiles.push(file);
    }

    return mockFiles;
  }

  /**
   * 创建模拟的BIP文件内容
   */
  private createMockBIPContent(fileName: string): string {
    const animationName = fileName.replace('.bip', '');
    
    return `
BONE Bip01
PARENT -1
POSITION 0 0 0
ROTATION 0 0 0 1

BONE Bip01 Pelvis
PARENT Bip01
POSITION 0 0 0
ROTATION 0 0 0 1

BONE Bip01 Spine
PARENT Bip01 Pelvis
POSITION 0 0.1 0
ROTATION 0 0 0 1

BONE Bip01 Spine1
PARENT Bip01 Spine
POSITION 0 0.1 0
ROTATION 0 0 0 1

BONE Bip01 Neck
PARENT Bip01 Spine1
POSITION 0 0.1 0
ROTATION 0 0 0 1

BONE Bip01 Head
PARENT Bip01 Neck
POSITION 0 0.05 0
ROTATION 0 0 0 1

ANIMATION ${animationName}
DURATION 2.0
FRAME_RATE 30
    `.trim();
  }

  /**
   * 显示动作库
   */
  private displayActionLibrary(): void {
    if (!this.fusionManager) return;

    const actionItems = this.fusionManager.getActionLibraryItems();
    
    console.log('\n=== 动作库 ===');
    console.log(`总动作数量: ${actionItems.length}`);
    
    actionItems.forEach((item, index) => {
      console.log(`${index + 1}. ${item.name}`);
      console.log(`   时长: ${item.duration.toFixed(2)}s`);
      console.log(`   来源: ${item.sourceFile}`);
      console.log(`   状态: ${item.status}`);
      console.log(`   冲突: ${item.hasConflict ? '是' : '否'}`);
      console.log('');
    });
  }

  /**
   * 演示动作播放
   */
  private async demonstrateActionPlayback(): Promise<void> {
    if (!this.fusionManager) return;

    const actionItems = this.fusionManager.getActionLibraryItems();
    if (actionItems.length === 0) {
      console.log('没有可播放的动作');
      return;
    }

    console.log('\n=== 动作播放演示 ===');

    // 播放第一个动作
    const firstAction = actionItems[0];
    console.log(`播放动作: ${firstAction.name}`);
    
    try {
      await this.fusionManager.playAction(firstAction.name, {
        fadeTime: 0.5,
        loop: true,
        speed: 1.0
      });
      
      console.log(`动作 ${firstAction.name} 开始播放`);

      // 等待一段时间后切换到下一个动作
      if (actionItems.length > 1) {
        setTimeout(async () => {
          const secondAction = actionItems[1];
          console.log(`切换到动作: ${secondAction.name}`);
          
          try {
            await this.fusionManager!.playAction(secondAction.name, {
              fadeTime: 0.3,
              loop: false,
              speed: 1.2
            });
            
            console.log(`动作 ${secondAction.name} 开始播放`);
          } catch (error) {
            console.error('动作切换失败:', error);
          }
        }, 3000);
      }

    } catch (error) {
      console.error('动作播放失败:', error);
    }
  }

  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    try {
      await this.initialize();
      await this.simulateBatchImport();
      
      console.log('\n多动作融合示例运行完成！');
      console.log('系统将继续运行以处理动画播放...');
      
      // 启动引擎更新循环
      this.startUpdateLoop();
      
    } catch (error) {
      console.error('示例运行失败:', error);
    }
  }

  /**
   * 启动更新循环
   */
  private startUpdateLoop(): void {
    let lastTime = performance.now();
    
    const update = (currentTime: number) => {
      const deltaTime = (currentTime - lastTime) / 1000;
      lastTime = currentTime;
      
      // 更新引擎
      this.engine.update(deltaTime);
      
      // 继续下一帧
      requestAnimationFrame(update);
    };
    
    requestAnimationFrame(update);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.fusionManager) {
      this.fusionManager.dispose();
    }
    
    this.digitalHumanSystem.dispose();
    this.engine.dispose();
    
    console.log('示例资源已清理');
  }
}

// 运行示例
const example = new MultiActionFusionExample();
example.run().catch(console.error);

// 导出示例类
export { MultiActionFusionExample };
