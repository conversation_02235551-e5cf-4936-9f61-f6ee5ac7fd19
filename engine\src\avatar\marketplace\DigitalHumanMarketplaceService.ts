import { Logger } from '../../../core/Logger';
import { EventEmitter } from 'events';
import { MinIOStorageService } from '../../storage/MinIOStorageService';
import { DigitalHumanPackage } from '../formats/DigitalHumanPackage';

/**
 * 市场物品信息
 */
export interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  creatorId: string;
  creatorName: string;
  thumbnailUrl: string;
  previewUrls: string[];
  price: number;
  currency: 'USD' | 'CNY' | 'EUR';
  license: 'free' | 'cc0' | 'cc_by' | 'commercial';
  tags: string[];
  category: string;
  downloadCount: number;
  rating: number;
  reviewCount: number;
  fileSize: number;
  version: string;
  compatibility: string[];
  createdAt: number;
  updatedAt: number;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  featured: boolean;
}

/**
 * 搜索查询参数
 */
export interface SearchQuery {
  keyword?: string;
  category?: string;
  tags?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  license?: string[];
  rating?: number;
  sortBy?: 'newest' | 'popular' | 'rating' | 'price_low' | 'price_high';
  page?: number;
  limit?: number;
}

/**
 * 搜索结果
 */
export interface SearchResult {
  items: MarketplaceItem[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * 发布信息
 */
export interface PublishInfo {
  userId: string;
  title: string;
  description: string;
  price: number;
  currency: 'USD' | 'CNY' | 'EUR';
  license: 'free' | 'cc0' | 'cc_by' | 'commercial';
  tags: string[];
  category: string;
  previewImages?: File[];
}

/**
 * 发布结果
 */
export interface PublishResult {
  success: boolean;
  itemId?: string;
  errors?: string[];
  warnings?: string[];
  status: 'pending' | 'approved' | 'rejected';
}

/**
 * 下载结果
 */
export interface DownloadResult {
  success: boolean;
  downloadUrl?: string;
  expiresAt: number;
  fileSize: number;
  error?: string;
}

/**
 * 用户评价
 */
export interface UserReview {
  id: string;
  userId: string;
  userName: string;
  itemId: string;
  rating: number;
  comment: string;
  createdAt: number;
  helpful: number;
}

/**
 * 数字人市场服务
 * 实现M4阶段的完整市场功能
 */
export class DigitalHumanMarketplaceService extends EventEmitter {
  private logger: Logger;
  private storageService: MinIOStorageService;
  private apiBaseUrl: string;
  private apiKey: string;

  // 缓存
  private itemCache: Map<string, MarketplaceItem> = new Map();
  private searchCache: Map<string, SearchResult> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5分钟

  constructor(apiBaseUrl: string, apiKey: string) {
    super();
    this.logger = new Logger('DigitalHumanMarketplaceService');
    this.apiBaseUrl = apiBaseUrl;
    this.apiKey = apiKey;
    this.storageService = new MinIOStorageService();
  }

  /**
   * 发布数字人到市场
   */
  public async publishDigitalHuman(
    digitalHumanId: string,
    publishInfo: PublishInfo
  ): Promise<PublishResult> {
    try {
      this.logger.info(`发布数字人到市场: ${digitalHumanId}`);

      // 1. 验证发布权限
      await this.validatePublishPermission(digitalHumanId, publishInfo.userId);

      // 2. 生成数字人包
      const digitalHumanPackage = await this.generateDigitalHumanPackage(digitalHumanId);

      // 3. 上传到存储服务
      const uploadResult = await this.uploadPackageToStorage(digitalHumanPackage, publishInfo);

      // 4. 生成预览图
      const previewUrls = await this.generatePreviewImages(digitalHumanId, publishInfo.previewImages);

      // 5. 创建市场物品
      const marketItem = await this.createMarketItem(digitalHumanPackage, publishInfo, uploadResult, previewUrls);

      // 6. 提交审核
      const reviewResult = await this.submitForReview(marketItem);

      this.logger.info(`数字人发布成功: ${marketItem.id}`);
      this.emit('itemPublished', { itemId: marketItem.id, status: reviewResult.status });

      return {
        success: true,
        itemId: marketItem.id,
        status: reviewResult.status,
        warnings: reviewResult.warnings
      };

    } catch (error) {
      this.logger.error('数字人发布失败:', error);
      return {
        success: false,
        errors: [error.message],
        status: 'rejected'
      };
    }
  }

  /**
   * 搜索市场中的数字人
   */
  public async searchDigitalHumans(query: SearchQuery): Promise<SearchResult> {
    try {
      // 检查缓存
      const cacheKey = this.generateSearchCacheKey(query);
      const cachedResult = this.searchCache.get(cacheKey);
      
      if (cachedResult && this.isCacheValid(cachedResult)) {
        return cachedResult;
      }

      this.logger.info('搜索数字人市场:', query);

      // 构建搜索请求
      const searchParams = this.buildSearchParams(query);
      
      // 调用市场API
      const response = await this.callMarketplaceAPI('GET', '/search', searchParams);
      
      if (!response.success) {
        throw new Error(`搜索失败: ${response.error}`);
      }

      const result: SearchResult = {
        items: response.data.items,
        total: response.data.total,
        page: query.page || 1,
        limit: query.limit || 20,
        hasMore: response.data.hasMore
      };

      // 缓存结果
      this.searchCache.set(cacheKey, result);

      this.emit('searchCompleted', { query, resultCount: result.items.length });
      return result;

    } catch (error) {
      this.logger.error('搜索失败:', error);
      throw error;
    }
  }

  /**
   * 获取市场物品详情
   */
  public async getMarketplaceItem(itemId: string): Promise<MarketplaceItem> {
    try {
      // 检查缓存
      const cachedItem = this.itemCache.get(itemId);
      if (cachedItem && this.isCacheValid(cachedItem)) {
        return cachedItem;
      }

      this.logger.info(`获取市场物品详情: ${itemId}`);

      const response = await this.callMarketplaceAPI('GET', `/items/${itemId}`);
      
      if (!response.success) {
        throw new Error(`获取物品详情失败: ${response.error}`);
      }

      const item: MarketplaceItem = response.data;
      
      // 缓存物品信息
      this.itemCache.set(itemId, item);

      return item;

    } catch (error) {
      this.logger.error('获取物品详情失败:', error);
      throw error;
    }
  }

  /**
   * 下载市场数字人
   */
  public async downloadDigitalHuman(itemId: string, userId: string): Promise<DownloadResult> {
    try {
      this.logger.info(`下载数字人: ${itemId}, 用户: ${userId}`);

      // 1. 验证下载权限
      await this.validateDownloadPermission(itemId, userId);

      // 2. 获取物品信息
      const item = await this.getMarketplaceItem(itemId);

      // 3. 生成下载链接
      const downloadUrl = await this.generateDownloadUrl(itemId, userId);

      // 4. 记录下载历史
      await this.recordDownloadHistory(itemId, userId);

      // 5. 更新下载计数
      await this.updateDownloadCount(itemId);

      this.logger.info(`数字人下载链接已生成: ${itemId}`);
      this.emit('itemDownloaded', { itemId, userId, fileSize: item.fileSize });

      return {
        success: true,
        downloadUrl,
        expiresAt: Date.now() + 3600000, // 1小时有效期
        fileSize: item.fileSize
      };

    } catch (error) {
      this.logger.error('下载失败:', error);
      return {
        success: false,
        expiresAt: 0,
        fileSize: 0,
        error: error.message
      };
    }
  }

  /**
   * 获取用户的发布物品
   */
  public async getUserPublishedItems(userId: string, page: number = 1, limit: number = 20): Promise<SearchResult> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/users/${userId}/items`, {
        page,
        limit
      });

      if (!response.success) {
        throw new Error(`获取用户发布物品失败: ${response.error}`);
      }

      return {
        items: response.data.items,
        total: response.data.total,
        page,
        limit,
        hasMore: response.data.hasMore
      };

    } catch (error) {
      this.logger.error('获取用户发布物品失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的下载历史
   */
  public async getUserDownloadHistory(userId: string, page: number = 1, limit: number = 20): Promise<any[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/users/${userId}/downloads`, {
        page,
        limit
      });

      if (!response.success) {
        throw new Error(`获取下载历史失败: ${response.error}`);
      }

      return response.data.downloads;

    } catch (error) {
      this.logger.error('获取下载历史失败:', error);
      throw error;
    }
  }

  /**
   * 提交用户评价
   */
  public async submitReview(itemId: string, userId: string, rating: number, comment: string): Promise<UserReview> {
    try {
      const response = await this.callMarketplaceAPI('POST', `/items/${itemId}/reviews`, {
        userId,
        rating,
        comment
      });

      if (!response.success) {
        throw new Error(`提交评价失败: ${response.error}`);
      }

      const review: UserReview = response.data;
      this.emit('reviewSubmitted', { itemId, userId, rating });

      return review;

    } catch (error) {
      this.logger.error('提交评价失败:', error);
      throw error;
    }
  }

  /**
   * 获取物品评价
   */
  public async getItemReviews(itemId: string, page: number = 1, limit: number = 10): Promise<UserReview[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/items/${itemId}/reviews`, {
        page,
        limit
      });

      if (!response.success) {
        throw new Error(`获取评价失败: ${response.error}`);
      }

      return response.data.reviews;

    } catch (error) {
      this.logger.error('获取评价失败:', error);
      throw error;
    }
  }

  /**
   * 获取热门物品
   */
  public async getFeaturedItems(limit: number = 10): Promise<MarketplaceItem[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', '/featured', { limit });

      if (!response.success) {
        throw new Error(`获取热门物品失败: ${response.error}`);
      }

      return response.data.items;

    } catch (error) {
      this.logger.error('获取热门物品失败:', error);
      throw error;
    }
  }

  /**
   * 获取分类列表
   */
  public async getCategories(): Promise<string[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', '/categories');

      if (!response.success) {
        throw new Error(`获取分类失败: ${response.error}`);
      }

      return response.data.categories;

    } catch (error) {
      this.logger.error('获取分类失败:', error);
      throw error;
    }
  }

  // 私有方法实现...
  
  private async validatePublishPermission(digitalHumanId: string, userId: string): Promise<void> {
    // TODO: 实现发布权限验证
  }

  private async generateDigitalHumanPackage(digitalHumanId: string): Promise<DigitalHumanPackage> {
    // TODO: 实现数字人包生成
    return {} as DigitalHumanPackage;
  }

  private async uploadPackageToStorage(packageData: DigitalHumanPackage, publishInfo: PublishInfo): Promise<any> {
    // TODO: 实现包上传到存储服务
    return {};
  }

  private async generatePreviewImages(digitalHumanId: string, previewImages?: File[]): Promise<string[]> {
    // TODO: 实现预览图生成
    return [];
  }

  private async createMarketItem(
    packageData: DigitalHumanPackage,
    publishInfo: PublishInfo,
    uploadResult: any,
    previewUrls: string[]
  ): Promise<MarketplaceItem> {
    // TODO: 实现市场物品创建
    return {} as MarketplaceItem;
  }

  private async submitForReview(item: MarketplaceItem): Promise<any> {
    // TODO: 实现审核提交
    return { status: 'pending', warnings: [] };
  }

  private generateSearchCacheKey(query: SearchQuery): string {
    return JSON.stringify(query);
  }

  private isCacheValid(cachedData: any): boolean {
    return Date.now() - cachedData.timestamp < this.cacheExpiry;
  }

  private buildSearchParams(query: SearchQuery): any {
    return {
      q: query.keyword,
      category: query.category,
      tags: query.tags?.join(','),
      price_min: query.priceRange?.min,
      price_max: query.priceRange?.max,
      license: query.license?.join(','),
      rating: query.rating,
      sort: query.sortBy,
      page: query.page || 1,
      limit: query.limit || 20
    };
  }

  private async callMarketplaceAPI(method: string, endpoint: string, params?: any): Promise<any> {
    // TODO: 实现市场API调用
    return { success: true, data: {} };
  }

  private async validateDownloadPermission(itemId: string, userId: string): Promise<void> {
    // TODO: 实现下载权限验证
  }

  private async generateDownloadUrl(itemId: string, userId: string): Promise<string> {
    // TODO: 实现下载链接生成
    return '';
  }

  private async recordDownloadHistory(itemId: string, userId: string): Promise<void> {
    // TODO: 实现下载历史记录
  }

  private async updateDownloadCount(itemId: string): Promise<void> {
    // TODO: 实现下载计数更新
  }

  /**
   * 获取推荐物品
   */
  public async getRecommendedItems(userId: string, limit: number = 10): Promise<MarketplaceItem[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/users/${userId}/recommendations`, { limit });

      if (!response.success) {
        throw new Error(`获取推荐物品失败: ${response.error}`);
      }

      return response.data.items;

    } catch (error) {
      this.logger.error('获取推荐物品失败:', error);
      throw error;
    }
  }

  /**
   * 添加到收藏
   */
  public async addToFavorites(itemId: string, userId: string): Promise<boolean> {
    try {
      const response = await this.callMarketplaceAPI('POST', `/users/${userId}/favorites`, { itemId });

      if (!response.success) {
        throw new Error(`添加收藏失败: ${response.error}`);
      }

      this.emit('itemFavorited', { itemId, userId });
      return true;

    } catch (error) {
      this.logger.error('添加收藏失败:', error);
      return false;
    }
  }

  /**
   * 从收藏中移除
   */
  public async removeFromFavorites(itemId: string, userId: string): Promise<boolean> {
    try {
      const response = await this.callMarketplaceAPI('DELETE', `/users/${userId}/favorites/${itemId}`);

      if (!response.success) {
        throw new Error(`移除收藏失败: ${response.error}`);
      }

      this.emit('itemUnfavorited', { itemId, userId });
      return true;

    } catch (error) {
      this.logger.error('移除收藏失败:', error);
      return false;
    }
  }

  /**
   * 获取用户收藏列表
   */
  public async getUserFavorites(userId: string, page: number = 1, limit: number = 20): Promise<MarketplaceItem[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/users/${userId}/favorites`, {
        page,
        limit
      });

      if (!response.success) {
        throw new Error(`获取收藏列表失败: ${response.error}`);
      }

      return response.data.items;

    } catch (error) {
      this.logger.error('获取收藏列表失败:', error);
      throw error;
    }
  }

  /**
   * 举报物品
   */
  public async reportItem(itemId: string, userId: string, reason: string, description?: string): Promise<boolean> {
    try {
      const response = await this.callMarketplaceAPI('POST', `/items/${itemId}/reports`, {
        userId,
        reason,
        description
      });

      if (!response.success) {
        throw new Error(`举报失败: ${response.error}`);
      }

      this.emit('itemReported', { itemId, userId, reason });
      return true;

    } catch (error) {
      this.logger.error('举报失败:', error);
      return false;
    }
  }

  /**
   * 获取统计信息
   */
  public async getMarketplaceStatistics(): Promise<any> {
    try {
      const response = await this.callMarketplaceAPI('GET', '/statistics');

      if (!response.success) {
        throw new Error(`获取统计信息失败: ${response.error}`);
      }

      return response.data;

    } catch (error) {
      this.logger.error('获取统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新物品信息
   */
  public async updateMarketplaceItem(
    itemId: string,
    userId: string,
    updateData: Partial<PublishInfo>
  ): Promise<boolean> {
    try {
      // 验证更新权限
      await this.validateUpdatePermission(itemId, userId);

      const response = await this.callMarketplaceAPI('PUT', `/items/${itemId}`, updateData);

      if (!response.success) {
        throw new Error(`更新物品失败: ${response.error}`);
      }

      // 清除缓存
      this.itemCache.delete(itemId);

      this.emit('itemUpdated', { itemId, userId });
      return true;

    } catch (error) {
      this.logger.error('更新物品失败:', error);
      return false;
    }
  }

  /**
   * 删除物品
   */
  public async deleteMarketplaceItem(itemId: string, userId: string): Promise<boolean> {
    try {
      // 验证删除权限
      await this.validateDeletePermission(itemId, userId);

      const response = await this.callMarketplaceAPI('DELETE', `/items/${itemId}`);

      if (!response.success) {
        throw new Error(`删除物品失败: ${response.error}`);
      }

      // 清除缓存
      this.itemCache.delete(itemId);

      this.emit('itemDeleted', { itemId, userId });
      return true;

    } catch (error) {
      this.logger.error('删除物品失败:', error);
      return false;
    }
  }

  /**
   * 获取创作者信息
   */
  public async getCreatorInfo(creatorId: string): Promise<any> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/creators/${creatorId}`);

      if (!response.success) {
        throw new Error(`获取创作者信息失败: ${response.error}`);
      }

      return response.data;

    } catch (error) {
      this.logger.error('获取创作者信息失败:', error);
      throw error;
    }
  }

  /**
   * 关注创作者
   */
  public async followCreator(creatorId: string, userId: string): Promise<boolean> {
    try {
      const response = await this.callMarketplaceAPI('POST', `/users/${userId}/following`, { creatorId });

      if (!response.success) {
        throw new Error(`关注创作者失败: ${response.error}`);
      }

      this.emit('creatorFollowed', { creatorId, userId });
      return true;

    } catch (error) {
      this.logger.error('关注创作者失败:', error);
      return false;
    }
  }

  /**
   * 取消关注创作者
   */
  public async unfollowCreator(creatorId: string, userId: string): Promise<boolean> {
    try {
      const response = await this.callMarketplaceAPI('DELETE', `/users/${userId}/following/${creatorId}`);

      if (!response.success) {
        throw new Error(`取消关注失败: ${response.error}`);
      }

      this.emit('creatorUnfollowed', { creatorId, userId });
      return true;

    } catch (error) {
      this.logger.error('取消关注失败:', error);
      return false;
    }
  }

  /**
   * 获取关注的创作者列表
   */
  public async getFollowingCreators(userId: string): Promise<any[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', `/users/${userId}/following`);

      if (!response.success) {
        throw new Error(`获取关注列表失败: ${response.error}`);
      }

      return response.data.creators;

    } catch (error) {
      this.logger.error('获取关注列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取热门标签
   */
  public async getPopularTags(limit: number = 20): Promise<string[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', '/tags/popular', { limit });

      if (!response.success) {
        throw new Error(`获取热门标签失败: ${response.error}`);
      }

      return response.data.tags;

    } catch (error) {
      this.logger.error('获取热门标签失败:', error);
      throw error;
    }
  }

  /**
   * 搜索建议
   */
  public async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]> {
    try {
      const response = await this.callMarketplaceAPI('GET', '/search/suggestions', {
        q: query,
        limit
      });

      if (!response.success) {
        throw new Error(`获取搜索建议失败: ${response.error}`);
      }

      return response.data.suggestions;

    } catch (error) {
      this.logger.error('获取搜索建议失败:', error);
      throw error;
    }
  }

  /**
   * 验证更新权限
   */
  private async validateUpdatePermission(itemId: string, userId: string): Promise<void> {
    const item = await this.getMarketplaceItem(itemId);
    if (item.creatorId !== userId) {
      throw new Error('没有权限更新此物品');
    }
  }

  /**
   * 验证删除权限
   */
  private async validateDeletePermission(itemId: string, userId: string): Promise<void> {
    const item = await this.getMarketplaceItem(itemId);
    if (item.creatorId !== userId) {
      throw new Error('没有权限删除此物品');
    }
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.itemCache.clear();
    this.searchCache.clear();
    this.emit('cacheCleared');
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): any {
    return {
      itemCacheSize: this.itemCache.size,
      searchCacheSize: this.searchCache.size,
      cacheExpiry: this.cacheExpiry
    };
  }

  /**
   * 设置缓存过期时间
   */
  public setCacheExpiry(expiry: number): void {
    this.cacheExpiry = expiry;
    this.emit('cacheExpiryUpdated', { expiry });
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    this.clearCache();
    this.removeAllListeners();
    this.logger.info('数字人市场服务已销毁');
  }
}
