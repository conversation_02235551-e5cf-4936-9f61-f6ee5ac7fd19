# 基于DL引擎的文本语音场景生成系统使用指南

## 系统概述

基于DL引擎的文本语音场景生成系统是一个智能化的3D场景创建工具，支持通过自然语言描述（文本或语音）快速生成复杂的3D场景。系统集成了先进的AI技术，包括自然语言理解、智能布局生成、资产匹配和多轮对话优化。

## 核心功能

### 1. 文本场景生成
- **自然语言理解**：解析中文场景描述，提取场景元素、空间关系和风格要求
- **智能布局生成**：基于约束求解和空间优化算法生成合理的3D布局
- **资产匹配**：智能匹配合适的3D模型和材质
- **实时预览**：支持输入时的实时场景预览

### 2. 语音场景生成
- **语音识别**：实时语音转文本，支持连续对话
- **语音合成**：智能语音反馈和指导
- **多轮对话**：支持场景的迭代优化和调整
- **语音指导**：提供智能的场景构建建议

### 3. 场景优化
- **多轮对话优化**：基于用户反馈持续改进场景
- **性能优化**：LOD、几何体合并、纹理优化等
- **版本管理**：场景版本历史和回滚功能
- **智能建议**：基于上下文的优化建议

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层                                │
├─────────────────────────────────────────────────────────────┤
│  文本场景生成面板  │  语音场景生成面板  │  场景管理面板      │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    控制器层                                  │
├─────────────────────────────────────────────────────────────┤
│  场景生成AI管理器  │  语音控制器  │  对话优化器  │  性能优化器 │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    AI模型层                                  │
├─────────────────────────────────────────────────────────────┤
│  场景理解模型  │  布局生成模型  │  资产匹配模型  │  NLP处理器  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    引擎层                                    │
├─────────────────────────────────────────────────────────────┤
│  DL引擎  │  场景管理器  │  资产加载器  │  渲染器  │  物理引擎  │
└─────────────────────────────────────────────────────────────┘
```

## 快速开始

### 1. 系统初始化

```typescript
import { SceneGenerationAIManager } from './engine/src/ai/scene';

// 创建场景生成管理器
const sceneManager = new SceneGenerationAIManager({
  debug: true,
  enableRealTimeGeneration: true,
  qualityLevel: 'balanced'
});

// 初始化系统
await sceneManager.initialize();
```

### 2. 文本场景生成

```typescript
// 基本文本生成
const description = "创建一个现代办公室，包含一张大会议桌，周围放置8把椅子，墙上挂着一块白板";
const result = await sceneManager.generateScene(description);

console.log('生成结果:', result);
console.log('置信度:', result.confidence);
```

### 3. 语音场景生成

```typescript
import { VoiceSceneGenerationController } from './engine/src/ai/scene';

// 创建语音控制器
const voiceController = new VoiceSceneGenerationController();

// 启动语音会话
const session = await voiceController.startVoiceSession();

// 监听生成结果
voiceController.on('sceneGenerated', (data) => {
  console.log('语音生成的场景:', data.result);
});
```

### 4. 多轮对话优化

```typescript
import { ConversationalSceneOptimizer } from './engine/src/ai/scene';

const optimizer = new ConversationalSceneOptimizer();

// 处理用户反馈
const feedback = "把桌子移动到房间中央，添加一盆绿植";
const optimizedResult = await optimizer.processFeedback(
  sessionId,
  feedback,
  currentScene
);

console.log('优化结果:', optimizedResult.optimizedScene);
console.log('修改说明:', optimizedResult.explanation);
```

## 详细使用说明

### 场景描述语法

系统支持自然的中文描述，以下是一些有效的描述模式：

#### 基本元素描述
```
"房间里有一张桌子和四把椅子"
"添加一个书架在墙边"
"放置一盏台灯在桌子上"
```

#### 空间关系描述
```
"桌子在房间中央"
"椅子围绕着桌子摆放"
"沙发对面放着电视"
"书架靠着墙壁"
```

#### 风格和属性描述
```
"现代简约风格的客厅"
"红色的布艺沙发"
"木质的会议桌"
"明亮的照明"
```

#### 复合描述
```
"创建一个温馨的客厅，有一套灰色布艺沙发，前面放着一张木质茶几，电视墙采用深色木纹，旁边有一个书架，整体色调温暖舒适"
```

### 支持的场景类型

- **办公空间**：办公室、会议室、工作室
- **居住空间**：客厅、卧室、厨房、餐厅
- **公共空间**：教室、图书馆、展厅、接待室
- **商业空间**：店铺、咖啡厅、餐厅

### 支持的风格

- **现代风格**：简洁、功能性强
- **古典风格**：传统、优雅
- **简约风格**：极简、纯净
- **工业风格**：粗犷、原始
- **北欧风格**：自然、舒适
- **中式风格**：传统中国元素

## 高级功能

### 1. 实时预览

```typescript
// 启用实时预览
const options = {
  realTimePreview: true,
  voiceGuidance: true
};

// 流式生成
const generator = sceneManager.generateSceneStream(description, options);
for await (const step of generator) {
  console.log('生成进度:', step.progress);
  if (step.scene) {
    // 更新预览
    updateScenePreview(step.scene);
  }
}
```

### 2. 性能优化

```typescript
import { ScenePerformanceOptimizer } from './engine/src/ai/scene';

const optimizer = new ScenePerformanceOptimizer({
  targetFPS: 60,
  maxPolygons: 100000,
  enableLOD: true,
  enableGeometryMerging: true
});

// 优化场景
await optimizer.optimizeScene(scene);

// 获取性能统计
const stats = optimizer.getPerformanceStats();
console.log('当前FPS:', stats.currentFPS);
console.log('优化建议:', stats.suggestions);
```

### 3. 场景版本管理

```typescript
// 保存场景版本
const versionId = await sceneManager.saveSceneVersion(scene);

// 获取版本历史
const history = await sceneManager.getVersionHistory(sceneId);

// 回滚到指定版本
const previousScene = await sceneManager.rollbackToVersion(sceneId, versionIndex);
```

## 配置选项

### SceneGenerationAIManager 配置

```typescript
interface SceneGenerationAIManagerConfig {
  // 基础配置
  debug?: boolean;
  enableRealTimeGeneration?: boolean;
  qualityLevel?: 'fast' | 'balanced' | 'high';
  
  // 模型配置
  sceneUnderstanding?: {
    modelType?: 'transformer' | 'bert' | 'gpt';
    language?: string;
    confidenceThreshold?: number;
  };
  
  layoutGeneration?: {
    algorithm?: 'constraint_satisfaction' | 'genetic_algorithm' | 'grid_based';
    maxIterations?: number;
    convergenceThreshold?: number;
  };
  
  assetMatching?: {
    embeddingModel?: string;
    similarityThreshold?: number;
    maxResults?: number;
  };
}
```

### 性能配置

```typescript
interface PerformanceConfig {
  targetFPS: number;           // 目标帧率
  maxPolygons: number;         // 最大多边形数
  maxTextureSize: number;      // 最大纹理尺寸
  enableLOD: boolean;          // 启用LOD
  enableGeometryMerging: boolean;  // 启用几何体合并
  enableTextureCompression: boolean;  // 启用纹理压缩
  enableInstancing: boolean;   // 启用实例化渲染
}
```

## 最佳实践

### 1. 场景描述编写

- **具体明确**：提供详细的对象描述和空间关系
- **结构化**：按照"场景类型 + 主要元素 + 空间关系 + 风格属性"的结构
- **渐进式**：从基本框架开始，逐步添加细节

### 2. 性能优化

- **合理设置质量级别**：根据硬件性能选择合适的质量级别
- **启用LOD**：对于复杂场景，启用LOD以提高性能
- **控制多边形数量**：避免过于复杂的模型

### 3. 多轮对话

- **明确反馈**：提供具体的修改要求
- **逐步调整**：避免一次性提出过多修改要求
- **确认结果**：及时确认修改效果

## 故障排除

### 常见问题

1. **场景生成失败**
   - 检查描述是否过于简单或模糊
   - 确认系统资源是否充足
   - 查看控制台错误信息

2. **语音识别不准确**
   - 确保麦克风权限已开启
   - 检查网络连接
   - 尝试更清晰的发音

3. **性能问题**
   - 降低质量级别
   - 启用性能优化选项
   - 减少场景复杂度

### 调试模式

```typescript
// 启用调试模式
const manager = new SceneGenerationAIManager({
  debug: true
});

// 查看详细日志
manager.on('debug', (info) => {
  console.log('调试信息:', info);
});
```

## API 参考

详细的API文档请参考：
- [SceneGenerationAIManager API](./api/SceneGenerationAIManager.md)
- [VoiceSceneGenerationController API](./api/VoiceSceneGenerationController.md)
- [ConversationalSceneOptimizer API](./api/ConversationalSceneOptimizer.md)
- [ScenePerformanceOptimizer API](./api/ScenePerformanceOptimizer.md)

## 示例项目

完整的示例项目可以在 `examples/scene-generation/` 目录中找到，包括：
- 基础文本生成示例
- 语音交互示例
- 多轮对话示例
- 性能优化示例

## 更新日志

### v1.0.0 (2025-01-14)
- 初始版本发布
- 支持文本和语音场景生成
- 实现多轮对话优化
- 添加性能优化功能
- 完整的测试套件

## 技术支持

如有问题或建议，请联系开发团队或提交Issue到项目仓库。
