import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface LLMOptions {
  temperature?: number;
  maxTokens?: number;
  language?: string;
  model?: string;
  stream?: boolean;
}

export interface LLMResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
}

@Injectable()
export class LLMService {
  private llmClient: LLMClient;

  constructor(private readonly configService: ConfigService) {
    this.initializeLLMClient();
  }

  /**
   * 初始化LLM客户端
   */
  private initializeLLMClient(): void {
    const provider = this.configService.get('llm.provider', 'openai');
    
    switch (provider) {
      case 'openai':
        this.llmClient = new OpenAIClient(this.configService);
        break;
      case 'azure':
        this.llmClient = new AzureOpenAIClient(this.configService);
        break;
      case 'anthropic':
        this.llmClient = new AnthropicClient(this.configService);
        break;
      case 'local':
        this.llmClient = new LocalLLMClient(this.configService);
        break;
      default:
        throw new Error(`不支持的LLM提供商: ${provider}`);
    }
  }

  /**
   * 生成回答
   */
  async generateResponse(prompt: string, options: LLMOptions = {}): Promise<string> {
    try {
      const response = await this.llmClient.generateResponse(prompt, options);
      return response.content;
    } catch (error) {
      console.error('LLM生成回答失败:', error);
      throw new Error(`生成回答失败: ${error.message}`);
    }
  }

  /**
   * 流式生成回答
   */
  async *generateStreamResponse(
    prompt: string,
    options: LLMOptions = {},
  ): AsyncGenerator<string, void, unknown> {
    try {
      yield* this.llmClient.generateStreamResponse(prompt, options);
    } catch (error) {
      console.error('LLM流式生成失败:', error);
      throw new Error(`流式生成失败: ${error.message}`);
    }
  }

  /**
   * 获取模型名称
   */
  getModelName(): string {
    return this.llmClient.getModelName();
  }

  /**
   * 估算token数量
   */
  estimateTokens(text: string): number {
    return this.llmClient.estimateTokens(text);
  }
}

/**
 * LLM客户端接口
 */
interface LLMClient {
  generateResponse(prompt: string, options: LLMOptions): Promise<LLMResponse>;
  generateStreamResponse(prompt: string, options: LLMOptions): AsyncGenerator<string, void, unknown>;
  getModelName(): string;
  estimateTokens(text: string): number;
}

/**
 * OpenAI客户端
 */
class OpenAIClient implements LLMClient {
  private apiKey: string;
  private model: string;
  private baseURL: string;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('openai.apiKey');
    this.model = configService.get('openai.model', 'gpt-3.5-turbo');
    this.baseURL = configService.get('openai.baseURL', 'https://api.openai.com/v1');
  }

  async generateResponse(prompt: string, options: LLMOptions): Promise<LLMResponse> {
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: options.model || this.model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        stream: false,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.statusText}`);
    }

    const data = await response.json();
    const choice = data.choices[0];

    return {
      content: choice.message.content,
      usage: data.usage,
      model: data.model,
      finishReason: choice.finish_reason,
    };
  }

  async *generateStreamResponse(prompt: string, options: LLMOptions): AsyncGenerator<string, void, unknown> {
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: options.model || this.model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        stream: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (error) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  getModelName(): string {
    return this.model;
  }

  estimateTokens(text: string): number {
    // 简单估算：英文按单词计算，中文按字符计算
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }
}

/**
 * Azure OpenAI客户端
 */
class AzureOpenAIClient implements LLMClient {
  private apiKey: string;
  private endpoint: string;
  private deploymentName: string;
  private apiVersion: string;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('azure.apiKey');
    this.endpoint = configService.get('azure.endpoint');
    this.deploymentName = configService.get('azure.deploymentName');
    this.apiVersion = configService.get('azure.apiVersion', '2023-12-01-preview');
  }

  async generateResponse(prompt: string, options: LLMOptions): Promise<LLMResponse> {
    const url = `${this.endpoint}/openai/deployments/${this.deploymentName}/chat/completions?api-version=${this.apiVersion}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'api-key': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`Azure OpenAI API错误: ${response.statusText}`);
    }

    const data = await response.json();
    const choice = data.choices[0];

    return {
      content: choice.message.content,
      usage: data.usage,
      model: this.deploymentName,
      finishReason: choice.finish_reason,
    };
  }

  async *generateStreamResponse(prompt: string, options: LLMOptions): AsyncGenerator<string, void, unknown> {
    // Azure OpenAI流式实现类似OpenAI
    throw new Error('Azure OpenAI流式响应暂未实现');
  }

  getModelName(): string {
    return this.deploymentName;
  }

  estimateTokens(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }
}

/**
 * Anthropic客户端
 */
class AnthropicClient implements LLMClient {
  private apiKey: string;
  private model: string;

  constructor(configService: ConfigService) {
    this.apiKey = configService.get('anthropic.apiKey');
    this.model = configService.get('anthropic.model', 'claude-3-sonnet-20240229');
  }

  async generateResponse(prompt: string, options: LLMOptions): Promise<LLMResponse> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: options.model || this.model,
        max_tokens: options.maxTokens || 1000,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: options.temperature || 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API错误: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      content: data.content[0].text,
      usage: data.usage,
      model: data.model,
      finishReason: data.stop_reason,
    };
  }

  async *generateStreamResponse(prompt: string, options: LLMOptions): AsyncGenerator<string, void, unknown> {
    // Anthropic流式实现
    throw new Error('Anthropic流式响应暂未实现');
  }

  getModelName(): string {
    return this.model;
  }

  estimateTokens(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }
}

/**
 * 本地LLM客户端
 */
class LocalLLMClient implements LLMClient {
  private endpoint: string;
  private model: string;

  constructor(configService: ConfigService) {
    this.endpoint = configService.get('local.endpoint', 'http://localhost:8000');
    this.model = configService.get('local.model', 'local-llm');
  }

  async generateResponse(prompt: string, options: LLMOptions): Promise<LLMResponse> {
    const response = await fetch(`${this.endpoint}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`本地LLM API错误: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      content: data.response,
      usage: {
        promptTokens: this.estimateTokens(prompt),
        completionTokens: this.estimateTokens(data.response),
        totalTokens: this.estimateTokens(prompt) + this.estimateTokens(data.response),
      },
      model: this.model,
      finishReason: 'stop',
    };
  }

  async *generateStreamResponse(prompt: string, options: LLMOptions): AsyncGenerator<string, void, unknown> {
    // 本地LLM流式实现
    throw new Error('本地LLM流式响应暂未实现');
  }

  getModelName(): string {
    return this.model;
  }

  estimateTokens(text: string): number {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }
}
