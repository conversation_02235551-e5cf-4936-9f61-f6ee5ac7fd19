/**
 * 资产匹配模型
 * 负责根据场景元素智能匹配合适的3D资产
 */
import { IAIModel } from '../models/IAIModel';
import { AIModelConfig } from '../AIModelConfig';
import { AIModelManagerConfig } from '../AIModelManager';
import {
  AssetMatchResult,
  SceneElement,
  SceneUnderstanding,
  SceneLayout
} from './SceneGenerationTypes';

/**
 * 资产信息
 */
export interface Asset {
  /** 资产ID */
  id: string;
  /** 资产名称 */
  name: string;
  /** 资产类型 */
  type: string;
  /** 资产分类 */
  category: string;
  /** 资产描述 */
  description: string;
  /** 资产标签 */
  tags: string[];
  /** 资产URL */
  url: string;
  /** 资产元数据 */
  metadata: {
    /** 尺寸信息 */
    dimensions?: { width: number; height: number; depth: number };
    /** 风格 */
    style?: string;
    /** 颜色 */
    color?: string;
    /** 材质 */
    material?: string;
    /** 多边形数量 */
    polygonCount?: number;
    /** 纹理信息 */
    textures?: string[];
  };
  /** 嵌入向量 */
  embedding?: number[];
  /** 置信度 */
  confidence?: number;
}

/**
 * 资产匹配模型配置
 */
export interface AssetMatchingConfig extends AIModelConfig {
  /** 嵌入模型 */
  embeddingModel?: string;
  /** 相似度阈值 */
  similarityThreshold?: number;
  /** 最大结果数 */
  maxResults?: number;
  /** 是否启用风格匹配 */
  enableStyleMatching?: boolean;
  /** 是否启用尺寸适配 */
  enableSizeAdaptation?: boolean;
  /** 是否启用程序化生成 */
  enableProceduralGeneration?: boolean;
}

/**
 * 资产匹配模型
 */
export class AssetMatchingModel implements IAIModel {
  private config: AssetMatchingConfig;
  private managerConfig: AIModelManagerConfig;
  private initialized: boolean = false;
  private assetDatabase: AssetDatabase;
  private embeddingModel: EmbeddingModel;
  private styleClassifier: StyleClassifier;
  private proceduralGenerator: ProceduralAssetGenerator;

  constructor(config: AssetMatchingConfig, managerConfig: AIModelManagerConfig) {
    this.config = {
      embeddingModel: 'sentence-transformers',
      similarityThreshold: 0.7,
      maxResults: 10,
      enableStyleMatching: true,
      enableSizeAdaptation: true,
      enableProceduralGeneration: false,
      ...config
    };
    this.managerConfig = managerConfig;
    this.assetDatabase = new AssetDatabase();
    this.embeddingModel = new EmbeddingModel(this.config.embeddingModel!);
    this.styleClassifier = new StyleClassifier();
    this.proceduralGenerator = new ProceduralAssetGenerator();
  }

  /**
   * 初始化模型
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      await this.assetDatabase.initialize();
      await this.embeddingModel.initialize();
      await this.styleClassifier.initialize();
      
      if (this.config.enableProceduralGeneration) {
        await this.proceduralGenerator.initialize();
      }
      
      this.initialized = true;
      
      if (this.managerConfig.debug) {
        console.log('资产匹配模型初始化成功');
      }
    } catch (error) {
      console.error('资产匹配模型初始化失败:', error);
      throw error;
    }
  }

  /**
   * 匹配场景资产
   */
  async matchAssets(understanding: SceneUnderstanding, layout: SceneLayout): Promise<AssetMatchResult[]> {
    if (!this.initialized) {
      throw new Error('资产匹配模型未初始化');
    }

    const results: AssetMatchResult[] = [];

    for (const layoutElement of layout.elements) {
      try {
        const matchResult = await this.matchSingleAsset(layoutElement.element, understanding);
        if (matchResult) {
          results.push(matchResult);
        }
      } catch (error) {
        console.warn(`匹配资产失败: ${layoutElement.element.name}`, error);
        
        // 如果匹配失败，尝试生成默认资产
        const fallbackAsset = await this.generateFallbackAsset(layoutElement.element);
        if (fallbackAsset) {
          results.push({
            element: layoutElement.element,
            asset: fallbackAsset,
            confidence: 0.3,
            source: 'generated'
          });
        }
      }
    }

    return results;
  }

  /**
   * 匹配单个资产
   */
  private async matchSingleAsset(element: SceneElement, understanding: SceneUnderstanding): Promise<AssetMatchResult | null> {
    // 1. 语义搜索
    const semanticMatches = await this.semanticSearch(element);
    
    if (semanticMatches.length === 0) {
      // 如果没有语义匹配结果，尝试程序化生成
      if (this.config.enableProceduralGeneration) {
        const generatedAsset = await this.proceduralGenerator.generate(element);
        if (generatedAsset) {
          return {
            element,
            asset: generatedAsset,
            confidence: 0.6,
            source: 'procedural'
          };
        }
      }
      return null;
    }

    // 2. 风格过滤
    let filteredMatches = semanticMatches;
    if (this.config.enableStyleMatching) {
      filteredMatches = await this.filterByStyle(semanticMatches, understanding.intent.style);
    }

    // 3. 尺寸适配
    if (this.config.enableSizeAdaptation && element.requiredSize) {
      filteredMatches = await this.adaptSize(filteredMatches, element.requiredSize);
    }

    // 4. 选择最佳匹配
    const bestMatch = this.selectBestMatch(filteredMatches, element);
    
    if (bestMatch) {
      return {
        element,
        asset: bestMatch,
        confidence: bestMatch.confidence || 0.8,
        source: 'database'
      };
    }

    return null;
  }

  /**
   * 语义搜索
   */
  private async semanticSearch(element: SceneElement): Promise<Asset[]> {
    // 构建搜索查询
    const query = this.buildSearchQuery(element);
    
    // 生成查询向量
    const queryEmbedding = await this.embeddingModel.embed(query);
    
    // 在资产数据库中搜索
    const results = await this.assetDatabase.vectorSearch(queryEmbedding, {
      topK: this.config.maxResults!,
      threshold: this.config.similarityThreshold!,
      category: element.category
    });

    return results;
  }

  /**
   * 构建搜索查询
   */
  private buildSearchQuery(element: SceneElement): string {
    let query = element.name;
    
    if (element.description) {
      query += ` ${element.description}`;
    }
    
    // 添加属性信息
    if (element.attributes.color) {
      query += ` ${element.attributes.color}`;
    }
    
    if (element.attributes.material) {
      query += ` ${element.attributes.material}`;
    }
    
    if (element.attributes.style) {
      query += ` ${element.attributes.style}`;
    }
    
    return query;
  }

  /**
   * 按风格过滤
   */
  private async filterByStyle(assets: Asset[], targetStyle: string): Promise<Asset[]> {
    const filteredAssets: Asset[] = [];
    
    for (const asset of assets) {
      const styleCompatibility = await this.styleClassifier.checkCompatibility(asset, targetStyle);
      if (styleCompatibility > 0.5) {
        asset.confidence = (asset.confidence || 1.0) * styleCompatibility;
        filteredAssets.push(asset);
      }
    }
    
    return filteredAssets.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
  }

  /**
   * 尺寸适配
   */
  private async adaptSize(assets: Asset[], requiredSize: any): Promise<Asset[]> {
    return assets.map(asset => {
      if (asset.metadata.dimensions) {
        const sizeCompatibility = this.calculateSizeCompatibility(asset.metadata.dimensions, requiredSize);
        asset.confidence = (asset.confidence || 1.0) * sizeCompatibility;
      }
      return asset;
    }).filter(asset => (asset.confidence || 0) > 0.3);
  }

  /**
   * 计算尺寸兼容性
   */
  private calculateSizeCompatibility(assetDimensions: any, requiredSize: any): number {
    // 简化的尺寸兼容性计算
    const assetVolume = assetDimensions.width * assetDimensions.height * assetDimensions.depth;
    const requiredVolume = requiredSize.x * requiredSize.y * requiredSize.z;
    
    const ratio = Math.min(assetVolume, requiredVolume) / Math.max(assetVolume, requiredVolume);
    return Math.max(0, ratio);
  }

  /**
   * 选择最佳匹配
   */
  private selectBestMatch(assets: Asset[], element: SceneElement): Asset | null {
    if (assets.length === 0) {
      return null;
    }

    // 按置信度排序
    const sortedAssets = assets.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));
    
    // 应用额外的选择逻辑
    for (const asset of sortedAssets) {
      if (this.isAssetSuitable(asset, element)) {
        return asset;
      }
    }
    
    // 如果没有完全合适的，返回置信度最高的
    return sortedAssets[0];
  }

  /**
   * 检查资产是否合适
   */
  private isAssetSuitable(asset: Asset, element: SceneElement): boolean {
    // 检查分类匹配
    if (asset.category !== element.category) {
      return false;
    }
    
    // 检查置信度
    if ((asset.confidence || 0) < this.config.similarityThreshold!) {
      return false;
    }
    
    // 检查特定属性匹配
    if (element.attributes.color && asset.metadata.color) {
      if (element.attributes.color !== asset.metadata.color) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 生成备用资产
   */
  private async generateFallbackAsset(element: SceneElement): Promise<Asset | null> {
    // 生成简单的几何体作为备用
    const fallbackAsset: Asset = {
      id: `fallback_${Date.now()}`,
      name: `Default ${element.name}`,
      type: 'primitive',
      category: element.category,
      description: `Default primitive for ${element.name}`,
      tags: ['default', 'primitive'],
      url: this.generatePrimitiveUrl(element),
      metadata: {
        dimensions: { width: 1, height: 1, depth: 1 },
        style: 'basic',
        polygonCount: 12
      }
    };
    
    return fallbackAsset;
  }

  /**
   * 生成基础几何体URL
   */
  private generatePrimitiveUrl(element: SceneElement): string {
    const primitiveMap: Record<string, string> = {
      'furniture': 'primitive://box',
      'lighting': 'primitive://sphere',
      'electronics': 'primitive://box',
      'decoration': 'primitive://cylinder'
    };
    
    return primitiveMap[element.category] || 'primitive://box';
  }

  /**
   * 销毁模型
   */
  async destroy(): Promise<void> {
    this.initialized = false;
  }

  /**
   * 获取模型信息
   */
  getModelInfo(): any {
    return {
      type: 'asset_matching',
      config: this.config,
      initialized: this.initialized
    };
  }
}

/**
 * 资产数据库
 */
class AssetDatabase {
  private assets: Map<string, Asset> = new Map();

  async initialize(): Promise<void> {
    // 初始化资产数据库
    await this.loadAssets();
  }

  private async loadAssets(): Promise<void> {
    // 加载预定义资产
    const defaultAssets: Asset[] = [
      {
        id: 'table_001',
        name: '办公桌',
        type: 'furniture',
        category: 'furniture',
        description: '现代办公桌',
        tags: ['办公', '桌子', '现代'],
        url: '/assets/models/table_office_001.glb',
        metadata: {
          dimensions: { width: 1.2, height: 0.75, depth: 0.6 },
          style: 'modern',
          color: 'brown',
          material: 'wood'
        }
      },
      {
        id: 'chair_001',
        name: '办公椅',
        type: 'furniture',
        category: 'furniture',
        description: '人体工学办公椅',
        tags: ['办公', '椅子', '现代'],
        url: '/assets/models/chair_office_001.glb',
        metadata: {
          dimensions: { width: 0.6, height: 1.2, depth: 0.6 },
          style: 'modern',
          color: 'black',
          material: 'fabric'
        }
      }
    ];

    defaultAssets.forEach(asset => {
      this.assets.set(asset.id, asset);
    });
  }

  async vectorSearch(embedding: number[], options: any): Promise<Asset[]> {
    // 简化的向量搜索实现
    const results: Asset[] = [];
    
    this.assets.forEach(asset => {
      if (!options.category || asset.category === options.category) {
        // 简化的相似度计算
        asset.confidence = Math.random() * 0.5 + 0.5;
        if (asset.confidence >= options.threshold) {
          results.push(asset);
        }
      }
    });
    
    return results
      .sort((a, b) => (b.confidence || 0) - (a.confidence || 0))
      .slice(0, options.topK);
  }
}

/**
 * 嵌入模型
 */
class EmbeddingModel {
  constructor(private modelName: string) {}

  async initialize(): Promise<void> {
    // 初始化嵌入模型
  }

  async embed(text: string): Promise<number[]> {
    // 简化的嵌入实现
    return Array(384).fill(0).map(() => Math.random());
  }
}

/**
 * 风格分类器
 */
class StyleClassifier {
  async initialize(): Promise<void> {
    // 初始化风格分类器
  }

  async checkCompatibility(asset: Asset, targetStyle: string): Promise<number> {
    // 简化的风格兼容性检查
    if (asset.metadata.style === targetStyle) {
      return 1.0;
    }
    
    const styleCompatibility: Record<string, Record<string, number>> = {
      'modern': { 'contemporary': 0.8, 'minimalist': 0.7 },
      'classical': { 'traditional': 0.9, 'vintage': 0.8 },
      'minimalist': { 'modern': 0.7, 'contemporary': 0.6 }
    };
    
    return styleCompatibility[asset.metadata.style || '']?.[targetStyle] || 0.3;
  }
}

/**
 * 程序化资产生成器
 */
class ProceduralAssetGenerator {
  async initialize(): Promise<void> {
    // 初始化程序化生成器
  }

  async generate(element: SceneElement): Promise<Asset | null> {
    // 程序化生成资产
    return null;
  }
}
