/**
 * 语音合成服务
 * 集成高质量TTS服务，支持情感化语音合成、数字人口型同步、多语言语音输出
 */

import { EmotionType } from '../dialogue/DialogueManager';

/**
 * 语音合成配置接口
 */
export interface SpeechSynthesisConfig {
  language: string;
  voice?: string;
  rate: number;
  pitch: number;
  volume: number;
  provider: 'web-speech' | 'azure' | 'google' | 'baidu' | 'iflytek';
  apiKey?: string;
  region?: string;
  enableEmotionalSynthesis: boolean;
  enableLipSync: boolean;
  audioFormat: 'mp3' | 'wav' | 'ogg';
  sampleRate: number;
}

/**
 * 语音合成结果接口
 */
export interface SpeechSynthesisResult {
  audioUrl?: string;
  audioBuffer?: ArrayBuffer;
  duration: number;
  text: string;
  language: string;
  voice: string;
  lipSyncData?: LipSyncData[];
  timestamp: Date;
}

/**
 * 口型同步数据接口
 */
export interface LipSyncData {
  time: number;
  phoneme: string;
  viseme: string;
  intensity: number;
}

/**
 * 语音参数接口
 */
export interface VoiceParameters {
  rate: number;
  pitch: number;
  volume: number;
  emotion?: EmotionType;
  emotionIntensity?: number;
  emphasis?: string[];
  pauses?: { position: number; duration: number }[];
}

/**
 * 语音合成状态枚举
 */
export enum SpeechSynthesisState {
  IDLE = 'idle',
  SYNTHESIZING = 'synthesizing',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ERROR = 'error',
  COMPLETED = 'completed'
}

/**
 * Web Speech API 语音合成实现
 */
export class WebSpeechSynthesis {
  private synthesis: SpeechSynthesis;
  private voices: SpeechSynthesisVoice[] = [];
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private config: SpeechSynthesisConfig;
  private state: SpeechSynthesisState = SpeechSynthesisState.IDLE;

  // 事件回调
  public onStart?: () => void;
  public onEnd?: () => void;
  public onError?: (error: string) => void;
  public onPause?: () => void;
  public onResume?: () => void;
  public onStateChange?: (state: SpeechSynthesisState) => void;

  constructor(config: SpeechSynthesisConfig) {
    this.config = config;
    this.synthesis = window.speechSynthesis;
    this.loadVoices();
  }

  /**
   * 加载可用语音
   */
  private loadVoices(): void {
    const loadVoicesImpl = () => {
      this.voices = this.synthesis.getVoices();
      console.log(`加载了 ${this.voices.length} 个语音`);
    };

    loadVoicesImpl();

    // 某些浏览器需要异步加载语音
    if (this.voices.length === 0) {
      this.synthesis.onvoiceschanged = () => {
        loadVoicesImpl();
      };
    }
  }

  /**
   * 获取可用语音列表
   */
  public getVoices(): SpeechSynthesisVoice[] {
    return this.voices;
  }

  /**
   * 根据语言获取语音
   */
  public getVoicesByLanguage(language: string): SpeechSynthesisVoice[] {
    return this.voices.filter(voice => voice.lang.startsWith(language));
  }

  /**
   * 选择最佳语音
   */
  private selectBestVoice(language: string, preferredVoice?: string): SpeechSynthesisVoice | null {
    // 如果指定了语音名称，优先使用
    if (preferredVoice) {
      const voice = this.voices.find(v => v.name === preferredVoice);
      if (voice) return voice;
    }

    // 按语言筛选
    const languageVoices = this.getVoicesByLanguage(language);
    if (languageVoices.length === 0) return null;

    // 优先选择本地语音
    const localVoices = languageVoices.filter(v => v.localService);
    if (localVoices.length > 0) {
      return localVoices[0];
    }

    return languageVoices[0];
  }

  /**
   * 语音合成
   */
  public async synthesize(
    text: string,
    parameters?: VoiceParameters
  ): Promise<SpeechSynthesisResult> {
    return new Promise((resolve, reject) => {
      if (this.state === SpeechSynthesisState.SYNTHESIZING || 
          this.state === SpeechSynthesisState.PLAYING) {
        reject(new Error('语音合成正在进行中'));
        return;
      }

      // 处理文本
      const processedText = this.preprocessText(text, parameters);

      // 创建语音合成实例
      this.currentUtterance = new SpeechSynthesisUtterance(processedText);

      // 选择语音
      const voice = this.selectBestVoice(this.config.language, this.config.voice);
      if (voice) {
        this.currentUtterance.voice = voice;
      }

      // 设置参数
      this.applyVoiceParameters(this.currentUtterance, parameters);

      // 设置事件监听器
      this.currentUtterance.onstart = () => {
        this.setState(SpeechSynthesisState.PLAYING);
        if (this.onStart) this.onStart();
      };

      this.currentUtterance.onend = () => {
        this.setState(SpeechSynthesisState.COMPLETED);
        if (this.onEnd) this.onEnd();

        const result: SpeechSynthesisResult = {
          duration: 0, // Web Speech API 不提供持续时间
          text: processedText,
          language: this.config.language,
          voice: voice?.name || 'default',
          timestamp: new Date()
        };

        resolve(result);
      };

      this.currentUtterance.onerror = (event) => {
        this.setState(SpeechSynthesisState.ERROR);
        const errorMessage = `语音合成错误: ${event.error}`;
        if (this.onError) this.onError(errorMessage);
        reject(new Error(errorMessage));
      };

      this.currentUtterance.onpause = () => {
        this.setState(SpeechSynthesisState.PAUSED);
        if (this.onPause) this.onPause();
      };

      this.currentUtterance.onresume = () => {
        this.setState(SpeechSynthesisState.PLAYING);
        if (this.onResume) this.onResume();
      };

      // 开始合成
      this.setState(SpeechSynthesisState.SYNTHESIZING);
      this.synthesis.speak(this.currentUtterance);
    });
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string, parameters?: VoiceParameters): string {
    let processedText = text;

    // 添加停顿
    if (parameters?.pauses) {
      for (const pause of parameters.pauses.reverse()) {
        const pauseMarkup = this.createPauseMarkup(pause.duration);
        processedText = processedText.slice(0, pause.position) + 
                      pauseMarkup + 
                      processedText.slice(pause.position);
      }
    }

    // 添加重音
    if (parameters?.emphasis) {
      for (const word of parameters.emphasis) {
        const emphasisRegex = new RegExp(`\\b${word}\\b`, 'gi');
        processedText = processedText.replace(emphasisRegex, this.createEmphasisMarkup(word));
      }
    }

    return processedText;
  }

  /**
   * 创建停顿标记
   */
  private createPauseMarkup(duration: number): string {
    // Web Speech API 使用逗号和句号来控制停顿
    if (duration < 500) return ', ';
    if (duration < 1000) return '. ';
    return '... ';
  }

  /**
   * 创建重音标记
   */
  private createEmphasisMarkup(word: string): string {
    // Web Speech API 没有标准的重音标记，使用大写来模拟
    return word.toUpperCase();
  }

  /**
   * 应用语音参数
   */
  private applyVoiceParameters(
    utterance: SpeechSynthesisUtterance,
    parameters?: VoiceParameters
  ): void {
    // 基础参数
    utterance.rate = parameters?.rate ?? this.config.rate;
    utterance.pitch = parameters?.pitch ?? this.config.pitch;
    utterance.volume = parameters?.volume ?? this.config.volume;

    // 情感调整
    if (parameters?.emotion && parameters?.emotionIntensity) {
      this.applyEmotionalParameters(utterance, parameters.emotion, parameters.emotionIntensity);
    }
  }

  /**
   * 应用情感参数
   */
  private applyEmotionalParameters(
    utterance: SpeechSynthesisUtterance,
    emotion: EmotionType,
    intensity: number
  ): void {
    const baseRate = this.config.rate;
    const basePitch = this.config.pitch;

    switch (emotion) {
      case EmotionType.EXCITED:
        utterance.rate = baseRate * (1 + intensity * 0.3);
        utterance.pitch = basePitch * (1 + intensity * 0.4);
        break;
      
      case EmotionType.POSITIVE:
        utterance.rate = baseRate * (1 + intensity * 0.1);
        utterance.pitch = basePitch * (1 + intensity * 0.2);
        break;
      
      case EmotionType.NEGATIVE:
        utterance.rate = baseRate * (1 - intensity * 0.2);
        utterance.pitch = basePitch * (1 - intensity * 0.3);
        break;
      
      case EmotionType.CONFUSED:
        utterance.rate = baseRate * (1 - intensity * 0.1);
        utterance.pitch = basePitch * (1 + intensity * 0.1);
        break;
      
      case EmotionType.FRUSTRATED:
        utterance.rate = baseRate * (1 + intensity * 0.2);
        utterance.pitch = basePitch * (1 - intensity * 0.1);
        break;
      
      case EmotionType.SATISFIED:
        utterance.rate = baseRate * (1 - intensity * 0.05);
        utterance.pitch = basePitch * (1 + intensity * 0.1);
        break;
      
      default:
        // NEUTRAL - 保持默认参数
        break;
    }

    // 确保参数在合理范围内
    utterance.rate = Math.max(0.1, Math.min(10, utterance.rate));
    utterance.pitch = Math.max(0, Math.min(2, utterance.pitch));
  }

  /**
   * 停止语音合成
   */
  public stop(): void {
    if (this.synthesis.speaking) {
      this.synthesis.cancel();
      this.setState(SpeechSynthesisState.IDLE);
    }
  }

  /**
   * 暂停语音合成
   */
  public pause(): void {
    if (this.synthesis.speaking && !this.synthesis.paused) {
      this.synthesis.pause();
    }
  }

  /**
   * 恢复语音合成
   */
  public resume(): void {
    if (this.synthesis.paused) {
      this.synthesis.resume();
    }
  }

  /**
   * 设置状态
   */
  private setState(newState: SpeechSynthesisState): void {
    if (this.state !== newState) {
      this.state = newState;
      if (this.onStateChange) {
        this.onStateChange(newState);
      }
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): SpeechSynthesisState {
    return this.state;
  }

  /**
   * 检查是否正在播放
   */
  public isSpeaking(): boolean {
    return this.synthesis.speaking;
  }

  /**
   * 检查是否暂停
   */
  public isPaused(): boolean {
    return this.synthesis.paused;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<SpeechSynthesisConfig>): void {
    Object.assign(this.config, config);
  }
}

/**
 * 口型同步生成器
 */
export class LipSyncGenerator {
  /**
   * 生成口型同步数据
   */
  public static generateLipSyncData(text: string, duration: number): LipSyncData[] {
    const lipSyncData: LipSyncData[] = [];
    const words = text.split(/\s+/);
    const timePerWord = duration / words.length;

    let currentTime = 0;

    for (const word of words) {
      const phonemes = this.textToPhonemes(word);
      const timePerPhoneme = timePerWord / phonemes.length;

      for (const phoneme of phonemes) {
        const viseme = this.phonemeToViseme(phoneme);
        const intensity = this.calculateIntensity(phoneme);

        lipSyncData.push({
          time: currentTime,
          phoneme,
          viseme,
          intensity
        });

        currentTime += timePerPhoneme;
      }
    }

    return lipSyncData;
  }

  /**
   * 文本转音素（简化实现）
   */
  private static textToPhonemes(word: string): string[] {
    // 简化的音素映射（实际应该使用专业的音素转换库）
    const phonemeMap: Record<string, string[]> = {
      'a': ['AH'],
      'e': ['EH'],
      'i': ['IH'],
      'o': ['OH'],
      'u': ['UH'],
      'b': ['B'],
      'p': ['P'],
      'm': ['M'],
      'f': ['F'],
      'v': ['V'],
      't': ['T'],
      'd': ['D'],
      'n': ['N'],
      'l': ['L'],
      'r': ['R'],
      's': ['S'],
      'z': ['Z'],
      'k': ['K'],
      'g': ['G'],
      'h': ['HH']
    };

    const phonemes: string[] = [];
    for (const char of word.toLowerCase()) {
      const phoneme = phonemeMap[char];
      if (phoneme) {
        phonemes.push(...phoneme);
      }
    }

    return phonemes.length > 0 ? phonemes : ['SIL']; // 静音
  }

  /**
   * 音素转视素
   */
  private static phonemeToViseme(phoneme: string): string {
    const visemeMap: Record<string, string> = {
      'SIL': 'sil',     // 静音
      'AH': 'a',        // 开口音
      'EH': 'e',        // 中开音
      'IH': 'i',        // 闭口音
      'OH': 'o',        // 圆唇音
      'UH': 'u',        // 圆唇闭音
      'B': 'p',         // 双唇音
      'P': 'p',
      'M': 'p',
      'F': 'f',         // 唇齿音
      'V': 'f',
      'T': 't',         // 舌尖音
      'D': 't',
      'N': 't',
      'L': 't',
      'R': 'r',         // 卷舌音
      'S': 's',         // 齿音
      'Z': 's',
      'K': 'k',         // 舌根音
      'G': 'k',
      'HH': 'h'         // 气音
    };

    return visemeMap[phoneme] || 'sil';
  }

  /**
   * 计算音素强度
   */
  private static calculateIntensity(phoneme: string): number {
    const intensityMap: Record<string, number> = {
      'SIL': 0.0,
      'AH': 0.8,
      'EH': 0.7,
      'IH': 0.6,
      'OH': 0.8,
      'UH': 0.7,
      'B': 0.9,
      'P': 0.9,
      'M': 0.8,
      'F': 0.7,
      'V': 0.7,
      'T': 0.8,
      'D': 0.8,
      'N': 0.7,
      'L': 0.6,
      'R': 0.7,
      'S': 0.8,
      'Z': 0.8,
      'K': 0.9,
      'G': 0.9,
      'HH': 0.5
    };

    return intensityMap[phoneme] || 0.5;
  }
}

/**
 * 语音合成服务主类
 */
export class SpeechSynthesisService {
  private webSpeechSynthesis: WebSpeechSynthesis;
  private config: SpeechSynthesisConfig;
  private currentAudio: HTMLAudioElement | null = null;

  // 事件回调
  public onSpeechStart?: () => void;
  public onSpeechEnd?: () => void;
  public onSpeechError?: (error: string) => void;
  public onLipSyncUpdate?: (lipSyncData: LipSyncData) => void;

  constructor(config: SpeechSynthesisConfig) {
    this.config = config;
    this.webSpeechSynthesis = new WebSpeechSynthesis(config);
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.webSpeechSynthesis.onStart = () => {
      if (this.onSpeechStart) {
        this.onSpeechStart();
      }
    };

    this.webSpeechSynthesis.onEnd = () => {
      if (this.onSpeechEnd) {
        this.onSpeechEnd();
      }
    };

    this.webSpeechSynthesis.onError = (error) => {
      if (this.onSpeechError) {
        this.onSpeechError(error);
      }
    };
  }

  /**
   * 语音合成
   */
  public async speak(
    text: string,
    emotion?: EmotionType,
    emotionIntensity?: number
  ): Promise<SpeechSynthesisResult> {
    console.log(`开始语音合成: ${text}`);

    const parameters: VoiceParameters = {
      rate: this.config.rate,
      pitch: this.config.pitch,
      volume: this.config.volume,
      emotion,
      emotionIntensity
    };

    const result = await this.webSpeechSynthesis.synthesize(text, parameters);

    // 生成口型同步数据
    if (this.config.enableLipSync) {
      result.lipSyncData = LipSyncGenerator.generateLipSyncData(text, result.duration || 3000);
      this.playLipSyncAnimation(result.lipSyncData);
    }

    return result;
  }

  /**
   * 播放口型同步动画
   */
  private playLipSyncAnimation(lipSyncData: LipSyncData[]): void {
    if (!this.onLipSyncUpdate) return;

    let currentIndex = 0;
    const startTime = Date.now();

    const updateLipSync = () => {
      if (currentIndex >= lipSyncData.length) return;

      const currentTime = Date.now() - startTime;
      const currentData = lipSyncData[currentIndex];

      if (currentTime >= currentData.time) {
        this.onLipSyncUpdate!(currentData);
        currentIndex++;
      }

      if (currentIndex < lipSyncData.length) {
        requestAnimationFrame(updateLipSync);
      }
    };

    requestAnimationFrame(updateLipSync);
  }

  /**
   * 停止语音
   */
  public stop(): void {
    this.webSpeechSynthesis.stop();

    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
    }
  }

  /**
   * 暂停语音
   */
  public pause(): void {
    this.webSpeechSynthesis.pause();

    if (this.currentAudio) {
      this.currentAudio.pause();
    }
  }

  /**
   * 恢复语音
   */
  public resume(): void {
    this.webSpeechSynthesis.resume();

    if (this.currentAudio) {
      this.currentAudio.play();
    }
  }

  /**
   * 获取可用语音列表
   */
  public getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.webSpeechSynthesis.getVoices();
  }

  /**
   * 根据语言获取语音
   */
  public getVoicesByLanguage(language: string): SpeechSynthesisVoice[] {
    return this.webSpeechSynthesis.getVoicesByLanguage(language);
  }

  /**
   * 设置语音
   */
  public setVoice(voiceName: string): void {
    this.config.voice = voiceName;
    this.webSpeechSynthesis.updateConfig({ voice: voiceName });
  }

  /**
   * 设置语言
   */
  public setLanguage(language: string): void {
    this.config.language = language;
    this.webSpeechSynthesis.updateConfig({ language });
  }

  /**
   * 设置语音参数
   */
  public setVoiceParameters(rate: number, pitch: number, volume: number): void {
    this.config.rate = rate;
    this.config.pitch = pitch;
    this.config.volume = volume;

    this.webSpeechSynthesis.updateConfig({ rate, pitch, volume });
  }

  /**
   * 检查是否正在播放
   */
  public isSpeaking(): boolean {
    return this.webSpeechSynthesis.isSpeaking();
  }

  /**
   * 检查是否暂停
   */
  public isPaused(): boolean {
    return this.webSpeechSynthesis.isPaused();
  }

  /**
   * 获取当前状态
   */
  public getState(): SpeechSynthesisState {
    return this.webSpeechSynthesis.getState();
  }

  /**
   * 预处理文本以优化语音效果
   */
  public preprocessTextForSpeech(text: string): string {
    let processedText = text;

    // 处理数字
    processedText = processedText.replace(/\d+/g, (match) => {
      return this.numberToWords(parseInt(match));
    });

    // 处理标点符号
    processedText = processedText.replace(/[。！？]/g, '。 ');
    processedText = processedText.replace(/[，、]/g, '， ');

    // 处理英文单词
    processedText = processedText.replace(/[a-zA-Z]+/g, (match) => {
      return ` ${match} `;
    });

    // 清理多余空格
    processedText = processedText.replace(/\s+/g, ' ').trim();

    return processedText;
  }

  /**
   * 数字转文字
   */
  private numberToWords(num: number): string {
    const units = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const tens = ['', '十', '二十', '三十', '四十', '五十', '六十', '七十', '八十', '九十'];

    if (num === 0) return '零';
    if (num < 10) return units[num];
    if (num < 100) {
      const ten = Math.floor(num / 10);
      const unit = num % 10;
      return tens[ten] + (unit > 0 ? units[unit] : '');
    }

    // 简化处理，实际应该支持更大的数字
    return num.toString();
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return [
      'zh-CN', // 中文（简体）
      'zh-TW', // 中文（繁体）
      'en-US', // 英语（美国）
      'en-GB', // 英语（英国）
      'ja-JP', // 日语
      'ko-KR', // 韩语
      'fr-FR', // 法语
      'de-DE', // 德语
      'es-ES', // 西班牙语
      'it-IT', // 意大利语
      'pt-BR', // 葡萄牙语（巴西）
      'ru-RU', // 俄语
      'ar-SA', // 阿拉伯语
      'hi-IN', // 印地语
      'th-TH'  // 泰语
    ];
  }

  /**
   * 获取配置
   */
  public getConfig(): SpeechSynthesisConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<SpeechSynthesisConfig>): void {
    Object.assign(this.config, config);
    this.webSpeechSynthesis.updateConfig(config);
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): any {
    return {
      availableVoices: this.getAvailableVoices().length,
      currentLanguage: this.config.language,
      currentVoice: this.config.voice,
      isSpeaking: this.isSpeaking(),
      isPaused: this.isPaused(),
      state: this.getState(),
      config: this.getConfig()
    };
  }

  /**
   * 销毁服务
   */
  public dispose(): void {
    this.stop();

    // 清理事件回调
    this.onSpeechStart = undefined;
    this.onSpeechEnd = undefined;
    this.onSpeechError = undefined;
    this.onLipSyncUpdate = undefined;

    console.log('语音合成服务已销毁');
  }
}

/**
 * 语音合成工厂类
 */
export class SpeechSynthesisFactory {
  /**
   * 创建语音合成服务
   */
  public static createService(config?: Partial<SpeechSynthesisConfig>): SpeechSynthesisService {
    const defaultConfig: SpeechSynthesisConfig = {
      language: 'zh-CN',
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
      provider: 'web-speech',
      enableEmotionalSynthesis: true,
      enableLipSync: true,
      audioFormat: 'mp3',
      sampleRate: 22050
    };

    const finalConfig = { ...defaultConfig, ...config };
    return new SpeechSynthesisService(finalConfig);
  }

  /**
   * 获取推荐配置
   */
  public static getRecommendedConfig(
    scenario: 'exhibition' | 'conversation' | 'announcement'
  ): SpeechSynthesisConfig {
    const baseConfig: SpeechSynthesisConfig = {
      language: 'zh-CN',
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
      provider: 'web-speech',
      enableEmotionalSynthesis: true,
      enableLipSync: true,
      audioFormat: 'mp3',
      sampleRate: 22050
    };

    switch (scenario) {
      case 'exhibition':
        return {
          ...baseConfig,
          rate: 0.9,
          pitch: 1.1,
          enableEmotionalSynthesis: true,
          enableLipSync: true
        };

      case 'conversation':
        return {
          ...baseConfig,
          rate: 1.0,
          pitch: 1.0,
          enableEmotionalSynthesis: true,
          enableLipSync: true
        };

      case 'announcement':
        return {
          ...baseConfig,
          rate: 0.8,
          pitch: 0.9,
          volume: 1.2,
          enableEmotionalSynthesis: false,
          enableLipSync: false
        };

      default:
        return baseConfig;
    }
  }
}
