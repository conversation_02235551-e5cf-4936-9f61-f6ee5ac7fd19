/**
 * AI模型类型
 */
export enum AIModelType {
  /** GPT模型 */
  GPT = 'gpt',

  /** Stable Diffusion模型 */
  STABLE_DIFFUSION = 'stable-diffusion',

  /** BERT模型 */
  BERT = 'bert',

  /** RoBERTa模型 - 用于更高级的情感分析 */
  ROBERTA = 'roberta',

  /** DistilBERT模型 - 轻量级BERT变体 */
  DISTILBERT = 'distilbert',

  /** ALBERT模型 - 轻量级BERT变体 */
  ALBERT = 'albert',

  /** XLNet模型 - 用于高级文本理解 */
  XLNET = 'xlnet',

  /** BART模型 - 用于文本生成和理解 */
  BART = 'bart',

  /** T5模型 - 用于文本到文本转换 */
  T5 = 't5',

  /** 场景理解模型 - 用于理解场景描述和提取场景元素 */
  SCENE_UNDERSTANDING = 'scene_understanding',

  /** 布局生成模型 - 用于生成场景布局和空间规划 */
  LAYOUT_GENERATION = 'layout_generation',

  /** 资产匹配模型 - 用于智能匹配场景资产 */
  ASSET_MATCHING = 'asset_matching',

  /** 空间推理模型 - 用于空间关系理解和约束求解 */
  SPATIAL_REASONING = 'spatial_reasoning',

  /** 自定义模型 */
  CUSTOM = 'custom'
}
