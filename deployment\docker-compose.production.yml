# M4数字人制作系统生产环境部署配置
# Docker Compose配置文件

version: '3.8'

services:
  # 前端应用
  frontend:
    image: digital-human-frontend:latest
    container_name: dh-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
      - ./logs/nginx:/var/log/nginx
    environment:
      - NODE_ENV=production
      - API_BASE_URL=https://api.digitalhuman.com
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - dh-network

  # 后端API服务
  backend:
    image: digital-human-backend:latest
    container_name: dh-backend
    ports:
      - "3000:3000"
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=database
      - DB_PORT=3306
      - DB_NAME=digital_human_prod
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - MARKETPLACE_API_KEY=${MARKETPLACE_API_KEY}
    depends_on:
      - database
      - redis
      - minio
    restart: unless-stopped
    networks:
      - dh-network

  # AI处理服务
  ai-service:
    image: digital-human-ai:latest
    container_name: dh-ai-service
    ports:
      - "5000:5000"
    volumes:
      - ./ai-models:/app/models
      - ./logs/ai:/app/logs
    environment:
      - FLASK_ENV=production
      - MODEL_PATH=/app/models
      - GPU_ENABLED=true
      - CUDA_VISIBLE_DEVICES=0,1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 2
              capabilities: [gpu]
    restart: unless-stopped
    networks:
      - dh-network

  # 数据库
  database:
    image: mysql:8.0
    container_name: dh-database
    ports:
      - "3306:3306"
    volumes:
      - db-data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/config/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./logs/mysql:/var/log/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=digital_human_prod
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    restart: unless-stopped
    networks:
      - dh-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: dh-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf
      - ./logs/redis:/var/log/redis
    command: redis-server /etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - dh-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: dh-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
      - ./logs/minio:/var/log/minio
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
      - MINIO_BROWSER_REDIRECT_URL=https://storage.digitalhuman.com
    command: server /data --console-address ":9001"
    restart: unless-stopped
    networks:
      - dh-network

  # Nginx负载均衡器
  nginx-lb:
    image: nginx:alpine
    container_name: dh-nginx-lb
    ports:
      - "8080:80"
    volumes:
      - ./nginx/lb.conf:/etc/nginx/nginx.conf
      - ./logs/nginx-lb:/var/log/nginx
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - dh-network

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: dh-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - dh-network

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: dh-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    networks:
      - dh-network

  # 日志收集 - ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: dh-elasticsearch
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
    restart: unless-stopped
    networks:
      - dh-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: dh-logstash
    ports:
      - "5044:5044"
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./logs:/var/log/apps
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - dh-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: dh-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - dh-network

  # 消息队列 - RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: dh-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
      - ./logs/rabbitmq:/var/log/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    restart: unless-stopped
    networks:
      - dh-network

  # 任务队列处理器
  worker:
    image: digital-human-worker:latest
    container_name: dh-worker
    volumes:
      - ./logs/worker:/app/logs
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=${RABBITMQ_USER}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
    depends_on:
      - redis
      - rabbitmq
    restart: unless-stopped
    networks:
      - dh-network

  # 健康检查服务
  healthcheck:
    image: digital-human-healthcheck:latest
    container_name: dh-healthcheck
    ports:
      - "8888:8888"
    volumes:
      - ./logs/healthcheck:/app/logs
    environment:
      - CHECK_INTERVAL=30
      - ALERT_WEBHOOK=${ALERT_WEBHOOK_URL}
    depends_on:
      - backend
      - database
      - redis
    restart: unless-stopped
    networks:
      - dh-network

# 网络配置
networks:
  dh-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  db-data:
    driver: local
  redis-data:
    driver: local
  minio-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local
  rabbitmq-data:
    driver: local
